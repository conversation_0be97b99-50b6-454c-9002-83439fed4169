import { Checkbox, DatePicker, Divider, Input, Typography } from 'antd';
import moment from 'moment';
import { FC, SyntheticEvent, useState } from 'react';

import {
  DATE_VARIANTS_LIST,
  DEFAULT_DATE_VARIANT,
} from 'shared/config/constants';
import { ButtonsContainer } from 'shared/ui';
import { ColumnFilters, InnerRenderProps } from '../types';

import styles from './styles.module.scss';

const initialValues: Omit<ColumnFilters, 'column'> = {
  query: '',
  to: '',
  from: '',
  values: [],
  fromNum: '',
  toNum: '',
};

const InnerRender: FC<InnerRenderProps> = ({
  column,
  onSubmit,
  filters,
  onReset,
}) => {
  const index = filters.findIndex((item) => item.column === column.dataIndex);
  const filterType = column?.filterType || 'default';

  const [query, setQuery] = useState(
    filters[index]?.query || initialValues.query,
  );
  const [numRange, setNumRange] = useState<
    Pick<ColumnFilters, 'fromNum' | 'toNum'>
  >({
    fromNum: filters[index]?.fromNum || '',
    toNum: filters[index]?.toNum || '',
  });
  const [date, setDate] = useState<Record<'from' | 'to', string>>({
    from:
      filters.length !== 0 && filters[index]?.from
        ? moment(filters[index].from).format(DEFAULT_DATE_VARIANT)
        : initialValues.from,
    to:
      filters.length !== 0 && filters[index]?.to
        ? moment(filters[index].to).format(DEFAULT_DATE_VARIANT)
        : initialValues.to,
  });
  const [values, setValues] = useState<(boolean | Key)[]>(
    filters[index]?.values || initialValues.values,
  );

  const handleSubmit = (e: SyntheticEvent): void => {
    e.preventDefault();
    onSubmit({
      column: column.dataIndex,
      to: date.to ? moment(date.to, DEFAULT_DATE_VARIANT).toISOString() : '',
      from: date.from
        ? moment(date.from, DEFAULT_DATE_VARIANT).toISOString()
        : '',
      query,
      values,
      fromNum: numRange.fromNum,
      toNum: numRange.toNum,
    });
  };

  const resetFields = {
    date: () => setDate({ from: initialValues.from, to: initialValues.to }),
    query: () => setQuery(initialValues.query),
    numRange: () =>
      setNumRange({
        fromNum: initialValues.fromNum,
        toNum: initialValues.toNum,
      }),
    default: () => setValues(initialValues.values),
  };

  const handleReset = (e: SyntheticEvent): void => {
    e.preventDefault();
    resetFields[filterType]();
    onReset({
      column: column.dataIndex,
      to: initialValues.to,
      from: initialValues.from,
      query: initialValues.query,
      values: initialValues.values,
      fromNum: initialValues.fromNum,
      toNum: initialValues.toNum,
    });
  };

  return (
    <form
      className={styles.container}
      onSubmit={handleSubmit}
      onReset={handleReset}
    >
      {
        {
          date: (
            <DatePicker.RangePicker
              className={styles.containerAction}
              format={DATE_VARIANTS_LIST}
              placeholder={['Выберите дату', 'Выберите дату']}
              value={
                date.from !== '' && date.to !== ''
                  ? [
                      moment(date.from, DEFAULT_DATE_VARIANT),
                      moment(date.to, DEFAULT_DATE_VARIANT),
                    ]
                  : [null, null]
              }
              onChange={(_, [from, to]) =>
                setDate({
                  from,
                  to,
                })
              }
            />
          ),
          query: (
            <Input
              maxLength={4000}
              placeholder="Введите значение для фильтрации"
              className={styles.containerAction}
              value={query}
              onChange={(evt) => setQuery(evt.target.value)}
            />
          ),
          numRange: (
            <Input.Group compact className={styles.containerRange}>
              <Input
                maxLength={4000}
                prefix="от"
                placeholder="Введите числовой диапазон от"
                value={numRange.fromNum}
                type="number"
                onChange={(evt) =>
                  setNumRange((prevState) => ({
                    ...prevState,
                    fromNum: evt.target.value,
                  }))
                }
              />
              <Input
                maxLength={4000}
                prefix="до"
                placeholder="Введите числовой диапазон до"
                value={numRange.toNum}
                type="number"
                onChange={(evt) =>
                  setNumRange((prevState) => ({
                    ...prevState,
                    toNum: evt.target.value,
                  }))
                }
              />
            </Input.Group>
          ),
          default: (
            <div>
              {column.filters?.map((item) => (
                <div key={String(item.value)}>
                  <Checkbox
                    checked={values.some((value) => value === item.value)}
                    onChange={(e) =>
                      e.target.checked
                        ? setValues((prev) => [...prev, item.value])
                        : setValues(
                            values.filter((value) => value !== item.value),
                          )
                    }
                  />
                  <Typography.Text>{item.text}</Typography.Text>
                </div>
              ))}
            </div>
          ),
        }[filterType]
      }

      <Divider className={styles.divider} />

      <ButtonsContainer
        buttons={[
          {
            title: 'Ок',
            key: '1',
            type: 'primary',
            size: 'small',
            htmlType: 'submit',
            disabled:
              query === '' &&
              date.from === '' &&
              date.to === '' &&
              values.length === 0 &&
              numRange.toNum === '' &&
              numRange.fromNum === '',
          },
          {
            title: 'Очистить',
            key: '2',
            danger: true,
            ghost: true,
            size: 'small',
            htmlType: 'reset',
          },
        ]}
      />
    </form>
  );
};

export const customColumnFilters = (
  column: import('features/DataGrid').TableColumnData,
  onSubmit: (value: ColumnFilters) => void,
  filters: ColumnFilters[],
  onReset: (value: ColumnFilters) => void,
): JSX.Element => (
  <InnerRender
    column={column}
    onSubmit={onSubmit}
    filters={filters}
    onReset={onReset}
  />
);
