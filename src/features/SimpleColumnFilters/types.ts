export type FetchFilteredData = (filters: ColumnFilters[]) => void;

export type ColumnFilters = {
  column: string;
  from: string;
  fromNum: string;
  query: string;
  to: string;
  toNum: string;
  values: (boolean | Key)[];
};

export type ColumnFilterActions = {
  filters: ColumnFilters[];
  onReset: FiltersCallback;
  onSubmit: FiltersCallback;
};

export type FiltersInitial = ColumnFilters[];

export type FiltersCallback = (values: ColumnFilters) => void;

export type InnerRenderProps = {
  column: import('features/DataGrid').TableColumnData;
  filters: ColumnFilters[];
  onReset: (value: ColumnFilters) => void;
  onSubmit: (value: ColumnFilters) => void;
};

export type SpecialColumnHandler = (
  cellValue: unknown,
  filter: ColumnFilters,
) => boolean;
