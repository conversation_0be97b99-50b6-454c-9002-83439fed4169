import { Select } from 'antd';
import { FC, useState } from 'react';
import { useEffectOnce } from 'react-use';
import { InputRow } from 'shared/ui';

import type { ReportsProps } from '../..';
import { reportSelects } from '../../config';

import styles from './styles.module.scss';

export const Report: FC<ReportsProps> = ({ selectValue, onChange, url }) => {
  const selectKey = (url ? url.replaceAll('/', '') : null) as
    | keyof typeof reportSelects
    | null;

  // По умолчанию устанавливаем значение type первого элемента
  const [type, setType] = useState(
    selectKey && selectKey in reportSelects
      ? Number(reportSelects[selectKey][0].key)
      : selectValue,
  );

  useEffectOnce(() => {
    onChange(type);
  });

  /* Отмена рендера если ключ отсутствует */
  if (!selectKey || !(selectKey in reportSelects)) {
    return null;
  }

  return (
    <InputRow title="Формирование отчетов">
      <Select
        value={type}
        popupClassName={styles.select}
        onChange={(inputValue: number) => {
          onChange(inputValue);
          setType(inputValue);
        }}
      >
        {reportSelects[selectKey].map((item) => (
          <Select.Option value={Number(item.key)} key={item.key}>
            {item.title}
          </Select.Option>
        ))}
      </Select>
    </InputRow>
  );
};
