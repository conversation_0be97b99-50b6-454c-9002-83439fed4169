import { useCallback, useState } from 'react';
import { FiltersCallback, FiltersInitial } from 'features/CustomColumnFilters';

const filtersInitial: FiltersInitial = {
  nested: [],
  main: [],
};

export const useColumnFilter = (): [
  FiltersCallback,
  FiltersInitial,
  Callback,
  FiltersCallback,
] => {
  const [filters, setFilters] = useState<FiltersInitial>({
    nested: [],
    main: [],
  });

  const handleFilters = useCallback<FiltersCallback>(
    (values, type) => {
      const filtered = filters[type].filter(
        (item) => item.column !== values.column,
      );

      setFilters((prevState) => ({
        ...prevState,
        [type]: prevState[type].some((item) => item.column === values.column)
          ? [...filtered, values]
          : [...prevState[type], values],
      }));
    },
    [filters],
  );

  const handleResetColumn = useCallback<FiltersCallback>((values, type) => {
    setFilters((prevState) => ({
      ...prevState,
      [type]: prevState[type].filter((item) => item.column !== values.column),
    }));
  }, []);

  const resetFilters = useCallback(() => {
    setFilters(filtersInitial);
  }, []);

  return [handleFilters, filters, resetFilters, handleResetColumn];
};
