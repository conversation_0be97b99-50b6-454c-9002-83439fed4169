export interface TableModalProps {
  /** Колбэк с возвратом выбранных рядов */
  onSave: (
    selectedRows: import('features/DataGrid').TableRowData[],
  ) => Promise<boolean>;
  /** Дополнительные параметры для запроса */
  additionalParams?: Record<string, string | number | boolean | null>;
  /** Кастомные кнопки для таблицы */
  customButtons?: AdditionalButton[];
  /** Эндпоинт для загрузки таблицы, если не передаем данные от родителя */
  endpoint?: Endpoint;
  /** Опциональный массив чекбоксов */
  filterCheckbox?: import('features/DataGrid').DataGridProps['filterCheckbox'];
  /** Опциональный массив селекнутых ключей для фильтрации */
  hideKeys?: Key[];
  /** Опциональный массив селекнутых роу айди для фильтрации */
  hideRowIds?: string[];
  /** Опциональный массив селекнутых итемов */
  selectedRows?: import('features/DataGrid').TableRowData[];
  /** Опциональные данные для таблицы, если получение данных от родителя */
  tableData?: {
    columns: import('features/DataGrid').TableColumnData[];
    rows: import('features/DataGrid').TableRowData[];
  };
}

export type CheckboxTableProps = TableModalProps & { onClose: Callback };
