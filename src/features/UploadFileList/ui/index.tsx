import { DeleteTwoTone } from '@ant-design/icons';
import { Checkbox, Progress, Typography, Tooltip } from 'antd';
import classNames from 'classnames';
import { FC } from 'react';
import { UploadFileListProps } from 'features/UploadFileList';

import styles from './styles.module.scss';

export const UploadFileList: FC<UploadFileListProps> = ({
  fileArr,
  checkable,
  handleDeleteFile,
  uploadState,
}) => {
  const checkedFiles = fileArr.filter((item) => item.checked).length;

  const indeterminate = fileArr.length !== checkedFiles && checkedFiles !== 0;

  const getTooltipText = (condition: boolean): string | null => {
    if (condition) {
      return 'Скачивание файлов размером от 1Гб через браузер не поддерживается. Необходимо использовать менеджер скачиваний.';
    }
    return null;
  };

  return (
    <div className={styles.list}>
      {fileArr.length !== 0 && checkable && (
        <button
          className={classNames(styles.listItem, styles.listItemSolo)}
          onClick={(event) => {
            event.stopPropagation();
          }}
        >
          <Checkbox
            disabled={checkable?.isPending || false}
            indeterminate={indeterminate}
            checked={fileArr.every((file) => file.checked)}
            onChange={(event) =>
              checkable?.handleCheckAllFiles(event.target.checked)
            }
          />
          <Typography.Text className={styles.listItemSoloText}>
            Выбрать все
          </Typography.Text>
        </button>
      )}
      {fileArr.map((item) => (
        <div className={styles.content} key={item.uid}>
          <button
            className={styles.listItem}
            onClick={(event) => {
              event.stopPropagation();
            }}
          >
            {checkable && (
              <Tooltip title={getTooltipText(item.size > 1073741824)}>
                <Checkbox
                  disabled={checkable.isPending || item.size > 1073741824}
                  checked={item.checked}
                  onChange={(e) => {
                    e.stopPropagation();
                    checkable?.handleCheckFiles(item);
                  }}
                />
              </Tooltip>
            )}
            <Tooltip title={item.name}>
              <Typography.Text className={styles.truncated}>
                {item.name}
              </Typography.Text>
            </Tooltip>
            {!checkable?.isPending && (
              <DeleteTwoTone
                onClick={(event) => {
                  event.stopPropagation();
                  handleDeleteFile(item.uid);
                }}
                className={styles.titleIcon}
                twoToneColor="#FF4D4F"
              />
            )}
          </button>

          {uploadState?.fileId === item.uid && (
            <Progress
              className={styles.progress}
              percent={uploadState?.progress || 0}
            />
          )}
        </div>
      ))}
    </div>
  );
};
