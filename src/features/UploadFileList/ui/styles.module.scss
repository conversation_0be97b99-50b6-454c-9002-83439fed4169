@import 'src/app/styles/mixins';

.content {
  width: 100%;
  border-bottom: 1px solid #d9d9d9;
}

.list {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 5px;
  padding: 10px;
  height: 40vh;
  overflow: auto;
  @include scrollBar;

  &Item {
    width: 100%;
    display: flex;
    gap: 10px;
    justify-content: space-between;
    align-items: center;
    border: none;
    padding: 0;
    margin: 0;
    background: none;

    &Solo {
      justify-content: flex-start;
      gap: 10px;
      margin-bottom: 5px;

      &Text {
        color: #030303;
      }
    }
  }
}

.progress {
  width: 100% !important;
}

.truncated {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: calc(28vw);
}
