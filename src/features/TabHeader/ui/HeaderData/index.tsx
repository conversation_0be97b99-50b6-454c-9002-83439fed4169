import { EditTwoTone, LoadingOutlined, SyncOutlined } from '@ant-design/icons';
import { Tooltip, Typography } from 'antd';
import { FC, memo, useEffect, useState } from 'react';
import { HeaderDataProps } from 'features/TabHeader';
import { apiUrls, filenetServiceInstance } from 'shared/api';
import { useAxiosRequest } from 'shared/model';
import { BorderedFieldset } from 'shared/ui';

import styles from './styles.module.scss';

const DossierLink: FC<{ auditCode: string }> = ({ auditCode }) => {
  const [trigger, { isPending, error }] = useAxiosRequest<
    import('pages/FileNet').ElasticTableResponse
  >(filenetServiceInstance);

  const [dossierId, setDossierId] = useState<null | string>(null);

  const getDossierId = async (): Promise<void> => {
    const {
      model: { data },
    } = await trigger(apiUrls.fileNet.dossierAndCardsTable, {
      method: 'POST',
      data: {
        docTypeIds: [],
        unitIds: [],
        extendedSearch: { auditCode },
        isAuditCards: true,
        disablePermissionControl: true,
      },
    });

    if (data.length > 0) {
      setDossierId(String(data[0].id));
    } else {
      setDossierId('');
    }
  };

  useEffect(() => {
    getDossierId();
  }, []); // eslint-disable-line

  if (isPending) {
    return <LoadingOutlined />;
  }

  if (error || dossierId === null) {
    return (
      <Tooltip title="Произошла ошибка при получении кода проверки">
        <Typography.Text>
          Обновить <SyncOutlined onClick={getDossierId} />
        </Typography.Text>
      </Tooltip>
    );
  }

  if (dossierId === '') {
    return <Typography.Text>Ссылка на ДП отсутствует</Typography.Text>;
  }

  return (
    <Typography.Link
      href={apiUrls.fileNet.dossierCard(dossierId)}
      target="_blank"
      rel="noreferrer"
    >
      {auditCode}
    </Typography.Link>
  );
};

export const HeaderData: FC<HeaderDataProps> = memo(
  ({ headerData, editCallback, isCanEdit }) => (
    <>
      {headerData.map((item) => (
        <BorderedFieldset
          title={item.title}
          key={item.key}
          titleClassName={styles.title}
        >
          {item.data.map((content) => (
            <div className={styles.row} key={content.key}>
              <Typography.Text className={styles.rowTitle} key={content.key}>
                {content.title}:
              </Typography.Text>

              {content.isLink ? (
                <DossierLink auditCode={content.content} />
              ) : (
                <Typography.Text>{content.content ?? ''}</Typography.Text>
              )}

              {content.isEditable && isCanEdit && (
                <Tooltip title="Редактировать">
                  <EditTwoTone className={styles.icon} onClick={editCallback} />
                </Tooltip>
              )}
            </div>
          ))}
        </BorderedFieldset>
      ))}
    </>
  ),
);
