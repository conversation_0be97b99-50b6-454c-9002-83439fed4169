import { DatePicker, Drawer, Input, Select, Typography } from 'antd';
import moment from 'moment';
import type { FC, ReactNode, SyntheticEvent } from 'react';
import { useCallback, useMemo, useState } from 'react';
import {
  DATE_VARIANTS_LIST,
  DEFAULT_DATE_VARIANT,
} from 'shared/config/constants';
import { appErrorNotification } from 'shared/lib';
import { ButtonsContainer } from 'shared/ui/ButtonsContainer';

import styles from './styles.module.scss';

interface SearchDrawerProps {
  formFields: SearchFormField[];
  handleClose: Callback;
  isOpened: boolean;
  placement: 'left' | 'right' | 'top' | 'bottom';
  searchCallback: (values: { key?: Key; value?: string }) => Promise<void>;
  setValues: React.Dispatch<React.SetStateAction<Record<string, string>>>;
  values: Record<string, string>;
  handleResetSearch?: Callback;
  width?: number;
}

type FieldComponentFunction = (
  item: SearchFormField,
  values: SearchDrawerProps['values'],
  handleFormValues: (body: { name: string; value: string }) => void,
) => ReactNode;
type FieldComponentMap = Record<
  SearchFormField['type'],
  FieldComponentFunction
>;

const fieldComponentMap: FieldComponentMap = {
  input: (item, values, handleFormValues) => (
    <Input
      maxLength={4000}
      placeholder={item.placeholder}
      className={styles.formActions}
      value={values[item.name] ? values[item.name] : ''}
      onChange={(e) =>
        handleFormValues({ name: item.name, value: e.target.value })
      }
    />
  ),
  datePicker: (item, values, handleFormValues) => (
    <DatePicker
      format={DATE_VARIANTS_LIST}
      value={
        values[item.name]
          ? moment(values[item.name], DEFAULT_DATE_VARIANT)
          : null
      }
      placeholder={item.placeholder}
      className={styles.formActions}
      onChange={(_, text) => handleFormValues({ name: item.name, value: text })}
    />
  ),
  select: (item, values, handleFormValues) => (
    <Select
      value={values[item.name] ? values[item.name] : null}
      placeholder={item.placeholder}
      className={styles.formActions}
      allowClear
      onChange={(value) => {
        handleFormValues({
          name: item.name,
          value: String(value || ''),
        });
      }}
    >
      {item.selectData?.map((select) => (
        <Select.Option value={select.value} key={select.key}>
          {select.title}
        </Select.Option>
      ))}
    </Select>
  ),
  multipleSelect: (item, values, handleFormValues) => (
    <Select
      mode="multiple"
      value={values[item.name] ? values[item.name] : undefined}
      placeholder={item.placeholder}
      className={styles.formActions}
      allowClear
      onChange={(value) => {
        handleFormValues({
          name: item.name,
          value,
        });
      }}
      options={item.selectData}
    />
  ),
  rangePicker: () => null, // Пока не используется
};

export const SearchDrawer: FC<SearchDrawerProps> = ({
  handleClose,
  handleResetSearch,
  isOpened,
  formFields,
  values,
  setValues,
  searchCallback,
  placement,
  width,
}) => {
  const [isPending, setIsPending] = useState(false);

  const isEmptyForm = useMemo(
    () =>
      Object.keys(values).length === 0 ||
      Object.values(values).every((i) => i === ''),
    [values],
  );

  const handleFormValues = useCallback(
    (body: { name: string; value: string }): void =>
      setValues((prevState) => {
        const isNotEmptyValue = Array.isArray(body.value)
          ? body.value.length > 0
          : !!body.value;

        if (isNotEmptyValue) {
          return { ...prevState, [body.name]: body.value };
        }
        // Если новое зачение пустое, отфильтровываем
        return Object.fromEntries(
          Object.entries(prevState).filter(([key]) => key !== body.name),
        );
      }),
    [setValues],
  );

  const handleSubmit = (e: SyntheticEvent): void => {
    e.preventDefault();
    setIsPending(true);
    searchCallback(values)
      .catch((error) => {
        if (error !== 'Canceled') {
          appErrorNotification('Произошла ошибка поиска', error);
        }
        setIsPending(false);
      })
      .finally(() => setIsPending(false));
  };

  const handleReset = (e: SyntheticEvent): void => {
    e.preventDefault();
    setValues({});
    handleResetSearch?.();
  };

  return (
    <Drawer
      title="Настроить фильтры"
      placement={placement}
      closable
      onClose={handleClose}
      open={isOpened}
      getContainer={false}
      height="min-content"
      width={width}
    >
      <form
        className={styles.form}
        onSubmit={handleSubmit}
        onReset={handleReset}
      >
        {formFields.map((item) => (
          <div className={styles.formContent} key={item.key}>
            <Typography.Text className={styles.formText}>
              {item.title}
            </Typography.Text>
            {fieldComponentMap[item.type]?.(item, values, handleFormValues) ||
              null}
          </div>
        ))}
        <ButtonsContainer
          buttons={[
            {
              title: 'Поиск',
              key: '1',
              type: 'primary',
              htmlType: 'submit',
              loading: isPending,
              disabled: isEmptyForm,
            },
            {
              title: 'Очистить',
              key: '2',
              danger: true,
              loading: isPending,
              htmlType: 'reset',
              ghost: true,
              disabled: isEmptyForm && !handleResetSearch,
            },
          ]}
        />
      </form>
    </Drawer>
  );
};
