import { Button, Checkbox, Popover, Radio, Select, Typography } from 'antd';
import moment from 'moment';
import type { FC } from 'react';
import { useCallback, useMemo, useState } from 'react';
import { DownloadModel } from 'features/DownloadOldPopup';
import { appPopupStore } from 'entities/AppPopup';
import { apiUrls, appInstance } from 'shared/api';
import { DEFAULT_DATE_TIME_VARIANT } from 'shared/config/constants';
import {
  asyncDownloadFile,
  downloadFile,
  generateUrlWithQueryParams,
} from 'shared/lib';
import { useAppSelector } from 'shared/model';

import { AppPopup } from 'shared/ui';
import styles from './styles.module.scss';

export interface InputValuesState {
  format: number;
  highlightTrimmedLongString: boolean;
  reportType: keyof typeof reportTypes;
}

export interface PopupWithDownloadProps {
  /** Колбэк на нажатие кнопки */
  onOutputToFileClick: (values: InputValuesState) => Promise<void>;
  /** Заголовок */
  title: Title;
  /** Опциональное состояние */
  isOpened?: boolean;
  /** Опциональный колбек на закрытие */
  onClose?: Callback;
  /** Опциональный ключ для показа экспорта и типа отчета */
  showExportAndReports?: boolean;
  /** Эндпоинт таблицы */
  tableEndpoint?: string;
  /** Количество записей в таблице */
  total?: number;
}

const reportTypes = {
  xlsx: 'MS Excel',
  docx: 'MS Word',
  html: 'html',
  rtf: 'rtf',
};

const downloadMessages = {
  notice:
    'Был отправлен запрос на построение файла. Файл будет загружен после построения.',
  success: 'Файл успешно сформирован!',
  error: 'Ошибка при построении отчета',
};

export const DownloadOldPopup: FC<PopupWithDownloadProps> = ({
  title,
  showExportAndReports,
  onOutputToFileClick,
  isOpened,
  onClose,
  tableEndpoint,
  total,
}) => {
  const { reportSelect } = DownloadModel;
  /* ----------------------------------------------------
   *                      Хуки
   ---------------------------------------------------- */
  const [inputValues, setInputValues] = useState<InputValuesState>({
    reportType: 'xlsx',
    highlightTrimmedLongString: false,
    format: 1,
  });

  /* ----------------------------------------------------
   *                      Селекторы
   ---------------------------------------------------- */

  /* Селектор на состояние попапа */
  const { download } = useAppSelector(appPopupStore.allPopupsSelector);

  /* Экшены попапа */
  const { handlePopupClose } = appPopupStore.usePopupActions();

  /* ----------------------------------------------------
   *                      Колбэки
   ---------------------------------------------------- */

  /* Колбэк на закрытие */
  const handleClose = useCallback(() => {
    handlePopupClose('download');

    if (onClose) {
      onClose();
    }
  }, [handlePopupClose, onClose]);

  const getExelReport = async (): Promise<void> => {
    const res = await appInstance.get(
      generateUrlWithQueryParams(
        apiUrls.download.excelExport(tableEndpoint || ''),
        { total },
      ),
      { responseType: 'blob' },
    );
    downloadFile(
      res.data,
      `Отчет ${moment().format(DEFAULT_DATE_TIME_VARIANT)}.xlsx`,
    );
  };

  /* ----------------------------------------------------
   *                      Мемоизация
   ---------------------------------------------------- */

  const reportVariantsRadio = useMemo(
    () =>
      (Object.keys(reportTypes) as Array<keyof typeof reportTypes>).map(
        (key) => (
          <Radio value={key} key={key}>
            {reportTypes[key]}
          </Radio>
        ),
      ),
    [],
  );
  /* ----------------------------------------------------
   *                      UI
   ---------------------------------------------------- */

  return (
    <AppPopup
      isOpened={isOpened || download}
      className={styles.container}
      onClose={handleClose}
      title={title}
    >
      <div className={styles.download}>
        {showExportAndReports && (
          <div>
            <Popover
              placement="top"
              content="Выполнить поиск в базе данных по заданным условиям и вывести в файл формата XLSX"
            >
              <Button
                type="primary"
                onClick={() =>
                  asyncDownloadFile(getExelReport(), downloadMessages)
                }
              >
                Экспорт в XLSX
              </Button>
            </Popover>
          </div>
        )}

        <div className={styles.download__content}>
          {showExportAndReports && tableEndpoint && (
            <div className={styles.downloadContainer}>
              <Typography.Text className={styles.downloadContainerText}>
                Формирование отчетов:
              </Typography.Text>
              {Object.hasOwn(reportSelect, tableEndpoint?.replace('/', '')) && (
                <Select
                  value={inputValues.format}
                  popupClassName={styles.downloadDropDown}
                  onChange={(value) =>
                    setInputValues((prev) => ({
                      ...prev,
                      format: value,
                    }))
                  }
                >
                  {reportSelect[tableEndpoint?.replace('/', '')].map((item) => (
                    <Select.Option value={Number(item.key)} key={item.key}>
                      {item.title}
                    </Select.Option>
                  ))}
                </Select>
              )}
            </div>
          )}

          <div className={styles.downloadContainer}>
            <Typography.Text className={styles.downloadContainerText}>
              Подсветить строки:
            </Typography.Text>
            <Checkbox
              checked={inputValues.highlightTrimmedLongString}
              onChange={(evt) =>
                setInputValues((prev) => ({
                  ...prev,
                  highlightTrimmedLongString: evt.target.checked,
                }))
              }
            />
          </div>

          <div className={styles.downloadContainer}>
            <Typography.Text className={styles.downloadContainerText}>
              Формат отчета:
            </Typography.Text>
            <Radio.Group
              className={styles.download__radio}
              value={inputValues.reportType}
              onChange={(evt) => {
                setInputValues((prev) => ({
                  ...prev,
                  reportType: evt.target.value,
                }));
              }}
            >
              {reportVariantsRadio}
            </Radio.Group>
          </div>

          <div className={styles.download__container}>
            <Popover content="Сформировать заданную отчетную форму и вывести в файл выбранного пользователем формата">
              <Button
                type="primary"
                onClick={() =>
                  asyncDownloadFile(
                    onOutputToFileClick(inputValues),
                    downloadMessages,
                  )
                }
              >
                Вывести в файл
              </Button>
            </Popover>
          </div>
        </div>
      </div>
    </AppPopup>
  );
};
