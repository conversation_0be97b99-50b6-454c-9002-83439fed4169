import { useLayoutEffect, useState } from 'react';

export const useMarkActions = (
  array: SelectData[],
  node: TreeElement,
): [string, (selectedValue: string) => void, string] => {
  const [values, setValues] = useState<{ label: string; value: string }>({
    value: '',
    label: '',
  });

  const handleValue = (value: string): void => {
    const index = array.findIndex((item) => item.value === value);
    setValues({ value, label: array[index]?.label || '' });
  };

  useLayoutEffect(() => {
    if (node.title && typeof node.title === 'string') {
      const splitNodeTitle = node.title.match(/\[(\w*)]/)?.at(-1) || 'нет';

      const defaultIndex = array.findIndex((i) => i.title === splitNodeTitle);

      setValues({
        value: array[defaultIndex ?? 0]?.value || '',
        label: array[defaultIndex ?? 0]?.label || '',
      });
    }
  }, [node]); // eslint-disable-line

  return [values.value, handleValue, values.label];
};
