import { Input, Select, Typography } from 'antd';
import { FC } from 'react';
import { renderPopup, RenderPopupProps } from 'shared/model';
import { ButtonsContainer } from 'shared/ui';
import { MarkStore } from '..';
import styles from './styles.module.scss';

interface MarkModalProps {
  handleClose: Callback;
  markArray: SelectData[];
  onSave: (value: string) => Promise<void>;
  treeNode: TreeElement;
}

type MarkProps = Omit<MarkModalProps, 'handleClose'> & { title: Title };

const MarkModal: FC<MarkModalProps> = ({
  markArray,
  onSave,
  treeNode,
  handleClose,
}) => {
  const [value, handleValue, label] = MarkStore.hooks.useMarkActions(
    markArray,
    treeNode,
  );

  return (
    <>
      <div className={styles.select}>
        <Typography.Text className={styles.text}>
          Выберите индекс:
        </Typography.Text>
        <Input
          maxLength={4000}
          className={styles.input}
          disabled
          addonBefore={
            <Select value={value} onChange={handleValue}>
              {[...markArray]
                .sort((a, b) => a.title.localeCompare(b.title))
                .map((item) => (
                  <Select.Option value={item.value} key={item.key}>
                    {item.title}
                  </Select.Option>
                ))}
            </Select>
          }
          value={label}
        />
      </div>

      <ButtonsContainer
        buttons={[
          {
            title: 'Сохранить',
            key: 'save',
            type: 'primary',
            onClick: async () => {
              await onSave(value);
              handleClose();
            },
          },
        ]}
      />
    </>
  );
};

export const createMarkNodeTree = (props: MarkProps): void => {
  const options: RenderPopupProps = {
    className: styles.popup,
    title: props.title,
    popupUI: ({ onClose }) => <MarkModal {...props} handleClose={onClose} />,
  };

  renderPopup(options);
};
