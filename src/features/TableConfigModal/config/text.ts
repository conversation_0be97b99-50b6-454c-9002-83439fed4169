export const text = {
  title: 'Настройка отображения таблицы',
  selectorTitle: 'Сохраненные пресеты',
  selectorDisableTooltip: 'Настройки фильтров отсутствуют',

  deleteButtonTitle: 'Удалить',
  deleteTitle: 'Удаление',
  deleteTooltip: 'Удаление текущго пресет',
  deleteMessage: (value: string) =>
    `Вы действительно хотите удалить пресет "${value}"?`,

  downloadTitle: 'Загрузить',
  downloadTooltip: 'Загрузить выбранный пресет',
  downloadConfirmMessage: (value: string) =>
    `Вы действительно хотите загрузить пресет "${value}"? Все не сохраненные изменения будут утеряны!`,

  confirmTitle: 'Применить настройки',
  closeTitle: 'Закрыть',
  reset: 'Сброс',
  resetConfirmMessage: 'Вы действительно хотите сбросить настройки?',

  onSaveTitle: 'Сохранение пресета',
  onSaveMessage: (value: string) =>
    `Вы действительно хотите сохранить пресет "${value}"?`,
  onDuplicateSaveMessage: (value: string) =>
    `Пресет "${value}" уже существует, перезаписать?`,
  onDefaultProfileSave:
    'Вы действительно хотите сохранить пресет как профиль по умолчанию?',
  onDefaultProfileReSave:
    'Вы действительно хотите перезаписать профиль по умолчанию?',
  onSaveSuccessTitle: 'Сохранено',
  onSaveSuccessMessage: (value: string) => `Пресет "${value}" успешно сохранен`,

  /* Компонент формы */
  formTitle: 'Создание пресета',
  formPlaceHolder: 'Название пресета для сохранения',
  saveButtonTitle: 'Сохранить',
  saveButtonTooltip: 'Сохранить пресет',
  defaultProfileLabel: 'Профиль по умолчанию',
} as const;
