import { Checkbox, InputNumber, Select, Space } from 'antd';
import { FC, useMemo } from 'react';
// TODO: убрать после переноса датагрида в ui либу
// eslint-disable-next-line boundaries/element-types
import { DataGrid, TableColumnData } from 'features/DataGrid';
import {
  OnCheck,
  OnPositionChange,
  OnSort,
  TableConfigModalTableProps,
} from 'features/TableConfigModal';

const renderColumns = (
  onCheck: OnCheck,
  onPositionChange: OnPositionChange,
  onSort: OnSort,
): TableColumnData[] => [
  {
    title: 'Вкл',
    dataIndex: 'show',
    key: 'show',
    align: 'center',
    width: 50,
    render: (_text, row) => (
      <Checkbox
        checked={row.show as boolean}
        onChange={(event) => onCheck(event.target.checked, row)}
        disabled={row.disabled}
      />
    ),
  },
  {
    title: 'Колонка',
    dataIndex: 'name',
    key: 'name',
    align: 'left',
  },
  {
    title: 'Расположение',
    dataIndex: 'position',
    key: 'position',
    align: 'center',
    render: (_text, row) =>
      (row.show as boolean) ? (
        <InputNumber
          maxLength={4000}
          value={row.position as number}
          disabled={row.disabled}
          size="small"
          min={0}
          onStep={(newPosition) => {
            onPositionChange(newPosition, row);
          }}
        />
      ) : (
        <span />
      ),
  },
  {
    title: 'Ширина',
    dataIndex: 'width',
    key: 'width',
    align: 'center',
    render: (text) => <span>{`${text} px`}</span>,
  },
  {
    title: 'Сортировка',
    dataIndex: 'sort',
    key: 'sort',
    align: 'center',
    render: (_text, row) =>
      row.sortable ? (
        <Space>
          {row.sort !== null && (
            <Select
              size="small"
              value={row.sort}
              onChange={(value) => {
                onSort(row.columnUuid, value as SortOrder);
              }}
            >
              <Select.Option value="ASC">По возрастанию</Select.Option>
              <Select.Option value="DESC">По убыванию</Select.Option>
            </Select>
          )}
          <Checkbox
            checked={row.sort !== null}
            onChange={() => {
              onSort(row.columnUuid, row.sort !== null ? null : 'ASC');
            }}
          />
        </Space>
      ) : (
        <span />
      ),
  },
];

export const Table: FC<TableConfigModalTableProps> = ({
  rows,
  onCheck,
  onPositionChange,
  onSort,
}) => {
  const hasSortableColumns = useMemo(
    () => rows.some((row) => row.sortable),
    [rows],
  );
  const columns = useMemo(
    () =>
      renderColumns(onCheck, onPositionChange, onSort).filter(
        (col) => col.key !== 'sort' || rows.some((row) => row.sortable),
      ),
    [hasSortableColumns], // eslint-disable-line
  );

  return (
    <DataGrid
      columns={columns}
      hideColumnSearch
      hideSorter
      tableAdditionProps={{
        pagination: { position: ['bottomCenter'], hideOnSinglePage: true },
        size: 'small',
      }}
      rows={rows}
    />
  );
};
