import { DeleteOutlined, DownloadOutlined } from '@ant-design/icons';
import { Select, Tooltip } from 'antd';
import { FC, useState } from 'react';
import { SelectRowProps } from 'features/TableConfigModal/types';
import { BorderedFieldset, ButtonsContainer } from 'shared/ui';
import { text } from '../../config';

import styles from './styles.module.scss';

export const SelectRow: FC<SelectRowProps> = ({
  isPending,
  onDownload,
  onDelete,
  selectValues,
  isError,
  selectedProfile,
}) => {
  const [currentSelectValue, setCurrentSelectValue] = useState(selectedProfile);

  const isSelectDisabled = selectValues.length === 0;

  return (
    <BorderedFieldset title={text.selectorTitle}>
      <div className={styles.row}>
        <Tooltip
          title={text.selectorDisableTooltip}
          trigger={isSelectDisabled && !isPending ? ['hover'] : []}
        >
          <Select
            status={isError ? 'error' : ''}
            disabled={isSelectDisabled || isPending}
            loading={isPending}
            className="w-100"
            value={currentSelectValue}
            onChange={async (value) => {
              setCurrentSelectValue(value);
            }}
          >
            {selectValues.map((savedName) => (
              <Select.Option value={savedName} key={savedName}>
                {savedName}
              </Select.Option>
            ))}
          </Select>
        </Tooltip>

        <ButtonsContainer
          buttons={[
            {
              key: 'download',
              title: text.downloadTitle,
              type: 'primary',
              icon: <DownloadOutlined />,
              tooltip: text.downloadTooltip,
              disabled: currentSelectValue === '' || isPending,
              onClick: () => {
                onDownload(currentSelectValue);
              },
            },
            {
              key: 'delete',
              title: text.deleteButtonTitle,
              danger: true,
              icon: <DeleteOutlined />,
              disabled: currentSelectValue === '' || isPending,
              onClick: () => {
                onDelete(currentSelectValue);
                setCurrentSelectValue('');
              },
            },
          ]}
        />
      </div>
    </BorderedFieldset>
  );
};
