/** Пропсы компонента SelectRow */
export interface SelectRowProps {
  /** Флаг, указывающий, находится ли строка в процессе выполнения операции */
  isPending: boolean;
  /**
   * Функция, вызываемая при нажатии на кнопку "удалить".
   *
   * @param selectValue - Значение выбранной опции для удаления
   */
  onDelete: (selectValue: string) => void;
  /**
   * Функция, вызываемая при нажатии на кнопку "загрузить".
   *
   * @param selectValue - Значение выбранной опции для загрузки
   */
  onDownload: (selectValue: string) => void;
  /**
   * Промис, вызываемый при смене селекта.
   *
   * @param selectValue - Значение выбранной опции для загрузки
   */
  onSelectChange: (selectValue: string) => Promise<void>;
  /** Массив сохраненных значений */
  selectValues: string[];
  /** Выбранный профиль */
  selectedProfile: string;
  /** Флаг ошибки какого либо действия */
  isError?: boolean;
}

/** Пропсы компонента InputForm */
export interface InputFormProps {
  /** Флаг, указывающий, находится ли строка в процессе выполнения операции */
  isPending: boolean;
  /**
   * Функция, вызываемая при отпарвке формы.
   *
   * @param inputValue - Значение выбранной опции для загрузки
   * @param isDefaultProfile - Ключ профиля по умолчанию
   */
  onSave: (inputValue: string, isDefaultProfile: boolean) => Promise<void>;
  /** Флаг ошибки какого либо действия */
  isError?: boolean;
}

/** Значения инпутов формы */
export interface FormValues {
  /** Имя настройки */
  inputNameValue: string;
  /** Ключ профиля по умолчанию */
  isDefaultProfile: boolean;
}

export interface TableConfigModalProps {
  columns: import('features/DataGrid').TableColumnData[];
  currentSelectedProfile: import('features/DataGrid').TableConfig | null;
  endpoint: Endpoint;
  onClose: Callback;
  onProfileSet: (profile: import('features/DataGrid').TableConfig) => void;
  tableSizes: import('features/DataGrid').TableSizes;
}

export interface TableConfigModalTableProps {
  onCheck: OnCheck;
  onPositionChange: OnPositionChange;
  onSort: OnSort;
  rows: import('features/DataGrid').TableRowData[];
}

export type OnCheck = (
  isChecked: boolean,
  row: import('features/DataGrid').TableRowData,
) => void;
export type OnPositionChange = (
  newPosition: number,
  row: import('features/DataGrid').TableRowData,
) => void;
export type OnSave = (
  inputNameValue: string,
  isDefaultProfile: boolean,
) => Promise<void>;
export type OnDownload = (currentSelectValue: string) => void;
export type OnDelete = (currentSelectValue: string) => void;
export type OnSort = (columnUuid: string, sortValue: SortOrder | null) => void;
