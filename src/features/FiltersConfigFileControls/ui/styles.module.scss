@import 'src/app/styles/mixins';

.header {
  display: flex;
  flex-direction: column;
  gap: 20px;

  :global(.ant-page-header-content) {
    padding: 0;
  }

  :global(.ant-btn):not(.ant-btn-icon-only):not(.ant-input-search-button):not(.ant-btn-circle) {
    min-width: 0;
  }
}

.buttonsRow {
  display: flex;
  justify-content: space-between;
  padding-bottom: 2px;
  padding-top: 10px;
  overflow-x: auto;
  @include scrollBar;
}
