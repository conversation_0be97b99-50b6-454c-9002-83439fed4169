import { Tree } from 'antd';
import classNames from 'classnames';
import { FC } from 'react';
import { useGetViewer } from 'shared/model/hooks';
import { LazyTreeProps, TreeFileLinksProps } from '..';

import { hooks } from '../store';

import styles from './styles.module.scss';
import { TreeFileLinks } from './TreeFileLinks';

const titleRenderer = (props: TreeFileLinksProps): JSX.Element => (
  <TreeFileLinks {...props} />
);

export const LazyTree: FC<LazyTreeProps> = ({
  treeData,
  treeAdditionalProps,
  tableEndpoint,
  className,
}) => {
  const [parsedTree, onLoadData] = hooks.useTreeData(treeData, tableEndpoint);
  const getViewerLink = useGetViewer();

  return (
    <Tree
      {...treeAdditionalProps}
      treeData={parsedTree}
      loadData={onLoadData}
      className={classNames(styles.tree, className)}
      expandAction="click"
      selectable={false}
      titleRender={(node) => titleRenderer({ node, getViewerLink })}
    />
  );
};
