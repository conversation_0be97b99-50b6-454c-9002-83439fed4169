.tree {
  // Стиль для анимации колеровки текста
  :global(.ant-tree-title) span {
    transition: color 3ms linear;
  }

  // Стиль для колеровки текста когда он активен
  :global(.ant-tree-node-selected) {
    // Псевдоэлемент для вораппера, если пригодится
    &::before {
      color: inherit;
    }

    :global(.ant-tree-title) span {
      color: white !important;
    }
  }

  // Стиль для того чтобы враппер был по ширине контента
  :global(.ant-tree-node-content-wrapper) {
    position: relative;
  }
}
