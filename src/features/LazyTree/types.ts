import { TreeProps } from 'antd';
import { UseGetViewerCallback } from 'shared/model/hooks';

export interface LazyTreeProps {
  /** Эндпоинт дерева и листьев */
  tableEndpoint: Endpoint;
  /** Элементы дерева */
  treeData: TreeElement[];
  /** Дополнительные класс неймы */
  className?: string;
  /** Дополнительные пропсы дерева */
  treeAdditionalProps?: TreeProps;
}

export interface TreeFileLinksProps {
  getViewerLink: UseGetViewerCallback;
  node: TreeElement;
}
