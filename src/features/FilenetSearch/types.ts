import { enums } from './config';

export type MainSearchNames =
  | 'id'
  | 'sorting'
  | 'title'
  | 'contentPath'
  | 'verifyPeriodFrom'
  | 'verifyPeriodTo'
  | 'includeMainFile'
  | 'includeAnnex';

export type ExtendedSearchNames =
  | 'auditCode'
  | 'auditPeriodFrom'
  | 'auditPeriodTo'
  | 'contentPath'
  | 'createPeriodFrom'
  | 'createPeriodTo'
  | 'documentContent'
  | 'modifiedPeriodFrom'
  | 'modifiedPeriodTo'
  | 'title'
  | 'uploadPeriodFrom'
  | 'uploadPeriodTo'
  | 'documentContentSearchTypes';

/** Исходное состояние для поиска в Filenet */
export interface FilenetSearchInitialState {
  /** Расширенный поиск */
  extendedSearch: Record<
    Exclude<ExtendedSearchNames, 'documentContentSearchTypes'>,
    string
  > & {
    /** Типы поиска в содержимом документа */
    documentContentSearchTypes: enums.DocumentContentSearchTypes[];
  };

  /** Стейт состояния текущей формы поиска */
  isExtendedSearchOpen: boolean;

  /** Основной поиск */
  mainSearch: Record<
    Exclude<MainSearchNames, 'sorting' | 'includeMainFile' | 'includeAnnex'>,
    string
  > & {
    /** Сортировка */
    sorting: enums.Sorting | '';
  } & Record<
      Extract<MainSearchNames, 'includeMainFile' | 'includeAnnex'>,
      boolean
    >;
}

export interface SearchFormProps {
  isPending: boolean;
  onClear: Callback;
  onSubmit: Callback;
}

export interface FilenetSearchProps {
  isPending: boolean;
  onSearch: Callback;
}
