import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { FilenetSearchInitialState } from '..';
import { enums } from '../config';

const initialState: FilenetSearchInitialState = {
  extendedSearch: {
    auditCode: '',
    auditPeriodFrom: '',
    auditPeriodTo: '',
    contentPath: '',
    createPeriodFrom: '',
    createPeriodTo: '',
    documentContent: '',
    modifiedPeriodFrom: '',
    modifiedPeriodTo: '',
    title: '',
    uploadPeriodFrom: '',
    uploadPeriodTo: '',
    documentContentSearchTypes: [
      enums.DocumentContentSearchTypes.IN_DOCUMENT_NAME,
      enums.DocumentContentSearchTypes.IN_FILE_CONTENT,
      enums.DocumentContentSearchTypes.IN_FILE_NAME,
    ],
  },
  mainSearch: {
    id: '',
    sorting: '',
    title: '',
    contentPath: '',
    verifyPeriodFrom: '',
    verifyPeriodTo: '',
    includeMainFile: false,
    includeAnnex: false,
  },
  isExtendedSearchOpen: false,
};

export const slice = createSlice({
  name: 'filenetSearch',
  initialState,
  reducers: {
    setExtendedSearchValues: (
      state,
      { payload }: PayloadAction<MapObject<typeof initialState.extendedSearch>>,
    ) => {
      state.extendedSearch = payload;
    },
    setMainSearch: (
      state,
      { payload }: PayloadAction<MapObject<typeof initialState.mainSearch>>,
    ) => {
      state.mainSearch = payload;
    },
    setSearchStatus: (state, { payload }: PayloadAction<boolean>) => {
      state.isExtendedSearchOpen = payload;
    },
    clearFormData: (state) => {
      state.extendedSearch = initialState.extendedSearch;
      state.mainSearch = initialState.mainSearch;
    },
  },
});
