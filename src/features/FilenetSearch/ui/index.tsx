import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons';
import { Tooltip } from 'antd';
import classNames from 'classnames';
import { useUnmountEffect } from 'framer-motion';
import { FC, useState } from 'react';
import { FilenetSearchProps } from 'features/FilenetSearch/types';
import { useAppSelector, useCreateSliceActions } from 'shared/model';

import { reducers, selectors } from '../store';

import { ExtendedSearch } from './ExtendedSearch';
import { MainSearch } from './MainSearch';

import styles from './styles.module.scss';

export const FilenetSearch: FC<FilenetSearchProps> = ({
  onSearch,
  isPending,
}) => {
  const isExtendedSearchOpen = useAppSelector(
    selectors.isExtendedSearchOpenSelector,
  );
  const { setSearchStatus, clearFormData } = useCreateSliceActions(
    reducers.slice.actions,
  );
  const [isHovered, setIsHovered] = useState(false);

  /** Компонент формы */
  const SearchComponent = isExtendedSearchOpen ? ExtendedSearch : MainSearch;
  /** Тултип */
  const tooltip = `Открыть ${
    isExtendedSearchOpen ? 'основной' : 'расширенный'
  } поиск`;

  /** Очистка стейта при размаунте */
  useUnmountEffect(() => {
    clearFormData();
    setSearchStatus(false);
  });

  const handleMouseEnter = (): void => {
    setIsHovered(true);
  };

  const handleMouseLeave = (): void => {
    setIsHovered(false);
  };

  return (
    <div
      className={classNames(
        styles.searchContainer,
        isExtendedSearchOpen
          ? styles.searchContainer_extendedSearch
          : styles.searchContainer_mainSearch,
      )}
    >
      <SearchComponent
        onClear={clearFormData}
        onSubmit={onSearch}
        isPending={isPending}
      />

      <Tooltip color="geekblue" title={tooltip} open={isHovered}>
        <button
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          className={classNames(styles.downButton, {
            [styles.downButtonLoading]: isPending,
          })}
          onClick={
            isPending
              ? undefined
              : () => {
                  (document.getElementById('main') as HTMLDivElement).scroll(
                    0,
                    0,
                  );

                  setSearchStatus(!isExtendedSearchOpen);
                  // Решает проблему "залипания" тултипа
                  setIsHovered(false);
                }
          }
        >
          {isExtendedSearchOpen ? <CaretUpOutlined /> : <CaretDownOutlined />}
        </button>
      </Tooltip>
    </div>
  );
};
