@import 'src/app/styles/mixins';

%form {
  :global(.ant-form-item) {
    margin-bottom: 6px !important;
  }
}

%row {
  display: flex;
  align-items: center;
  gap: 10px;

  :global(.ant-row) {
    flex-flow: column nowrap;
  }

  > div {
    width: 100%;
  }
}

%input {
  width: 100% !important;
}

%buttons-container {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 10px;
}

.searchContainer {
  position: relative;
  @include defaultBorder;

  margin: 0 20px 16px;
  padding: 10px;
  background: rgb(0 0 0 / 2%);
  transition: height 500ms linear;

  &_mainSearch {
    height: 134px;
  }

  &_extendedSearch {
    height: 476px;
  }

  &InnerContainer {
    overflow: hidden;
    height: 100%;
  }
}

.downButton {
  position: absolute;
  bottom: -18px;
  left: 50%;
  @include defaultBorder;

  border-top: none;
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translate(-50%, 0%);
  z-index: 2;
  background: rgb(0 0 0 / 5%);
  cursor: pointer;
  transition: background 0.1s ease-in;

  &:hover {
    background: rgb(0 0 0 / 15%);
  }
}

.downButtonLoading {
  cursor: progress;
  opacity: 0.5;

  &:hover {
    background: rgb(0 0 0 / 5%);
  }
}
