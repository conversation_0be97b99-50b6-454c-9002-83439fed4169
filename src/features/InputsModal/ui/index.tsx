import { notification, Spin } from 'antd';
import { useEffect, useRef, useState } from 'react';
import type { FC, SyntheticEvent } from 'react';

import { renderPopup, RenderPopupProps } from 'shared/model';
import { useAxiosRequest } from 'shared/model/useAxiosRequest';

import { ButtonsContainer } from 'shared/ui/ButtonsContainer';
import { InputRow } from 'shared/ui/InputRow';
import { InputsModalProps } from '..';

import { renderInput } from '../model';

import styles from './styles.module.scss';

const InputsCreator: FC<InputsModalProps> = ({
  inputs,
  endpoint,
  additionalBodyKeys,
  bodyParser,
  onSuccessSubmit,
  responseBody,
  onSubmit,
  successMessage,
  rejectMessage,
  onClose,
}) => {
  const [trigger] = useAxiosRequest();
  const [isPending, setPending] = useState(false);
  const formRef = useRef<HTMLFormElement>(null);

  useEffect(() => {
    // После рендеринга компонента пытаемся найти первый доступный инпут
    if (formRef.current) {
      const firstInput = Array.from(formRef.current.elements).find(
        (element) => {
          const input = element as HTMLInputElement;
          return (
            input.tagName === 'INPUT' &&
            (input.type === 'text' || input.type === 'number') &&
            !input.disabled
          );
        },
      ) as HTMLInputElement | undefined;

      // Если найдено, то фокусируемся на этом инпуте
      if (firstInput) {
        setTimeout(() => {
          firstInput.focus();
        }, 0);
      }
    }
  }, []);

  const onSubmitForm = (event: SyntheticEvent<HTMLFormElement>): void => {
    event.preventDefault();

    let body = inputs.reduce((acc, input) => {
      if (input.hideOnRequest) {
        return acc;
      }
      const target = event.target as typeof event.target &
        Record<string, { value: string | number; checked?: boolean }>;

      switch (input.type) {
        case 'checkbox': {
          acc[input.key] = target[input.key].checked;
          break;
        }
        case 'text': {
          acc[input.key] = String(target[input.key].value).trim();
          break;
        }
        default:
          acc[input.key] = target[input.key].value;
      }

      return acc;
    }, {} as Record<string, unknown>);

    if (additionalBodyKeys) {
      body = { ...body, ...additionalBodyKeys };
    }

    if (bodyParser) {
      body = bodyParser(body);
    }

    if (body.isError) {
      notification.error({
        message:
          body.errorText ||
          'Введено недопустимое значение, проверьте корректность введенных данных',
      });
      return;
    }

    setPending(true);
    trigger(endpoint, {
      method: 'POST',
      data: body,
    })
      .then(async (res) => {
        if (res === false) {
          notification.error({
            message: rejectMessage || 'Ошибка при выполнении операции',
          });
          return;
        }

        /* Колбек после успешного ответа со значением из параметров */
        if (onSuccessSubmit) {
          onSuccessSubmit(body);
        }

        /* Колбек после успешного ответа со значением из ответа */
        if (responseBody) {
          responseBody(res);
        }

        /* Колбек после успешного ответа */
        await onSubmit();

        if (successMessage) {
          notification.success({
            message: successMessage,
          });
        }

        onClose();
      })
      .finally(() => setPending(false));
  };

  return (
    <Spin spinning={isPending}>
      <form
        className={styles.form}
        name="inputs-form"
        onSubmit={onSubmitForm}
        ref={formRef}
      >
        {inputs.map((input) => (
          <InputRow title={input.title} key={input.key}>
            {renderInput(input, styles.input)}
          </InputRow>
        ))}

        <ButtonsContainer
          className={styles.buttons}
          buttons={[
            {
              key: 'save',
              title: 'Сохранить',
              type: 'primary',
              htmlType: 'submit',
              disabled: isPending,
            },
            {
              key: 'cancel',
              onClick: () => onClose(),
              title: 'Отмена',
              type: 'default',
              disabled: isPending,
            },
          ]}
        />
      </form>
    </Spin>
  );
};

export const createInputsModal = (
  props: Omit<InputsModalProps, 'onClose'> & {
    hideCloseButton?: boolean;
    shouldCloseOnBackdropClick?: boolean;
  },
): void => {
  const options: RenderPopupProps = {
    popupUI: ({ onClose }) => <InputsCreator {...props} onClose={onClose} />,
    title: props.title,
    className: styles.popup,
    ...(props.hideCloseButton !== undefined && {
      hideCloseButton: props.hideCloseButton,
    }),
    ...(props.shouldCloseOnBackdropClick !== undefined && {
      shouldCloseOnBackdropClick: props.shouldCloseOnBackdropClick,
    }),
  };

  if (props.inputs.length > 0) {
    renderPopup(options);
  } else {
    throw new Error('Инпуты не могут быть пустыми');
  }
};
