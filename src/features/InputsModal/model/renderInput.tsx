import { Checkbox, Input, InputNumber } from 'antd';
import type { PopupInput } from '..';

export const renderInput = (input: PopupInput, styles: string): JSX.Element => {
  switch (input.type) {
    case 'checkbox':
      return (
        <Checkbox
          name={input.key}
          defaultChecked={input.defaultChecked ?? false}
          className={styles}
        />
      );
    case 'number':
      return (
        <InputNumber
          maxLength={4000}
          type="number"
          name={input.key}
          defaultValue={input.defaultValue ?? 0}
          className={styles}
          required={input.required}
        />
      );
    case 'text':
    default:
      return (
        <Input
          maxLength={4000}
          name={input.key}
          defaultValue={input.defaultValue ?? ''}
          className={styles}
          required={input.required}
          disabled={input.disabled}
          placeholder={input.placeholder ?? 'Введите значение'}
        />
      );
  }
};
