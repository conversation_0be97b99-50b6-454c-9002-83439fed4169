export interface RubricElement {
  closed: boolean;
  description: string;
  externalId: string;
  hasChildren: boolean;
  iconType: null;
  id: number;
  label: string;
  name: string;
  orderString: string;
  parentId: null | number;
  parentName: string;
  periodicity: string | null;
  root: boolean;
}

export interface FilenetInfoCardProps {
  handleDossierOpen: (dossierId: string) => void;
  isOpened: boolean;
  onClose: Callback;
  popupId: string;
}
export type PrivateFilenetInfoCardProps = Omit<
  FilenetInfoCardProps,
  'isOpened' | 'onClose'
> & {
  isFullSize: boolean;
};
