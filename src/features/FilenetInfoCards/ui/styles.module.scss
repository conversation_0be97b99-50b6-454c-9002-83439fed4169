@import 'src/app/styles/mixins';

.container {
  width: 70vw;
  height: 70vh;
  margin: auto;
  border-radius: 5px;
  transition: height, width 0.1s ease-in;

  &_fullSize {
    @include fullSizePopup;
  }
}

.column {
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow-y: scroll;
  padding-right: 10px;
  padding-bottom: 10px;

  &_hidden {
    display: none;
  }

  @include scrollBar;
}

.input {
  color: rgb(0 0 0 / 70%) !important;
  user-select: none !important;
  cursor: default !important;
  width: 100%;

  :global(.ant-picker-input) {
    input {
      color: rgb(0 0 0 / 70%) !important;
    }
  }
}

.row {
  display: flex;
  gap: 10px;

  &Space {
    justify-content: space-between;
  }
}

.btn {
  width: 50px;
}
