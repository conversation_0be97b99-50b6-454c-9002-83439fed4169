import { Typography } from 'antd';
import { FC } from 'react';
import { AppPopup, ButtonsContainer } from 'shared/ui';

import styles from './styles.module.scss';

interface CustomMessageModalWithOutCloseInner {
  buttons: AdditionalButton[];
  message: string;
}

type CustomMessageModalWithOutCloseProps =
  CustomMessageModalWithOutCloseInner & {
    handleClose: Callback;
    isOpened: boolean;
  };

const CustomMessageModalWithOutCloseInner: FC<
  CustomMessageModalWithOutCloseInner
> = ({ buttons, message }) => (
  <div key="customMessageModal" className={styles.content}>
    <Typography.Text>{message}</Typography.Text>
    <ButtonsContainer buttons={buttons} />
  </div>
);

export const CustomMessageModalWithOutClose: FC<
  CustomMessageModalWithOutCloseProps
> = ({ isOpened, handleClose, ...props }) => (
  <AppPopup
    isOpened={isOpened}
    onClose={handleClose}
    backdropDisabled
    className={styles.popup}
  >
    <CustomMessageModalWithOutCloseInner {...props} />
  </AppPopup>
);
