import { TreeState } from '../types';
import { deleteNode } from './deleteNode';
import { updateTotalCountOfLeafs } from './updateTotalCountOfLeafs';

// Функция для удаления нескольких узлов
export const deleteNodes = (
  nodes: Pick<TreeElement, 'itemId' | 'parent'>[],
  tree: TreeState | undefined,
): void => {
  if (!tree) return;
  const parentIds = new Set<string>();

  nodes.forEach(({ itemId, parent }) => {
    if (itemId) {
      deleteNode(itemId, tree);
      if (parent) parentIds.add(parent);
    }
  });

  // Обновление общего количества листьев для затронутых родительских узлов
  parentIds.forEach((parentId) => updateTotalCountOfLeafs(parentId, tree));
};
