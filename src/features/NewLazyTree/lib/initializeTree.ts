import { TreesState, TreeState } from '../types';

// Вспомогательная функция для инициализации дерева в состоянии
export const initializeTree = (
  state: TreesState,
  key: string,
  initialTreeNodeState: TreeState,
  isPaginationEnabled?: boolean,
): void => {
  if (!state.trees[key]) {
    state.trees[key] = {
      ...initialTreeNodeState,
      loading: true,
      isPaginationEnabled: isPaginationEnabled ?? false,
    };
  }
};
