import { TreeState } from '../types';

// Функция для получения всех идентификаторов потомков узла
export const getDescendantNodeIds = (
  tree: TreeState,
  nodeId: string,
): string[] => {
  const node = tree.entities[nodeId];
  if (!node) return [];
  let ids = [nodeId];
  if (node.childrenIds && node.childrenIds.length > 0) {
    node.childrenIds.forEach((childId) => {
      ids = ids.concat(getDescendantNodeIds(tree, childId));
    });
  }
  return ids;
};
