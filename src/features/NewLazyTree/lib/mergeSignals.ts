export function mergeSignals(
  signals: (AbortSignal | undefined)[],
): AbortSignal | undefined {
  const validSignals = signals.filter(Boolean) as AbortSignal[];

  if (validSignals.length === 0) {
    return undefined;
  }

  const combinedController = new AbortController();

  validSignals.forEach((signal) => {
    if (signal.aborted) {
      combinedController.abort();
    } else {
      signal.addEventListener('abort', () => {
        combinedController.abort();
      });
    }
  });

  return combinedController.signal;
}
