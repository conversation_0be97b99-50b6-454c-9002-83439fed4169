import { TreeState } from '../types';

/**
 * Обновляет totalCountOfLeafs у всех предков, используя разницу между
 * totalCountOfLeafs Используется в режиме пагинации когда мы знаем точную
 * разницу
 */
export const updateAncestorsTotalCountOfLeafs = (
  nodeId: string | undefined,
  diff: number,
  tree: TreeState,
): void => {
  if (!nodeId || diff === 0) return;

  const node = tree.entities[nodeId];
  if (!node) return;

  // Обновляем значение у текущего узла
  node.totalCountOfLeafs = (node.totalCountOfLeafs || 0) + diff;

  // Рекурсивно обновляем всех предков
  updateAncestorsTotalCountOfLeafs(node.parent, diff, tree);
};
