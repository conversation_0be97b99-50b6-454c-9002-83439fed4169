import { TreeState } from '../types';

export const updateTotalCountOfLeafs = (
  nodeId: string | undefined,
  tree: TreeState,
  forceUpdate = false,
): void => {
  const node = nodeId && tree.entities[nodeId];
  if (!node) return;

  // В режиме пагинации пропускаем пересчет для директорий
  // Значение должно приходить с сервера через parentInfo.totalCountOfLeafs
  if (
    tree.isPaginationEnabled &&
    !forceUpdate &&
    (node.isDirectory || node.isTable)
  ) {
    // Продолжаем обновление вверх по дереву
    updateTotalCountOfLeafs(node.parent, tree);
    return;
  }

  // В обычном режиме или при forceUpdate считаем по загруженным элементам
  node.totalCountOfLeafs = node?.childrenIds?.reduce((total, childId) => {
    const child = tree.entities[childId];
    if (!child || child.isSkeleton) {
      return total + (child?.totalCountOfLeafs || 1);
    }
    return (
      total +
      (child.isDirectory || child.isTable ? child.totalCountOfLeafs || 0 : 1)
    );
  }, 0);

  updateTotalCountOfLeafs(node.parent, tree);
};
