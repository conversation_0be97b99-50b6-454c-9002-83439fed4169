import { LoadingOutlined } from '@ant-design/icons';
import { Button, Divider, Space, Tooltip } from 'antd';
import { FC } from 'react';
import { blitzStore } from 'entities/Blitz';
import { UserInfo } from 'entities/UserInfo';
import { useAppSelector } from 'shared/model';
import { SignOutIcon } from 'shared/ui/SignOutLogo';

export const ProfileMenu: FC = () => {
  const logout = blitzStore.hooks.useBlitzLogout();

  const blitzApiSelector = useAppSelector(
    blitzStore.selectors.blitzApiSelector,
  );

  return (
    <Space>
      <UserInfo />

      <Divider type="vertical" />

      <Tooltip
        title={
          blitzApiSelector.isPending
            ? 'Идет получение адреса blitz'
            : 'Выход из системы'
        }
        placement="bottomLeft"
      >
        <Button
          icon={
            blitzApiSelector.isPending ? <LoadingOutlined /> : <SignOutIcon />
          }
          disabled={blitzApiSelector.isPending}
          onClick={logout}
        />
      </Tooltip>
    </Space>
  );
};
