import { useCallback, useMemo, useState } from 'react';
import type { TableRowData } from 'features/DataGrid';
import { recursiveFillRows } from '../../lib';

export type UseRowsFilterByCheckbox = (
  rows: TableRowData[],
) => [TableRowData[], string[], (checkboxValues: string[]) => void];

/**
 * Хук для фильтрации данных таблицы по чекбоксу, происходит проход по
 * переданным значениям в массиве и проверятся по checkboxStatus[key] в ряде
 * checkboxStatus: объект с value чекбоксов в виде ключа и boolean в виде
 * значения
 *
 * Value чекбокса должен совпадать с value ключа checkboxStatus
 *
 * @summary Фильтрация по true ключам, показывает только то что выбрано у чекбоксов
 */
export const useRowsFilterByCheckbox: UseRowsFilterByCheckbox = (rows) => {
  const [checkboxValues, setCheckboxValues] = useState<string[]>([]);

  const filteredRow = useMemo(
    () =>
      /* Если есть ключ showAll, то возвращает массив без фильтрации */
      checkboxValues.length === 0
        ? rows
        : recursiveFillRows(rows, checkboxValues),
    [checkboxValues, rows],
  );

  const setCheckboxValuesCallback = useCallback(setCheckboxValues, []); // eslint-disable-line

  /* Если есть ключ showAll то возвращает массив без фильтрации */
  return [filteredRow, checkboxValues, setCheckboxValuesCallback];
};
