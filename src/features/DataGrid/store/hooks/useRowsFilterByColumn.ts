import { FilterValue } from 'antd/lib/table/interface';
import { useEffect, useState } from 'react';
import { TableColumnData, TableRowData } from 'features/DataGrid/types';
import { getFlatRows } from 'shared/lib';

type OnFilter = (
  column: TableColumnData,
  value: boolean | React.Key,
  record: TableRowData,
) => boolean;

export type UseRowsFilterByColumn = (
  rows: TableRowData[],
  columns: TableColumnData[],
) => [
  flattenRows: TableRowData[] | null,
  setFilters: React.Dispatch<
    React.SetStateAction<Record<string, FilterValue | null>>
  >,
];

const onFilter: OnFilter = (column, value, record) => {
  const cellValue = record[column.dataIndex];
  const searchValue = value;

  // Если значение ячейки пустое, возвращаем false
  if (cellValue === undefined || cellValue === null || cellValue === '') {
    return false;
  }

  // Обработка числовых значений
  if (typeof cellValue === 'number' || typeof cellValue === 'bigint') {
    const numSearchValue = Number(searchValue);
    // Проверяем, является ли searchValue валидным числом
    if (!Number.isNaN(numSearchValue)) {
      return cellValue === numSearchValue;
    }
    return false;
  }

  // Обработка строковых значений
  if (typeof cellValue === 'string') {
    try {
      const trimmedSearch = String(searchValue).trim();
      const trimmedCell = String(cellValue).trim();
      // Экранирование специальных символов в поисковом значении
      const escapedSearch = trimmedSearch.replace(
        /[.*+?^${}()|[\]\\]/g,
        '\\$&',
      );
      return RegExp(escapedSearch, 'igm').test(trimmedCell);
    } catch {
      return false;
    }
  }

  // Для всех остальных типов
  return String(cellValue) === String(searchValue);
};

export const useRowsFilterByColumn: UseRowsFilterByColumn = (rows, columns) => {
  const [filters, setFilters] = useState<Record<string, FilterValue | null>>(
    {},
  );
  const [flattenRows, setFlattenRows] = useState<TableRowData[] | null>(null);
  const filterKeys = Object.keys(filters);

  // Нужна плоская структура для фильтрации по столбцам - APPID-1106
  useEffect(() => {
    const isFilters =
      Object.values(filters).filter((v) => v !== null).length > 0;

    if (isFilters) {
      const flatRows = getFlatRows(rows).filter((row) =>
        filterKeys
          .filter((key) => filters[key] !== null)
          .every((key) => {
            const column = columns.find((col) => col.key === key);
            return filters[key]?.some((v) => {
              if (column?.onFilter) {
                return column?.onFilter?.(v, row);
              }

              if (column) {
                return onFilter(column, String(v), row);
              }
              return false;
            });
          }),
      );
      setFlattenRows(flatRows);
    } else {
      setFlattenRows(null);
    }
  }, [rows, filters, columns]); // eslint-disable-line
  // eslint-disable-line

  return [flattenRows, setFilters];
};
