import { useCallback, useEffect, useState } from 'react';
import { useTableConfig, useTableSizes } from 'shared/model/tableConfigHooks';

import type { TableColumnData } from '../..';
import { constants } from '../../config';

export const useTableConfigStore = (
  columns: TableColumnData[],
  tableName?: string,
): [
  TableColumnData[],
  (column: TableColumnData[]) => void,
  typeof tableSizes,
  typeof changeTableSizes,
] => {
  /** Хук на массив таблиц */
  const [configuratorColumns, setConfiguratorColumns] = useState<
    TableColumnData[]
  >([]);

  /* Хук на подгрузку колонок из локал стора */
  const [currentConfig] = useTableConfig();

  /** Хук на данные в локал сторе */
  const [tableSizes, setTableSizes] = useTableSizes(tableName);

  /* Сайд на подгрузку колонок из локал стора */
  useEffect(() => {
    if (
      currentConfig &&
      currentConfig.asColumns &&
      currentConfig.asColumns.length > 0
    ) {
      const columnIndexByKeyMap = new Map<string, number>();
      columns.forEach(({ key }, index) => {
        columnIndexByKeyMap.set(key as string, index);
      });

      setConfiguratorColumns(
        currentConfig.asColumns.reduce((acc, savedColumn) => {
          if (columnIndexByKeyMap.has(savedColumn.key as string)) {
            acc.push({
              ...columns[
                columnIndexByKeyMap.get(savedColumn.key as string) as number
              ],
              width: savedColumn.width,
            });
          }

          return acc;
        }, [] as TableColumnData[]),
      );
    } else {
      setConfiguratorColumns(columns);
    }
  }, [currentConfig, columns]);

  /* Сайд на подгрузку данных в локалстор */
  useEffect(() => {
    if (tableSizes && Object.keys(tableSizes).length === 0) {
      setTableSizes(
        columns.reduce(
          (obj, column) => ({
            ...obj,
            [column.key as string]: column.width ?? constants.MIN_COLUMN_WIDTH,
          }),
          {},
        ),
      );
    }
  }, [columns, tableSizes]); // eslint-disable-line

  /* Колбэк на обновление стейта */
  const changeConfiguratorColumns = useCallback(
    (newColumns: TableColumnData[]) => {
      setConfiguratorColumns(newColumns);
    },
    [],
  );

  const changeTableSizes = useCallback((newTableSizes) => {
    setTableSizes(newTableSizes);
  }, []); // eslint-disable-line

  return [
    configuratorColumns,
    changeConfiguratorColumns,
    tableSizes,
    changeTableSizes,
  ];
};
