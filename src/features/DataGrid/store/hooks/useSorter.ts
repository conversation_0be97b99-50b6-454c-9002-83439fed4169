import { CompareFn } from 'antd/lib/table/interface';
import { useCallback } from 'react';
import { dataGridLib, TableColumnData, TableRowData } from '../..';

type UseSorter = (column: TableColumnData) => CompareFn<TableRowData>;

export const useSorter = (): UseSorter =>
  useCallback<UseSorter>(
    (tableColumn): CompareFn<TableRowData> =>
      (firstRow, secondRow, sortOrder) => {
        const {
          isEmptyOrNull,
          isNumber,
          compareNumbers,
          compareStrings,
          compareDates,
          isValidDate,
        } = dataGridLib;

        const firstValue = firstRow[tableColumn.dataIndex];
        const secondValue = secondRow[tableColumn.dataIndex];

        // Обработка пустых или null значений
        if (isEmptyOrNull(firstValue) && isEmptyOrNull(secondValue)) return 0;
        if (isEmptyOrNull(firstValue)) return sortOrder === 'ascend' ? 1 : -1;
        if (isEmptyOrNull(secondValue)) return sortOrder === 'ascend' ? -1 : 1;

        if (isValidDate(firstValue) && isValidDate(secondValue)) {
          return compareDates(firstValue, secondValue);
        }

        if (isNumber(firstValue) && isNumber(secondValue)) {
          return compareNumbers(firstValue, secondValue);
        }

        return compareStrings(firstValue, secondValue);
      },
    [],
  );
