import { useCallback, useEffect, useState } from 'react';
import { DataGridProps, TableRowData } from '../..';

type ReadOnlyKeys = readonly Key[];
type SetExpandedRows = (newExpandedRows: ReadOnlyKeys) => void;

export const useExpandable = (
  tableAdditionProps: DataGridProps['tableAdditionProps'],
  rows: TableRowData[],
): [boolean, ReadOnlyKeys, SetExpandedRows] => {
  const [isExpandable, setExpandable] = useState(false);
  const [expandedRowKeys, setExpandedRowKeys] = useState<ReadOnlyKeys>([]);

  useEffect(() => {
    setExpandable(
      Boolean(tableAdditionProps?.expandable?.rowExpandable) ||
        rows.findIndex(
          (row) =>
            'nestedTable' in row || (row?.children && row.children.length > 0),
        ) !== -1,
    );
  }, [rows, tableAdditionProps?.expandable?.rowExpandable]);

  const setExpandedRows = useCallback<SetExpandedRows>((newExpandedRows) => {
    setExpandedRowKeys(newExpandedRows);
  }, []);

  return [isExpandable, expandedRowKeys, setExpandedRows];
};
