import { useCallback, useEffect, useMemo, useState } from 'react';
import { useSessionStorage } from 'react-use';
import type { TableColumnData, TableSizes } from 'features/DataGrid';

import { constants } from '../../config';

/** Хук на ширины колонок */
export const useResizeStore = (
  presetName: string,
  columns: TableColumnData[],
): [TableSizes, (sizes: TableSizes) => void] => {
  const [memoryTableSizes, setMemoryTableSizes] = useState<TableSizes | null>(
    null,
  );
  const [, setTableSizes] = useSessionStorage<TableSizes>(presetName);

  const updateTableSizes = useCallback(
    (sizes) => {
      setMemoryTableSizes(sizes);
      setTableSizes(sizes);
    },
    [setTableSizes],
  );

  const tableSizes = useMemo<TableSizes>(() => {
    if (!memoryTableSizes) {
      const sizes = sessionStorage.getItem(presetName);
      try {
        return sizes ? JSON.parse(sizes) : {};
      } catch {
        return {};
      }
    } else {
      return memoryTableSizes;
    }
  }, [memoryTableSizes, presetName]);

  /** Запись дефолт значений в кеш если они отсутствуют */
  useEffect(() => {
    if (tableSizes && Object.keys(tableSizes).length === 0) {
      const defaultTableSizes = columns.reduce(
        (obj, column) => ({
          ...obj,
          [column.key as string]: column.width ?? constants.MIN_COLUMN_WIDTH,
        }),
        {},
      );
      setTableSizes(defaultTableSizes);
      setMemoryTableSizes(defaultTableSizes);
    }
  }, [columns, setTableSizes]); // eslint-disable-line

  return [tableSizes, updateTableSizes];
};
