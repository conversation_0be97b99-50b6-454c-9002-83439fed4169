import { ReactNode } from 'react';
import { ApiContainer } from 'shared/ui';
import type { DataGridProps, TableRowData } from '..';
import { DataGrid } from '..';

export const renderNestedTable = (
  props: Omit<DataGridProps, 'columns' | 'rows'>,
  { nestedTable, nestedTableStatus }: TableRowData,
): ReactNode => {
  if (nestedTable && 'columns' in nestedTable) {
    return (
      <ApiContainer
        error={(nestedTableStatus && nestedTableStatus.error) || false}
        isPending={(nestedTableStatus && nestedTableStatus.isLoading) || false}
      >
        <DataGrid
          {...props}
          columns={nestedTable.columns}
          rows={nestedTable.rows}
        />
      </ApiContainer>
    );
  }

  return null;
};
