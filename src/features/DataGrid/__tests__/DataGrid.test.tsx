import { render, cleanup, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import userEvent from '@testing-library/user-event';
import { wrapper } from 'shared/lib/testingUtils';
import type { TableColumnData, TableRowData } from '..';
import { DataGrid } from '../ui';

const tableClasses = {
  head: 'ant-table-thead',
  body: 'ant-table-tbody',
  switch: 'switch',
  pagination: 'pagination',
};

const columns: TableColumnData[] = [
  {
    title: 'Name',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: 'Age',
    dataIndex: 'age',
    key: 'age',
  },
  {
    title: 'Address',
    dataIndex: 'address',
    key: 'address',
  },
  {
    title: 'Tags',
    key: 'tags',
    dataIndex: 'tags',
  },
];

const rows: TableRowData[] = [
  {
    key: '1',
    name: '<PERSON>',
    tabTitle: '<PERSON>',
    address: 'New York No. 1 Lake Park',
  },
  {
    key: '2',
    name: '<PERSON>',
    tabTitle: '<PERSON>',
    address: 'London No. 1 Lake Park',
  },
  {
    key: '3',
    name: '<PERSON>',
    tabTitle: '<PERSON>',
    address: 'Sidney No. 1 Lake Park',
  },
];

describe('DataGrid UI', () => {
  afterEach(cleanup);

  it('Должен быть в DOM', () => {
    const { container } = render(<DataGrid columns={[]} rows={[]} />, {
      wrapper,
    });

    expect(container.firstChild).toBeInTheDocument();
    expect(container.firstChild).toHaveClass('container');
    expect(
      container.querySelector(`.${tableClasses.head}`),
    ).toBeInTheDocument();
    expect(
      container.querySelector(`.${tableClasses.body}`),
    ).toBeInTheDocument();
  });

  it('Должен построить таблицу', () => {
    const { container } = render(<DataGrid columns={columns} rows={rows} />, {
      wrapper,
    });

    const head = container.querySelector(`.${tableClasses.head}`) as Element;
    const body = container.querySelector(`.${tableClasses.body}`) as Element;

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    expect(head.firstChild?.childElementCount).toBe(columns.length);
    expect(body.childElementCount).toBe(rows.length);
  });

  it('Должен показывать input для поиска если передать true в searchProps.show', () => {
    const { queryByRole, rerender } = render(
      <DataGrid
        columns={columns}
        rows={rows}
        searchProps={{ label: '', show: false }}
      />,
      { wrapper },
    );

    expect(queryByRole('search')).toBeNull();

    rerender(
      <DataGrid
        columns={columns}
        rows={rows}
        searchProps={{ label: '', show: true }}
      />,
    );

    expect(queryByRole('search')).toBeInTheDocument();
  });

  it('Должен передавать label в input', () => {
    const { getByPlaceholderText } = render(
      <DataGrid
        columns={columns}
        rows={rows}
        searchProps={{ label: 'Тестовый лейбл', show: true }}
      />,
      { wrapper },
    );

    expect(getByPlaceholderText('Тестовый лейбл')).toBeInTheDocument();
  });

  it('Должен отрабатывать при печатаньи', async () => {
    const { getByPlaceholderText } = render(
      <DataGrid
        columns={columns}
        rows={rows}
        searchProps={{ label: 'Тестовый лейбл', show: true }}
      />,
      { wrapper },
    );

    const input = getByPlaceholderText('Тестовый лейбл') as HTMLInputElement;

    const user = userEvent.setup();

    expect(input.value).toBe('');

    await user.click(input);
    await user.keyboard('foo-bar');

    expect(input.value).toBe('foo-bar');
  });

  it('Должен рисовать spinner если isLoading: true', () => {
    const { container, rerender } = render(
      <DataGrid columns={columns} rows={rows} isLoading={false} />,
      { wrapper },
    );

    expect(container.querySelector('.spinner')).toBeNull();

    rerender(<DataGrid columns={columns} rows={rows} isLoading />);

    expect(container.querySelector('.spinner')).toBeInTheDocument();
  });

  it('Должен рисовать пагинацию если paginationProps.show', () => {
    const { container, rerender } = render(
      <DataGrid
        columns={columns}
        rows={rows}
        paginationProps={{
          show: false,
          currentPage: 1,
          total: 1,
          onPaginatorClick: jest.fn,
          onItemsOnPageClick: jest.fn,
        }}
      />,
      { wrapper },
    );

    expect(container.querySelector(`.${tableClasses.pagination}`)).toBeNull();

    rerender(
      <DataGrid
        columns={columns}
        rows={rows}
        paginationProps={{
          show: true,
          currentPage: 1,
          total: 1,
          onPaginatorClick: jest.fn,
          onShowSizeChange: jest.fn,
        }}
      />,
    );
    // expect(
    //   container.querySelector(`.${tableClasses.pagination}`),
    // ).toBeInTheDocument();
  });

  it('Должен отрабатывать клики по ряду', async () => {
    const onRowClick = jest.fn();

    const { getByText } = render(
      <DataGrid
        columns={columns}
        rows={rows}
        tableAdditionHandlers={{ onRowClick }}
      />,
      { wrapper },
    );

    const user = userEvent.setup();

    await waitFor(() => user.click(getByText(rows[0].name as string)));

    await new Promise((resolve) => {
      setTimeout(resolve, 500);
    });

    expect(onRowClick).toBeCalledWith(rows[0], false);

    expect(onRowClick).toBeCalledTimes(1);
  });

  it('Должен отрабатывать дабл клик по ряду', async () => {
    const onRowClick = jest.fn();

    const { getByText } = render(
      <DataGrid
        columns={columns}
        rows={rows}
        tableAdditionHandlers={{ onRowClick }}
      />,
      { wrapper },
    );

    const user = userEvent.setup();

    await waitFor(() => user.dblClick(getByText(rows[0].name as string)));

    await new Promise((resolve) => {
      setTimeout(resolve, 500);
    });

    expect(onRowClick).toBeCalledWith(rows[0], true);

    expect(onRowClick).toBeCalledTimes(1);
  });
});
