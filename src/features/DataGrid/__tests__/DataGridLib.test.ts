import { checkIsActual } from '../lib';

describe('checkIsActual', () => {
  it('Должно вернуть true', () => {
    expect(checkIsActual('(01.01.2010-05.05.2030)')).toBeTruthy();
    expect(checkIsActual('(01.01.2010-05.05.2099)')).toBeTruthy();
  });

  it('Должно вернуть false', () => {
    expect(checkIsActual('(01.01.2010-05.05.2020)')).toBeFalsy();
    expect(checkIsActual('(01.01.2010-05.05.1900)')).toBeFalsy();
  });

  it('Должно вернуть false в случае если нет даты', () => {
    expect(checkIsActual('')).toBeFalsy();
    expect(checkIsActual('Alex is not tripleIncheasin')).toBeFalsy();
  });
});
