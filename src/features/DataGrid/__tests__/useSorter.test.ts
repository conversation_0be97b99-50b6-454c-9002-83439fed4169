import { renderHook } from '@testing-library/react-hooks';
import { useSorter } from '../store/hooks';

describe('useSorter', () => {
  it('должен правильно сортировать числа', () => {
    const { result } = renderHook(() => useSorter());

    const sorter = result.current({ dataIndex: 'value' });

    const data = [
      { value: '11', key: 0 },
      { value: '1001', key: 1 },
      { value: '10', key: 2 },
      { value: '2', key: 3 },
      { value: '5', key: 4 },
    ];

    const sortedData = data.sort((a, b) => sorter(a, b, 'ascend'));

    expect(sortedData).toEqual([
      { value: '2', key: 3 },
      { value: '5', key: 4 },
      { value: '10', key: 2 },
      { value: '11', key: 0 },
      { value: '1001', key: 1 },
    ]);
  });

  it('должен правильно сортировать строки', () => {
    const { result } = renderHook(() => useSorter());

    const sorter = result.current({ dataIndex: 'value' });

    const data = [
      { value: 'Ясненское отделение № 1600', key: 0 },
      { value: 'Городское отделение № 70', key: 1 },
      { value: 'Ярцевское отделение № 1612', key: 2 },
      { value: 'Городское отделение № 69', key: 3 },
      { value: 'Автозаводское отделение № 1000', key: 4 },
      { value: 'Верхнекамское отделение № 4400', key: 5 },
      { value: 'Верхнекамское отделение № 4399', key: 6 },
    ];

    const sortedData = data.sort((a, b) => sorter(a, b, 'ascend'));

    expect(sortedData).toEqual([
      { value: 'Автозаводское отделение № 1000', key: 4 },
      { value: 'Верхнекамское отделение № 4399', key: 6 },
      { value: 'Верхнекамское отделение № 4400', key: 5 },
      { value: 'Городское отделение № 69', key: 3 },
      { value: 'Городское отделение № 70', key: 1 },
      { value: 'Ярцевское отделение № 1612', key: 2 },
      { value: 'Ясненское отделение № 1600', key: 0 },
    ]);
  });

  it('должен правильно сортировать даты', () => {
    const { result } = renderHook(() => useSorter());

    const sorter = result.current({ dataIndex: 'value' });

    const data = [
      { value: '10.05.2023', key: 0 },
      { value: '01.01.2022', key: 1 },
      { value: '01.01.2022 19:30:31', key: 2 },
      { value: '01.01.2022 19:30', key: 3 },
      { value: '15.12.2022', key: 4 },
      { value: '20.03.2023 10:30', key: 5 },
      { value: '05.07.2022 15:45:30', key: 6 },
      { value: '25-08-2023', key: 7 },
    ];

    const sortedData = data.sort((a, b) => sorter(a, b, 'ascend'));

    expect(sortedData).toEqual([
      { value: '01.01.2022', key: 1 },
      { value: '01.01.2022 19:30', key: 3 },
      { value: '01.01.2022 19:30:31', key: 2 },
      { value: '05.07.2022 15:45:30', key: 6 },
      { value: '15.12.2022', key: 4 },
      { value: '20.03.2023 10:30', key: 5 },
      { value: '10.05.2023', key: 0 },
      { value: '25-08-2023', key: 7 },
    ]);
  });
});
