import { Pagination } from 'antd';
import classNames from 'classnames';
import React, { FC } from 'react';
import { DataGridProps } from '../..';
import { constants } from '../../config';
import { renderPaginationTotal } from '../../lib';

import styles from './styles.module.scss';

const TableFooter: FC<
  Pick<DataGridProps, 'paginationProps'> & {
    className?: string;
  }
> = ({ className, paginationProps, children }) => {
  if (!paginationProps?.show && !React.isValidElement(children)) {
    return null;
  }

  return (
    <div className={classNames(styles.footer, className)}>
      {paginationProps?.show && (
        <Pagination
          {...paginationProps}
          key="pagination"
          size="small"
          className={classNames(styles.pagination, paginationProps.className)}
          total={paginationProps.total}
          current={paginationProps.currentPage}
          onChange={paginationProps.onPaginatorClick}
          onShowSizeChange={paginationProps.onItemsOnPageClick}
          pageSizeOptions={
            paginationProps.pageSizeOptions || constants.PAGE_SIZE_OPTIONS
          }
          showTotal={
            paginationProps.hideTotalLabel ? undefined : renderPaginationTotal
          }
        />
      )}
      {children}
    </div>
  );
};

export default TableFooter;
