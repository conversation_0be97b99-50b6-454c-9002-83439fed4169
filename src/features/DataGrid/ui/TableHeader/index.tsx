import { Checkbox, Tooltip, Typography } from 'antd';
import { FC } from 'react';
import type { DataGridProps } from 'features/DataGrid';
import { ButtonsContainer } from 'shared/ui';

import styles from './styles.module.scss';

export const TableHeader: FC<
  Pick<DataGridProps, 'additionalButtons' | 'filterCheckbox'> & {
    checkboxValues: string[];
    handleCheckboxValues: (checkboxValues: string[]) => void;
    additionalClassName?: string;
    className?: string;
  }
> = ({
  additionalButtons = [],
  filterCheckbox = { checkboxes: [] },
  additionalClassName,
  handleCheckboxValues,
  checkboxValues,
  className,
}) => {
  if (
    Array.isArray(additionalButtons) &&
    additionalButtons.length === 0 &&
    filterCheckbox &&
    filterCheckbox.checkboxes.length === 0
  ) {
    return null;
  }

  return (
    <div className={className}>
      {Array.isArray(additionalButtons) && additionalButtons.length > 0 && (
        <ButtonsContainer
          buttons={additionalButtons}
          className={additionalClassName}
          disableAnimation
        />
      )}

      {filterCheckbox && filterCheckbox.checkboxes.length > 0 && (
        <div className={styles.checkboxGroup}>
          {filterCheckbox.title && (
            <Typography.Text>{filterCheckbox.title}:</Typography.Text>
          )}

          <Checkbox.Group
            className={styles.checkboxGroup}
            onChange={(checkedValue) => {
              handleCheckboxValues(checkedValue as string[]);
            }}
            value={checkboxValues}
          >
            {filterCheckbox?.checkboxes &&
              Array.isArray(filterCheckbox?.checkboxes) &&
              filterCheckbox?.checkboxes.map((checkbox) => (
                <Tooltip
                  title={checkbox?.hint}
                  key={checkbox.value}
                  trigger={checkbox?.hint !== '' ? 'hover' : []}
                >
                  <Checkbox value={checkbox.value}>{checkbox.label}</Checkbox>
                </Tooltip>
              ))}
          </Checkbox.Group>
        </div>
      )}
    </div>
  );
};
