@import 'src/app/styles/mixins';

.container {
  position: relative;
  height: max-content;
  width: 100%;

  &Resize {
    cursor: ew-resize !important;

    &::after {
      position: absolute;
      content: '';
      inset: 0;
      z-index: 29;
    }

    :global(.ant-table-column-has-sorters) {
      cursor: ew-resize !important;
    }
  }

  :global(.ant-spin) {
    z-index: 3 !important;
    max-height: none !important;
  }

  :global(.ant-table-container) {
    &::before {
      z-index: 3 !important;
    }
  }

  :global(.ant-table-row-cell-break-word) {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  :global(.ant-pagination-item) {
    @include defaultBorder;
  }

  :global(.ant-pagination-item-active) {
    border-color: #109bdf !important;
  }

  // Mozilla fix
  :global(.ant-table-body) {
    scrollbar-width: thin;
    cursor: pointer;
  }

  :global(.ant-table-body::-webkit-scrollbar) {
    height: 10px;
    width: 10px;
    cursor: pointer;
  }

  :global(.ant-table-body::-webkit-scrollbar-thumb) {
    background-color: rgb(80 80 80 / 50%);
    border-radius: 4px;
    @include defaultBorder;
  }

  :global(tr.ant-table-expanded-row) {
    background: none !important;
  }

  :global(.ant-table-row-expand-icon-collapsed) {
    color: #36b4eb;
    border: 1px solid #36b4eb;
    background: rgb(54 180 235 / 3%);
  }

  :global(.ant-table-row-expand-icon-expanded) {
    color: #ff7d7d;
    border: 1px solid #ff7d7d;
    background: rgb(255 125 125 / 7%);
  }
}

.search {
  flex-direction: column-reverse;
  gap: 10px;
  display: flex;
  padding: 5px;
  align-items: center;
  margin-bottom: 5px;
}

.header {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 10px;
  align-items: center;
  width: 100%;
  margin-bottom: 6px;
}

.spinner {
  margin: 15% 0 0;
}

.grid {
  &Tooltip {
    max-width: 300px !important;
    overflow-y: scroll;
    max-height: 50vh;
    scrollbar-width: none;

    &::-webkit-scrollbar {
      width: 1px;
      height: 1px;
    }
  }

  &Table {
    @include scrollBar;

    &Checkbox {
      margin-right: 35%;
    }

    :global(.ant-pagination.ant-table-pagination) {
      display: flex;
      gap: 10px;
    }
  }
}

.resizeDivider {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 30px;
  width: 3px;
  background: rgb(0 0 0 / 30%);
  border-radius: 2px;
  z-index: 30;
}
