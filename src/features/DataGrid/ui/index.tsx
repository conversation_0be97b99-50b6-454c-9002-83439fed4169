import { SearchOutlined } from '@ant-design/icons';
import { InputRef, Table, Tag, Tooltip } from 'antd';
import classNames from 'classnames';
import type { FC, MutableRefObject, ReactElement } from 'react';
import { lazy, Suspense, useEffect, useMemo, useRef } from 'react';

import { AppSpinner } from 'shared/ui/AppSpinner';
import { ButtonsContainer } from 'shared/ui/ButtonsContainer';
import { dataGridStore, TableColumnData } from '..';

import type { TableRowData, DataGridProps } from '..';
import { renderNestedTable } from '../lib';
import { FilterDropdown } from './FilterDropdown';

import { ResizableTableTitle } from './ResizableTableTitle';
import { SearchInput } from './SearchInput';

import styles from './styles.module.scss';
import { TableHeader } from './TableHeader';
import { TitleWithLazySort } from './TitleWithLazySort';

const TableFooter = lazy(() => import('./TableFooter'));

const filterIcon = (filtered: boolean): JSX.Element => (
  <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
);

const renderTitle = (
  column: TableColumnData,
  onSort: TableColumnData['onLazyLoadSort'],
): ReactElement => {
  if (onSort) {
    return (
      <TitleWithLazySort
        title={column.title}
        onSort={onSort}
        currentSort={column.lazySortOrder}
      />
    );
  }

  return (
    <Tooltip title={column.title}>
      <span>{column.title}</span>
    </Tooltip>
  );
};

export const DataGrid: FC<DataGridProps> = ({
  columnsProps,
  tableAdditionHandlers,
  isLoading,
  columns,
  rows,
  tableAdditionProps = {},
  secondRowAdditionalButtons,
  paginationProps,
  searchProps,
  resizableProps,
  additionalComponent,
  filterCheckbox,
  additionalButtons,
  elementsRef,
  additionalClassNames,
  hideColumnSearch = false,
  hideSorter = false,
  footerAdditionalComponent,
  nestedTableProps,
  nestedTableLoader,
  rowSelectionProps,
  showOnlyFounded = true,
  setFilteredRows,
}) => {
  /* ----------------------------------------------------
   *                      Константы
   ---------------------------------------------------- */

  // TODO вынести в хедер по второму ряду тоже
  // Активен ли второй ряд
  const isHeaderSecondRowActive = Boolean(
    (Array.isArray(secondRowAdditionalButtons) &&
      secondRowAdditionalButtons.length > 0) ||
      additionalComponent,
  );

  /* ----------------------------------------------------
 *                      Рефы
 ---------------------------------------------------- */

  const localHeaderRef = useRef<HTMLDivElement>(null);
  const headerRef = (elementsRef?.header ||
    localHeaderRef) as MutableRefObject<HTMLDivElement>;

  const localTableRef = useRef<HTMLDivElement>(null);
  const tableRef = (elementsRef?.table ||
    localTableRef) as MutableRefObject<HTMLDivElement>;

  const searchInput = useRef<InputRef>(null);

  /* ----------------------------------------------------
   *                      Хуки
   ---------------------------------------------------- */
  const renderFilterDropdown =
    dataGridStore.hooks.useFilterDropdown(FilterDropdown);
  const [flattenRows, setFilters] = dataGridStore.hooks.useRowsFilterByColumn(
    rows,
    columns,
  );
  const handleOnRowClick = dataGridStore.hooks.useOnRowEvent(
    tableAdditionHandlers,
  );
  const handleSort = dataGridStore.hooks.useSorter();
  const [isExpandable, expandedKeys, setExpandedRows] =
    dataGridStore.hooks.useExpandable(tableAdditionProps, flattenRows || rows);
  const [
    resizableColumns,
    onHeaderCell,
    isResizeActive,
    onResizeMove,
    dividerLeftPosition,
  ] = dataGridStore.hooks.useResizable(resizableProps, headerRef, columns);
  const [
    searchFilteredRows,
    allFoundedKeys,
    handleFilter,
    isOnlyFounded,
    handleOnlyFounded,
  ] = dataGridStore.hooks.useTableSearch(
    flattenRows || rows,
    columns,
    searchProps?.onInput,
    tableAdditionProps?.pagination
      ? (tableAdditionProps.pagination.onChange as (page: number) => void)
      : () => {}, // eslint-disable-line
  );
  const [checkboxFilteredRows, checkboxValues, handleCheckboxValues] =
    dataGridStore.hooks.useRowsFilterByCheckbox(searchFilteredRows);

  const prevFoundedKeysRef = useRef<Set<Key>>(new Set());
  useEffect(() => {
    // Фильтруем ключи с предыдущего рендера, чтобы не накапливать раскрытые строки
    setExpandedRows([
      ...new Set([
        ...expandedKeys.filter((k) => !prevFoundedKeysRef.current.has(k)),
        ...allFoundedKeys,
      ]),
    ]);
    prevFoundedKeysRef.current = new Set([...allFoundedKeys]);
  }, [allFoundedKeys]); // eslint-disable-line

  /* ----------------------------------------------------
   *                     Конфигурация
   ---------------------------------------------------- */

  if (resizableProps?.isActive) {
    tableAdditionProps.components = {};
    tableAdditionProps.components.header = {};
    tableAdditionProps.components.header.cell = ResizableTableTitle;
  }
  /*
   * Если передаются чекбоксы, то ряды после поиска фильтруются по
   * чекбоксу фильтры
   */
  const filteredRows = useMemo(
    () => (filterCheckbox ? checkboxFilteredRows : searchFilteredRows),
    [checkboxFilteredRows, filterCheckbox, searchFilteredRows],
  );

  useEffect(() => {
    setFilteredRows?.(filteredRows);
  }, [filteredRows, setFilteredRows]);

  const rowSelection = useMemo(
    () =>
      tableAdditionProps?.rowSelection
        ? {
            preserveSelectedRowKeys: true,
            ...tableAdditionProps.rowSelection,
            ...(rowSelectionProps?.selectAll
              ? {
                  selections: Array.isArray(
                    tableAdditionProps.rowSelection.selections,
                  )
                    ? [
                        {
                          key: 'all',
                          text: 'Выбрать все данные',
                          onSelect: () => {
                            rowSelectionProps.selectAll(filteredRows);
                          },
                        },
                        ...tableAdditionProps.rowSelection.selections,
                      ]
                    : [],
                }
              : {}),
          }
        : undefined,
    [filteredRows, rowSelectionProps, tableAdditionProps.rowSelection],
  );

  // Эффект для загрузки ленивых вложенных таблиц при раскрытии
  useEffect(() => {
    if (nestedTableLoader && expandedKeys.length > 0) {
      const expandedKeysSet = new Set(expandedKeys);

      // Перебор строк для поиска раскрытых, которые требуют загрузки
      filteredRows.forEach((record: TableRowData) => {
        if (
          expandedKeysSet.has(record.key) &&
          !record?.nestedTableStatus?.isLoading &&
          !record?.nestedTableStatus?.isLoaded
        ) {
          // Загружаем данные вложенной таблицы для этой записи
          nestedTableLoader(record);
        }
      });
    }
  }, [expandedKeys]); // eslint-disable-line

  /* ----------------------------------------------------
   *                      UI
   ---------------------------------------------------- */
  return (
    <div
      className={classNames(
        styles.container,
        additionalClassNames?.container,
        isResizeActive && styles.containerResize,
      )}
      ref={headerRef}
      key="table"
      onMouseMove={(event) => {
        if (isResizeActive) {
          onResizeMove(event);
        }
      }}
    >
      <SearchInput
        handleFilter={handleFilter}
        searchProps={searchProps}
        isExpandable={isExpandable}
        isOnlyFounded={isOnlyFounded}
        showOnlyFounded={showOnlyFounded}
        handleOnlyFounded={handleOnlyFounded}
      />

      <TableHeader
        className={styles.header}
        additionalButtons={additionalButtons}
        additionalClassName={additionalClassNames?.footer}
        checkboxValues={checkboxValues}
        filterCheckbox={filterCheckbox}
        handleCheckboxValues={(checkedValue) => {
          handleCheckboxValues(checkedValue as string[]);
        }}
      />

      {isHeaderSecondRowActive && (
        <div className={styles.header}>
          {Array.isArray(secondRowAdditionalButtons) &&
            secondRowAdditionalButtons.length > 0 && (
              <ButtonsContainer buttons={secondRowAdditionalButtons} />
            )}

          {/* Дополнительный компонент */}
          {additionalComponent || null}
        </div>
      )}

      <AppSpinner
        isPending={Boolean(isLoading)}
        className={classNames(styles.spinner, additionalClassNames?.spinner)}
      >
        <Table
          onChange={(_, filters) => setFilters(filters)}
          ref={tableRef}
          className={classNames(
            styles.gridTable,
            additionalClassNames?.table,
            additionalClassNames?.cursor,
          )}
          dataSource={filteredRows}
          pagination={false}
          onRow={handleOnRowClick}
          bordered={Boolean(resizableProps?.isActive)}
          {...tableAdditionProps}
          expandable={{
            expandedRowKeys: expandedKeys,
            onExpandedRowsChange: setExpandedRows,
            rowExpandable: (row) => {
              if (tableAdditionProps?.expandable?.rowExpandable) {
                return tableAdditionProps.expandable.rowExpandable(row);
              }

              return Boolean(
                'nestedTable' in row ||
                  tableAdditionProps?.expandable?.expandedRowRender,
              );
            },
            ...(nestedTableProps && {
              expandedRowRender: (record) => {
                let props;

                if (typeof nestedTableProps === 'function') {
                  props = nestedTableProps(record);
                } else {
                  props = nestedTableProps;
                }

                return renderNestedTable(props, record);
              },
            }),
            showExpandColumn: isExpandable,
            ...tableAdditionProps?.expandable,
          }}
          rowSelection={rowSelection}
          showSorterTooltip={{
            placement: 'bottom',
            title: 'Нажмите, чтобы изменить сортировку',
          }}
        >
          {resizableColumns.map((currentColumn, index) => {
            const column: TableColumnData = {
              ...currentColumn,
              /** Проброс пропсов по колонке */
              ...(() => {
                if (columnsProps) {
                  if (typeof columnsProps === 'function') {
                    return columnsProps(currentColumn);
                  }
                  return columnsProps;
                }

                return {};
              })(),
            };

            const isShowColumnSearch =
              !(column.hideColumnSearch || hideColumnSearch) && !column.filters;

            return (
              <Table.Column
                {...column}
                {...(isShowColumnSearch && {
                  filterDropdown:
                    column.filterDropdown ||
                    renderFilterDropdown(
                      column.dataIndex,
                      searchInput,
                      column.title,
                    ),
                  filterIcon: column.filterIcon || filterIcon,
                  onFilterDropdownOpenChange: (visible) => {
                    if (column.onFilterDropdownOpenChange) {
                      column.onFilterDropdownOpenChange(visible);
                    } else if (visible && searchInput) {
                      setTimeout(() => searchInput.current?.select(), 100);
                    }
                  },
                })}
                {...((column.hideSorter ? !column.hideSorter : !hideSorter) && {
                  sorter: column.sorter || handleSort(column),
                })}
                filters={column.filters}
                title={(() => {
                  if (column.renderTitle) {
                    return column.renderTitle(column.title as string);
                  }

                  return renderTitle(column, column.onLazyLoadSort);
                })()}
                key={column.key}
                dataIndex={column.dataIndex}
                width={column.width}
                render={(text, row) =>
                  column.render ? (
                    column.render(text, row as TableRowData)
                  ) : (
                    <Tooltip
                      placement="topLeft"
                      title={
                        row?.hint?.[column.dataIndex] ||
                        Object.hasOwn(column, 'ellipsis')
                          ? column.ellipsis
                            ? text
                            : null
                          : text
                      }
                      overlayClassName={styles.gridTooltip}
                    >
                      {Object.hasOwn(row, 'highlights') &&
                      row?.highlights?.[column.dataIndex] ? (
                        <Tag
                          color={
                            row?.highlights?.[column.dataIndex] || 'default'
                          }
                        >
                          {text}
                        </Tag>
                      ) : (
                        text
                      )}
                    </Tooltip>
                  )
                }
                /** В дефолте делает текст в элипсис с убраным дефолтным титулом */
                ellipsis={
                  Object.hasOwn(column, 'ellipsis')
                    ? column.ellipsis
                    : { showTitle: false }
                }
                align={column.align}
                {...(resizableProps?.isActive && {
                  onHeaderCell: onHeaderCell(index),
                })}
              />
            );
          })}
        </Table>
      </AppSpinner>

      <Suspense fallback={null}>
        <TableFooter
          className={additionalClassNames?.footer}
          paginationProps={paginationProps}
        >
          {footerAdditionalComponent}
        </TableFooter>
      </Suspense>

      {isResizeActive && (
        <div
          className={styles.resizeDivider}
          style={{ left: dividerLeftPosition }}
        />
      )}
    </div>
  );
};
