import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons';
import { Tooltip } from 'antd';
import classNames from 'classnames';
import type { FC } from 'react';
import type { TableColumnData } from '../..';

import styles from './styles.module.scss';

export const handleSortOrder = (
  currentSortOrder: TableColumnData['lazySortOrder'],
): TableColumnData['lazySortOrder'] => {
  switch (currentSortOrder) {
    case 'ASC':
      return 'DESC';
    case 'DESC':
      return undefined;
    default:
      return 'ASC';
  }
};

export const TitleWithLazySort: FC<{
  currentSort: TableColumnData['lazySortOrder'];
  onSort: (newSortOrder: TableColumnData['lazySortOrder']) => void;
  title: TableColumnData['title'];
}> = ({ title, currentSort, onSort }) => (
  <div
    className={styles.sortTitle}
    role="button"
    onClick={() => {
      onSort(handleSortOrder(currentSort));
    }}
    onKeyDown={(event) => {
      if (event.key === 'Space' || event.key === 'Enter') {
        onSort(handleSortOrder(currentSort));
      }
    }}
    tabIndex={0}
  >
    <div className={styles.sortCarets}>
      <CaretUpOutlined
        className={classNames(
          styles.sortCaret,
          currentSort === 'ASC' && styles.sortCaret_active,
          currentSort === 'DESC' && styles.sortCaret_hidden,
        )}
      />
      <CaretDownOutlined
        className={classNames(
          styles.sortCaret,
          currentSort === 'DESC' && styles.sortCaret_active,
          currentSort === 'ASC' && styles.sortCaret_hidden,
        )}
      />
    </div>
    <Tooltip placement="topLeft" title={title}>
      <span>{title}</span>
    </Tooltip>
  </div>
);
