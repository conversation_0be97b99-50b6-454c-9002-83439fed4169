import { SearchOutlined } from '@ant-design/icons';
import { Checkbox, Input } from 'antd';
import { FC, useState } from 'react';
import { dataGridConfig } from '../..';
import type { DataGridProps } from '../..';

import styles from './styles.module.scss';

export const SearchInput: FC<{
  handleFilter: (filterQuery: string) => void;
  handleOnlyFounded: (value: boolean) => void;
  searchProps: DataGridProps['searchProps'];
  isExpandable?: boolean;
  isOnlyFounded?: boolean;
  showOnlyFounded?: boolean;
}> = ({
  searchProps,
  handleFilter,
  isExpandable,
  showOnlyFounded = true,
  handleOnlyFounded,
  isOnlyFounded,
}) => {
  const [inputValue, setInputValue] = useState('');

  if (searchProps === undefined || !searchProps?.show) {
    return null;
  }

  return (
    <div className={styles.control}>
      <Input
        maxLength={4000}
        role="search"
        prefix={<SearchOutlined />}
        className={styles.input}
        placeholder={
          searchProps?.label ||
          dataGridConfig.constants.DEFAULT_SEARCH_INPUT_PLACEHOLDER
        }
        addonAfter={
          isExpandable && showOnlyFounded ? (
            <Checkbox
              disabled={inputValue.trim().length === 0}
              checked={isOnlyFounded}
              onChange={(event) => handleOnlyFounded(event.target.checked)}
            >
              Показать только найденное
            </Checkbox>
          ) : null
        }
        onChange={(event) => {
          const { value } = event.target;

          setInputValue(value);
          handleFilter(value);
        }}
      />
    </div>
  );
};
