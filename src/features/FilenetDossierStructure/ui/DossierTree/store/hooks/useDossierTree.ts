import { Key, useCallback } from 'react';
import {
  reducers,
  selectors,
  thunks,
} from 'features/FilenetDossierStructure/store';
import { ParsedDossierStructureTree } from 'entities/FilenetAttachmentsTree';
import { appErrorNotification, useExpandTreeActions } from 'shared/lib';
import {
  useAppDispatch,
  useAppSelector,
  useCreateSliceActions,
} from 'shared/model';

export type UseDossierTree = {
  checkedKeys: Key[];
  dossierData: ParsedDossierStructureTree[];
  error: AppError;
  expandedKeys: Key[];
  isDocumentGenerating: boolean;
  isPending: boolean;
  loadedKeys: Key[];
  onCheck: (
    checkedTreeKeys: Key[] | { checked: Key[]; halfChecked: Key[] },
  ) => void;
  onExpand: (keys: Key[]) => void;
  onLoadData: ({
    key,
    numericKey,
    loaded,
    isDirectory,
  }: {
    key: Key;
    numericKey: Key;
    isDirectory?: boolean;
    loaded?: boolean;
  }) => Promise<void>;
};

export const useDossierTree = ({
  cardId,
}: {
  cardId: string;
}): UseDossierTree => {
  const { setCheckedKeys } = useCreateSliceActions(reducers.slice.actions);
  const {
    dossierData,
    isPending,
    error,
    isDocumentGenerating,
    checkedKeys,
    loadedKeys,
  } = useAppSelector(selectors.filenetDossierStructureSelector);

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [_, expandedKeys, onExpand] = useExpandTreeActions(
    dossierData,
    'loaded' as keyof TreeElement,
  );

  const dispatch = useAppDispatch();

  const onLoadData = useCallback(
    async ({
      key,
      isDirectory,
      numericKey,
    }: {
      key: Key;
      numericKey: Key;
      isDirectory?: boolean | undefined;
      loaded?: boolean | undefined;
    }): Promise<void> =>
      new Promise<void>((resolve, reject) => {
        if (loadedKeys.includes(key) || !isDirectory) {
          /** Если не директория или уже загружена - выход */
          resolve();
        } else {
          dispatch(thunks.getSubTreeThunk({ key, cardId, numericKey }))
            .unwrap()
            .then(() => {
              resolve();
            })
            .catch((err) => {
              appErrorNotification(
                `Произошла ошибка при загрузке листьев дерева по ключу. Хук useDossierTree`,
                err,
              );
              reject();
            });
        }
      }),
    [cardId, dispatch, loadedKeys],
  );

  const onCheck = (
    checkedTreeKeys: Key[] | { checked: Key[]; halfChecked: Key[] },
  ): void => {
    if ('checked' in checkedTreeKeys && !isDocumentGenerating) {
      setCheckedKeys(checkedTreeKeys.checked);
    }
  };

  return {
    onLoadData,
    onCheck,
    dossierData,
    isPending,
    error,
    isDocumentGenerating,
    checkedKeys,
    loadedKeys,
    expandedKeys,
    onExpand,
  };
};
