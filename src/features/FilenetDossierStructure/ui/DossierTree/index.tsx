import { Tree } from 'antd';
import { FC } from 'react';
import { ApiContainer } from 'shared/ui';
import { hooks } from './store';
import styles from './styles.module.scss';
import { renderDossierTitle } from './ui';

export type DossierTreeProps = {
  cardId: string;
  height: number;
};

export const DossierTree: FC<DossierTreeProps> = ({ cardId, height }) => {
  const {
    onLoadData,
    onCheck,
    dossierData,
    isPending,
    error,
    checkedKeys,
    loadedKeys,
    expandedKeys,
    onExpand,
  } = hooks.useDossierTree({ cardId });

  return (
    <ApiContainer error={error} isPending={isPending}>
      <Tree
        treeData={dossierData}
        loadedKeys={loadedKeys}
        loadData={onLoadData}
        expandedKeys={expandedKeys}
        onExpand={onExpand}
        height={height}
        checkable
        disabled={isPending}
        checkedKeys={checkedKeys}
        checkStrictly
        className={styles.tree}
        selectable={false}
        showLine
        titleRender={renderDossierTitle}
        onCheck={onCheck}
      />
    </ApiContainer>
  );
};
