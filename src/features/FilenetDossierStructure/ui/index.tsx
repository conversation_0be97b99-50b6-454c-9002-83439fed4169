import { <PERSON><PERSON>, <PERSON>, Tooltip } from 'antd';
import { ResultStatusType } from 'antd/es/result';
import classNames from 'classnames';

import type { FC } from 'react';

import AutoSizer from 'react-virtualized-auto-sizer';
import { errorMessages } from 'shared/config';
import { useAppSelector } from 'shared/model';
import { useCreateSliceActions } from 'shared/model/useCreateSliceActions';
import { AppPopup, ApiContainer } from 'shared/ui';

import { hooks, selectors, reducers } from '../store';

import { DossierTree } from './DossierTree';
import styles from './styles.module.scss';

const DossierStructure: FC<{ cardId: string }> = ({ cardId }) => {
  const getStructureFile = hooks.useGetDossierStructure(cardId);

  const { dossierData, isPending, error, isDocumentGenerating, checkedKeys } =
    useAppSelector(selectors.filenetDossierStructureSelector);

  const errorStatuses: { message: string; status: ResultStatusType } = (() => {
    if (error && dossierData.length === 0) {
      return { status: 404, message: 'Структура отсутствует' };
    }

    return { status: 500, message: errorMessages.pendingError };
  })();

  return (
    <ApiContainer
      error={error}
      isPending={isPending}
      errorTitle={errorStatuses.message}
      errorStatus={errorStatuses.status}
    >
      <Spin
        wrapperClassName={styles.treeContainer}
        spinning={isDocumentGenerating}
      >
        <AutoSizer disableWidth className={styles.autoSizer}>
          {({ height }) => <DossierTree cardId={cardId} height={height} />}
        </AutoSizer>
      </Spin>

      <Tooltip
        title="Необходимо выбрать папки"
        trigger={checkedKeys.length === 0 ? 'hover' : []}
      >
        <Button
          type="primary"
          disabled={checkedKeys.length === 0 || isDocumentGenerating}
          onClick={getStructureFile}
        >
          Получить ссылки
        </Button>
      </Tooltip>
    </ApiContainer>
  );
};

export const FilenetDossierStructure: FC = () => {
  const { handleDossierClose } = useCreateSliceActions(reducers.slice.actions);
  const { isOpened, cardId } = useAppSelector(
    selectors.filenetDossierStructureIsOpenAndCardIdSelector,
  );

  return (
    <AppPopup
      closeOnEscape
      isOpened={isOpened && cardId !== null}
      onClose={handleDossierClose}
      key="dossier-structure"
      className={classNames(styles.container)}
      title="Структура досье"
      fullSizeClassName={styles.fullSize}
    >
      <DossierStructure cardId={cardId || ''} />
    </AppPopup>
  );
};
