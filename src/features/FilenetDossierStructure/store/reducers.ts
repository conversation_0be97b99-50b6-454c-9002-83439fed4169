import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ParsedDossierStructureTree } from 'entities/FilenetAttachmentsTree';
import { getFilteredFlatTreeByParam } from 'shared/lib';
import { FilenetDossierInitialState } from '..';
import { setChildrenToTree } from '../lib';
import {
  getStructureFileThunk,
  getStructureTreeThunk,
  getSubTreeThunk,
} from './thunks';

const initialState: FilenetDossierInitialState = {
  isOpened: false,
  dossierData: [],
  isPending: false,
  error: null,
  cardId: null,
  checkedKeys: [],
  loadedKeys: [],
  expandedKeys: [],
  isDocumentGenerating: false,
};

export const slice = createSlice({
  name: 'filenetDossierStructure',
  initialState,
  reducers: {
    handleDossierOpen: (state, { payload }: PayloadAction<string>) => {
      state.isOpened = true;
      state.cardId = payload;
    },
    handleDossierClose: (state) => {
      state.isOpened = false;
      state.cardId = null;
    },
    setCheckedKeys: (state, { payload }: PayloadAction<Key[]>) => {
      state.checkedKeys = payload;
    },
    setLoadedKeys: (state, { payload }: PayloadAction<Key[]>) => {
      state.loadedKeys = payload;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(getStructureTreeThunk.pending, (state) => {
      state.isPending = true;
      state.error = null;
      state.dossierData = [];
      state.checkedKeys = [];
    });
    builder.addCase(getStructureTreeThunk.fulfilled, (state, { payload }) => {
      state.isPending = false;
      state.dossierData = payload;
      const flatTree = getFilteredFlatTreeByParam(payload, 'key');
      state.loadedKeys = (flatTree as ParsedDossierStructureTree[])
        .filter((node) => node.loaded && node.isDirectory)
        .map((node) => node.key);
    });
    builder.addCase(getStructureTreeThunk.rejected, (state, { error }) => {
      state.isPending = false;
      state.error = error;
    });

    builder.addCase(getStructureFileThunk.pending, (state) => {
      state.isDocumentGenerating = true;
    });
    builder.addCase(getStructureFileThunk.fulfilled, (state) => {
      state.isDocumentGenerating = false;
    });
    builder.addCase(getStructureFileThunk.rejected, (state) => {
      state.isDocumentGenerating = false;
    });
    builder.addCase(getSubTreeThunk.fulfilled, (state, { payload }) => {
      const { tree, key } = payload;
      state.dossierData = setChildrenToTree(state.dossierData, key, tree);
      state.loadedKeys.push(key);
    });
  },
});
