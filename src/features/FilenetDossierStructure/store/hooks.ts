import { useCallback, useEffect } from 'react';
import { useAppDispatch } from 'shared/model';

import { getStructureFileThunk, getStructureTreeThunk } from './thunks';

export const useGetDossierStructure = (cardId: string): Callback => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    const promise = dispatch(getStructureTreeThunk(cardId));

    return () => {
      promise.abort();
    };
  }, [cardId, dispatch]);

  return useCallback(() => {
    dispatch(getStructureFileThunk());
  }, [dispatch]);
};
