// cb styles

@import 'antd/dist/antd.css';

@import './styles/index';

.app {
  position: relative;
  display: flex;
  min-width: 320px;

  /** TODO: фикс для самого первого спинера (возможно из за этого стиля может что то поплыть, но пока все работает)  */

  height: 100vh;
}

.spinnerContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.h-100 {
  height: 100%;
}

.w-100 {
  width: 100%;
}

.modalOverlay {
  position: fixed;
  z-index: 5;
  inset: 0;
  background-color: rgb(0 0 0 / 30%) !important;
  cursor: pointer;
}

.ant-table-row {
  &-disabled {
    background-color: #e8e4e4;
    cursor: not-allowed;

    &:hover > td {
      background-color: #e8e4e4 !important;
    }
  }
}

.ant-tree-switcher,
.ant-tree-treenode {
  display: flex !important;
  align-items: center !important;
}

.ant-tree-checkbox {
  margin: 0 !important;
}

.ant-typography {
  word-break: normal !important;
}
