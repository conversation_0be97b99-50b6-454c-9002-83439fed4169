import { createSelector } from '@reduxjs/toolkit';
import { selectSelf } from 'shared/lib/selectSelf';
import { LoadingStatusState } from '../types';
import { slice } from './reducers';

const selectSefLoadingStatus = selectSelf(slice.name);

export const selectIsLoading = createSelector(
  selectSefLoadingStatus,
  (state) => !!state.isLoadingDatabase,
);

export const selectCurrentFinishDate = createSelector(
  selectSefLoadingStatus,
  (state) => state.currentFinishDate || '',
);

export const selectLoadingStatus = createSelector(
  selectSefLoadingStatus,
  (state): LoadingStatusState => ({
    isLoadingDatabase: state.isLoadingDatabase || false,
    currentFinishDate: state.currentFinishDate || '',
    isPending: !!state.isPending,
    error: state.error || null,
    current: state.current,
    total: state.total,
    text: state.text,
  }),
);
