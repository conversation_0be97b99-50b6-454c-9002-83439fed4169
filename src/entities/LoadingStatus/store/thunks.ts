import { createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import { apiUrls, appInstance } from 'shared/api';
import { StatusResponse } from '../types';

export const getLoadingStatus = createAsyncThunk<
  StatusResponse,
  void,
  { rejectValue: string }
>(
  'loadingStatus/fetchStatus',
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async (_, { rejectWithValue }) => {
    try {
      const response = await appInstance.get<StatusResponse>(
        apiUrls.replicator.progress,
      );
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          error.response?.data?.message ||
            'Произошла ошибка при загрузке статуса',
        );
      }
      return rejectWithValue('Произошла неизвестная ошибка');
    }
  },
);
