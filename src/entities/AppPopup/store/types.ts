export type PopupName =
  | 'confirm'
  | 'download'
  | 'tableConfig'
  | 'reports'
  | 'replicatorSettings'
  | 'replicatorLog'
  | 'electronicVerificationPassport'
  | 'directoryElectronicVerificationPassport'
  | 'upload';

export interface AppPopupInitialState {
  openedPopupsStack: PopupName[];
  popup: Record<PopupName, boolean>;
}

export interface UsePopupReturn {
  handleCloseLastOpened: Callback;
  handlePopupClose: (popup: PopupName) => void;
  handlePopupOpen: (popup: PopupName) => void;
}

export type UsePopupActions = () => UsePopupReturn;
