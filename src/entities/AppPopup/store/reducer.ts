import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { useCallback } from 'react';
import { useAppDispatch } from 'shared/model';
import type { AppPopupInitialState, PopupName, UsePopupActions } from './types';

export const initialState: AppPopupInitialState = {
  popup: {
    confirm: false,
    download: false,
    reports: false,
    tableConfig: false,
    replicatorSettings: false,
    replicatorLog: false,
    electronicVerificationPassport: false,
    directoryElectronicVerificationPassport: false,
    upload: false,
  },
  openedPopupsStack: [],
};

export const slice = createSlice({
  name: 'appPopup',
  initialState,
  reducers: {
    handlePopupOpen: (
      state: AppPopupInitialState,
      { payload }: PayloadAction<PopupName>,
    ) => {
      state.popup[payload] = true;
      state.openedPopupsStack = [...state.openedPopupsStack, payload];
    },
    handlePopupClose: (
      state: AppPopupInitialState,
      { payload }: PayloadAction<PopupName>,
    ) => {
      state.popup[payload] = false;
      state.openedPopupsStack = [...state.openedPopupsStack].filter(
        (item) => item !== payload,
      );
    },
    handleCloseLastOpened: (state: AppPopupInitialState) => {
      if (state.openedPopupsStack.length > 0) {
        const lastItem =
          state.openedPopupsStack[state.openedPopupsStack.length - 1];
        const immutablePopupStack = state.openedPopupsStack.slice(
          0,
          state.openedPopupsStack.length - 1,
        );

        state.popup[lastItem] = false;
        state.openedPopupsStack = immutablePopupStack;
      }
    },
  },
});

export const usePopupActions: UsePopupActions = () => {
  const dispatch = useAppDispatch();

  const handlePopupOpen = useCallback(
    (popup) => dispatch(slice.actions.handlePopupOpen(popup)),
    [dispatch],
  );
  const handlePopupClose = useCallback(
    (popup) => dispatch(slice.actions.handlePopupClose(popup)),
    [dispatch],
  );

  const handleCloseLastOpened = useCallback(
    () => dispatch(slice.actions.handleCloseLastOpened()),
    [dispatch],
  );

  return {
    handlePopupOpen,
    handlePopupClose,
    handleCloseLastOpened,
  };
};

export default slice.reducer;
