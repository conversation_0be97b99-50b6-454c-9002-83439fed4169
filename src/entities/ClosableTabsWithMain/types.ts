import { TabsProps } from 'antd/lib/tabs';
import { ReactNode } from 'react';

/** Ре-экспорт таба ант дизайна */
export type Tab = NonNullable<TabsProps['items']>[0];

export type ClosableTabsWithMainProps =
  /**
   * Проброс и реформат в обязательные ключи просов? текущего ключа и хендлера
   * клика
   */
  Required<Pick<TabsProps, 'activeKey' | 'onTabClick'>> & {
    /** Основной компонент таблицы */
    MainTab: ReactNode;
    /** Дополнитьельные табы */
    additionalTabs: Tab[];
    /**
     * Колбек на закрытие таба. Принимает ключ таба для закрытия и текущий
     * выбранный таб
     */
    onClose: (key: Key, selectedTab: string) => void;
    /** @link Title */
    title: Title;
  };

/** Колбек на получение rowId, tabName и проверка на дабл клик */
export type OnTabAddition = (
  TabElement: ReactNode,
) => (
  rowId: import('features/DataGrid').TableRowData['rowId'],
  tabName: string,
  isDoubleClick: boolean,
) => void;
