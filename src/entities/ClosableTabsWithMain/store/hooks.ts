import { useCallback, useEffect, useRef, useState } from 'react';
import { v4 } from 'uuid';
import { useTabsHandler } from 'shared/model';
import { ClosableTabsWithMainProps, OnTabAddition } from '..';

import type { Tab } from '..';
import { FIRST_TAB } from '../config';

export const useTabs = (
  tableEndpoint: string,
  options?: {
    isAdditionDisabled?: boolean;
    isSingleTab?: boolean;
  },
): [
  string,
  (newTab: string) => void,
  OnTabAddition,
  Tab[],
  (key: Key, currentSelectedTab: string) => void,
  Callback,
] => {
  const [selectedTab, setSelectedTab] = useTabsHandler();
  const [additionalTabs, setAdditionalTabs] = useState<Tab[]>([]);

  const tabMapByRowId = useRef(new Map<string, string>());
  const tabMapByKey = useRef(new Map<string, string>());

  const clearState = useCallback(() => {
    setAdditionalTabs([]);
    tabMapByRowId.current = new Map<string, string>();
    tabMapByKey.current = new Map<string, string>();
    setSelectedTab(FIRST_TAB);
  }, []); // eslint-disable-line

  useEffect(() => {
    clearState();
  }, [tableEndpoint]); // eslint-disable-line

  /** Колбек на закрытие таба */
  const closeTab = useCallback<ClosableTabsWithMainProps['onClose']>(
    (key: Key, currentSelectedTab: string) => {
      if (typeof key !== 'string') return;

      const tabName = tabMapByKey.current.get(key);
      if (!tabName) return;

      setAdditionalTabs((prev) => prev.filter((tab) => tab.key !== key));
      tabMapByRowId.current.delete(tabName);
      tabMapByKey.current.delete(key);

      if (key === currentSelectedTab) {
        setSelectedTab(FIRST_TAB);
      }
    },
    [setSelectedTab],
  );

  /** Колбек на клик по табу */
  const onTabClick = useCallback(
    (newTab) => {
      setSelectedTab(newTab);
    },
    [setSelectedTab],
  );

  /** Колбек на добавление таба */
  const addTab = useCallback<OnTabAddition>(
    (TabElement) => (rowId, tabName, isDoubleClick) => {
      /** Дизейбл открытия */
      if (options?.isAdditionDisabled || rowId === undefined) return;

      const tabHash = JSON.stringify(rowId);

      if (isDoubleClick) {
        setSelectedTab(tabMapByRowId.current.get(tabHash));
      }

      setAdditionalTabs((prev) => {
        if (!tabMapByRowId.current.has(tabHash)) {
          /** Если синглтон табы, то пересоздает мапу */
          if (options?.isSingleTab) {
            tabMapByRowId.current = new Map<string, string>();
            tabMapByKey.current = new Map<string, string>();
          }

          const key = v4();

          tabMapByRowId.current.set(tabHash, key);
          tabMapByKey.current.set(key, tabHash);

          const newTab: Tab = {
            key,
            closable: true,
            label: tabName,
            forceRender: true,
            children: TabElement,
          };

          if (isDoubleClick) {
            setSelectedTab(key);
          }

          return options?.isSingleTab ? [newTab] : [newTab, ...prev];
        }

        return prev;
      });
    },
    [options?.isAdditionDisabled, options?.isSingleTab, setSelectedTab],
  );

  return [
    selectedTab,
    onTabClick,
    addTab,
    additionalTabs,
    closeTab,
    clearState,
  ];
};
