export interface IndexFilesResponse {
  list: {
    children: {
      data: {
        closed: boolean;
        description: string;
        externalId: string;
        hasChildren: boolean;
        iconType: null;
        id: number;
        label: string;
        name: string;
        orderString: string;
        parentId: number;
        parentName: string;
        periodicity: null;
        root: boolean;
        selectable: boolean;
      };
    }[];
  }[];
}

export interface IndexFilesInitial extends ApiDefaultKeys {
  table: TableColumnsAndRowsWithPagination;
}
