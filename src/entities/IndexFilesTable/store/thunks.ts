import { createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import { IndexFilesResponse } from 'entities/IndexFilesTable';
import { apiUrls, filenetServiceInstance } from 'shared/api';

export const getIndexFilesThunk = createAsyncThunk<
  IndexFilesResponse,
  void,
  {
    state: import('processes/store').RootState;
  }
>(
  'indexFiles/getIndexFilesThunk',
  async (_, { rejectWithValue }) => {
    try {
      const { data } = await filenetServiceInstance.get<IndexFilesResponse>(
        apiUrls.fileNet.rubrics,
      );

      return data;
    } catch (err) {
      if (axios.isAxiosError(err)) {
        return rejectWithValue(err);
      }

      throw err;
    }
  },
  {
    condition: (endpoint, { getState }) => {
      const {
        table: { rows },
        isPending,
      } = getState().indexFiles;

      return !isPending || rows.length !== 0;
    },
  },
);
