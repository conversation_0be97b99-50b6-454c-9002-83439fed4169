import { apiUrls, appInstance } from 'shared/api';
import { createBasicGetThunkWithUrl } from 'shared/lib';
import { specialPermissionsStore } from '..';

export const getReportPermissionsThunk = createBasicGetThunkWithUrl<
  specialPermissionsStore.enums.ReportPermissions[]
>(
  'specialPermissions/getReportPermissions',
  apiUrls.reportPermissions,
  appInstance,
  {
    errorTitle: 'Ошибка получения прав для отчетов',
  },
);

export const getWildcardPermissionsThunk = createBasicGetThunkWithUrl<
  specialPermissionsStore.enums.ReportPermissions[]
>(
  'specialPermissions/getWildcardPermissions',
  apiUrls.workGroup.wildcardPermissions,
  appInstance,
  {
    errorTitle: 'Ошибка получения прав доступа',
  },
);
