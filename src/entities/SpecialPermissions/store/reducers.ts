import { createSlice } from '@reduxjs/toolkit';
import { ReportPermissions, WildcardPermissions } from './enums';
import {
  getReportPermissionsThunk,
  getWildcardPermissionsThunk,
} from './thunks';

type PermissionsState<T> = {
  permissions: T[];
  status: {
    isError: boolean;
    isLoaded: boolean;
    isLoading: boolean;
  };
};

type SpecialPermissionsInitial = {
  report: PermissionsState<ReportPermissions>;
  wildcard: PermissionsState<WildcardPermissions>;
};

const defaultStatus = {
  isLoading: false,
  isLoaded: false,
  isError: false,
};

const initialState: SpecialPermissionsInitial = {
  report: {
    permissions: [],
    status: { ...defaultStatus },
  },
  wildcard: {
    permissions: [],
    status: { ...defaultStatus },
  },
};

const thunks = [
  {
    thunk: getReportPermissionsThunk,
    name: 'report' as const,
  },
  {
    thunk: getWildcardPermissionsThunk,
    name: 'wildcard' as const,
  },
] as const;

export const slice = createSlice({
  name: 'specialPermissions',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    thunks.forEach(({ thunk, name }) => {
      builder
        .addCase(thunk.pending, (state) => {
          state[name].status = {
            ...defaultStatus,
            isLoading: true,
          };
        })
        .addCase(thunk.rejected, (state) => {
          state[name].status = {
            ...defaultStatus,
            isError: true,
          };
        })
        .addCase(thunk.fulfilled, (state, { payload }) => {
          state[name].status = {
            ...defaultStatus,
            isLoaded: true,
          };
          state[name].permissions = [
            ...new Set([...state[name].permissions, ...payload]),
          ] as typeof state[typeof name]['permissions'];
        });
    });
  },
});
