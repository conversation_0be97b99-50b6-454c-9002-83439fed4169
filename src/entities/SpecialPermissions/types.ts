import { specialPermissionsStore } from 'entities/SpecialPermissions';

export type SpecialPermissionsStatus = {
  isError: boolean;
  isLoaded: boolean;
  isLoading: boolean;
};

export type SpecialPermissionsInitial = {
  report: {
    permissions: specialPermissionsStore.enums.ReportPermissions[];
    status: SpecialPermissionsStatus;
  };
  wildcard: {
    permissions: specialPermissionsStore.enums.WildcardPermissions[];
    status: SpecialPermissionsStatus;
  };
};
