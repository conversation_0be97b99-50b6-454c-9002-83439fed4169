import { SizeType } from 'antd/lib/config-provider/SizeContext';

/** Интерфейс, описывающий пропсы экшенов */
export interface FilenetActionsProps {
  /** Вложение файла */
  attachment: {
    /** Имя файла */
    filename: string;
    /** Идентификатор файла */
    id: string;
  };
  /** Идентификатор карточки */
  cardId: number | string;
  /** Разрешения */
  permissions: string[];
  /** Опция для скрытия при нехватке прав */
  hideNotEnoughRights?: boolean;
  /** Размер */
  size?: SizeType;
}

export type UseActions = (
  cardId: FilenetActionsProps['cardId'],
  attachment: FilenetActionsProps['attachment'],
) => {
  onCopy: Callback;
  onDownload: Callback;
  onView: Callback;
};
