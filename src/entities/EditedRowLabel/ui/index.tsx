import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Tag } from 'antd';
import { FC } from 'react';

import styles from './styles.module.scss';

export const EditedRowLabel: FC<{
  row: import('features/DataGrid').TableRowData;
}> = ({ row }) => {
  if (!row?.edited) {
    return null;
  }

  return (
    <Tag
      className={styles.edited}
      icon={<ExclamationCircleOutlined className={styles.editedIcon} />}
      color="warning"
    >
      Есть изменения
    </Tag>
  );
};
