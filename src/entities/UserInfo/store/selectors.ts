import { createSelector } from '@reduxjs/toolkit';
import { selectSelf } from 'shared/lib';
import { DEFAULT_USER_NAME } from '../config';

export const userInfoSelector = selectSelf('userInfo');

export const userNameSelector = createSelector(
  userInfoSelector,
  (state) =>
    state.auth?.principal.attributes.displayName ||
    state.auth?.principal.attributes.username ||
    DEFAULT_USER_NAME,
);

export const userInfoApiSelector = createSelector(
  userInfoSelector,
  ({ isPending, error }) => ({
    isPending,
    error,
  }),
);
