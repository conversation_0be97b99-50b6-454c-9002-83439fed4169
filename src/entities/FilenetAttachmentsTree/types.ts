import { ReactElement } from 'react';

/** Структура дерева файловой системы FileNet */
export interface FileNetStructureTree {
  /** Дочерние элементы дерева */
  children: FileNetStructureTree[];
  /** Данные досье */
  data: FilenetResponseData;
  /** Тип иконки */
  iconType: string | null;
  /** Метка */
  label: string;
  /** Ключ для файлов вида '0-0-1-3' */
  numericKey: Key;
  /** Загружены ли дети на backend */
  loaded?: boolean;
}

/** Ответ с деревом файловой системы FileNet */
export interface FileNetStructureTreeResponse {
  /** Список элементов дерева */
  list: FileNetStructureTree[];
}

/** Распарсенная структура дерева досье */
export type ParsedDossierStructureTree = Omit<TreeElement, 'children'> &
  Pick<
    FileNetStructureTree['data'],
    'id' | 'viewDownloadPermissions' | 'attachmentId' | 'folder' | 'iconType'
  > &
  Pick<FileNetStructureTree, 'loaded' | 'numericKey'> & {
    children: ParsedDossierStructureTree[];
  };

/** Свойства компонента дерева вложений в FileNet */
export interface FilenetAttachmentsTreeProps {
  /** Путь для получения данных */
  folder: string;
  /** Идентификатор объекта для получения данных */
  id: string | number;
  /**
   * Функция для отрисовки заголовка узла
   *
   * @param node - Распарсенная структура дерева досье
   * @returns Элемент React
   */
  renderTitle: (node: ParsedDossierStructureTree) => ReactElement;
}

/** Данные об организационных единицах */
export interface OrganizationUnitsData {
  /** Отображаемый код */
  displayCode: null | string | number;
  /** Внешний идентификатор */
  externalId: null | string | number;
  /** Идентификатор */
  id: number;
  /** Метка */
  label: string;
  /** Отозванная лицензия */
  licenceRevoked: boolean;
  /** Название */
  name: string;
  /** Официальное название */
  officialName: null | string;
  /** Название родительской единицы */
  parentName: null | string;
  /** Тип в виде строки */
  typeStr: string;
}

/** Интерфейс для представления данных структуры досье. */
export interface FilenetResponseData {
  /** Связанные документы. */
  associatedDocuments: null | number[];
  /** Идентификатор вложения. */
  attachmentId: null | number;
  /** Наличие проверочной карточки. */
  auditCard: boolean;
  /** Идентификатор проверочной карточки. */
  auditCardId: null | number;
  /** Название проверочной карточки. */
  auditCardName: null | string;
  /** Период проверки: с (дата). */
  auditPeriodFrom: null | string;
  /** Период проверки: по (дата). */
  auditPeriodTo: null | string;
  /** Автор документа. */
  author: null | string;
  /** Отменен ли документ. */
  canceled: boolean;
  /** Путь к содержимому документа. */
  contentPath: null | string;
  /** Является ли этот документ основным. */
  document: boolean;
  /** Внешний идентификатор типа документа. */
  documentTypeExternalId: null | string;
  /** Название типа документа. */
  documentTypeName: null | string;
  /** Размер файла. */
  fileSize: null | string;
  /** Папка, в которой находится документ. */
  folder: null | string;
  /** Внешний идентификатор группы типов документов. */
  groupDocumentTypeExternalId: null | string;
  /** Название группы типов документов. */
  groupDocumentTypeName: null | string;
  /** Есть ли дополнительные файлы. */
  hasAnnexFiles: boolean;
  /** Тип иконки */
  iconType: 'xls' | 'xlsx' | 'pdf' | 'doc' | 'docx' | null;
  /** Идентификатор документа. */
  id: number | string;
  /** Организационные единицы, которые были проверены. */
  inspectedOrganizationUnits: number[];
  /** Данные об организационных единицах, которые были проверены. */
  inspectedOrganizationUnitsData: OrganizationUnitsData[];
  /** Организационные единицы, которые проводили проверку. */
  inspectorOrganizationUnits: number[];
  /** Данные об организационных единицах, которые проводили проверку. */
  inspectorOrganizationUnitsData: OrganizationUnitsData[];
  /** Метка. */
  label: null | string;
  /** Является ли этот документ основным. */
  main: boolean;
  /** Дата и время последнего изменения. */
  modifiedDatetime: string;
  /** Дата и время последнего изменения (по Гибралтарскому стандартному времени). */
  modifiedGibrDatetime: null | string;
  /** Дата и время публикации. */
  publishedDatetime: null | string;
  /** Заголовок документа. */
  title: string;
  /** Название типа. */
  typeName: null | string;
  /** Дата и время загрузки. */
  uploadDatetime: string;
  /** Дата и время загрузки (по Гибралтарскому стандартному времени). */
  uploadGibrDatetime: null | string;
  /** Права на просмотр и загрузку. */
  viewDownloadPermissions: string[] | null;
  /** Возвращается лицензия. */
  licenceRevoked?: boolean;
}
