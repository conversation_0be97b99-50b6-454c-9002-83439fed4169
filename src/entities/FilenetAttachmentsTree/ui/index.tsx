import { PaperClipOutlined } from '@ant-design/icons';
import { Button, Popover, Spin, Tree } from 'antd';
import { FC, useEffect, useState } from 'react';
import { FilenetAttachmentsTreeProps, ParsedDossierStructureTree } from '..';
import { useFilenetAttachmentsData } from '../hooks';
import { parseDossierResTree } from '../lib';
import styles from './styles.module.scss';

export const FilenetAttachmentsTree: FC<FilenetAttachmentsTreeProps> = ({
  renderTitle,
  folder,
  id,
}) => {
  const [treeData, setTreeData] = useState<ParsedDossierStructureTree[]>([]);

  const attachData = useFilenetAttachmentsData(folder, id);
  const isLoading = attachData.isPending;

  useEffect(() => {
    if (attachData.data && Array.isArray(attachData.data.list)) {
      const list = parseDossierResTree(attachData.data.list, false);
      setTreeData(list);
    }
  }, [attachData.data]);

  return (
    <Popover
      placement="right"
      title={
        <div className={styles.loadingIndicator}>
          <span>Вложения</span>
          {isLoading && <Spin size="small" />}
        </div>
      }
      trigger={['click']}
      content={
        <div className={styles.root}>
          <Tree
            height={540}
            virtual
            className={styles.tree}
            treeData={treeData}
            selectable={false}
            showLine
            titleRender={renderTitle}
          />
        </div>
      }
      overlayInnerStyle={{
        borderRadius: 5,
        boxShadow: '0 0 10px black',
        minWidth: 450,
        width: 'max-content',
        height: 600,
        overflowY: 'auto',
      }}
    >
      <Button icon={<PaperClipOutlined />} type="primary" />
    </Popover>
  );
};
