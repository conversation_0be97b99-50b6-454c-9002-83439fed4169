import { useEffect, useRef, useState } from 'react';
import { apiUrls, filenetServiceInstance } from 'shared/api';
import { generateUrlWithQueryParams } from 'shared/lib';
import { FileNetStructureTree, FileNetStructureTreeResponse } from '..';

export type UseFilenetAttachmentsData = {
  data: FileNetStructureTreeResponse | null;
  error: AppError | null;
  hasAttachments: boolean;
  isPending: boolean;
};

export const useFilenetAttachmentsData = (
  folder: string,
  id: string | number,
): UseFilenetAttachmentsData => {
  const [state, setState] = useState<UseFilenetAttachmentsData>({
    data: null,
    error: null,
    hasAttachments: false,
    isPending: false,
  });

  const bufferRef = useRef<FileNetStructureTree[]>([]);
  const rafRef = useRef<number | null>(null);
  const sourceRef = useRef<EventSource | null>(null);

  useEffect(() => {
    if (!folder || !id) {
      return () => {
        // Возвращаем noop
      };
    }

    setState({
      data: null,
      error: null,
      hasAttachments: false,
      isPending: true,
    });
    bufferRef.current = [];

    const path = generateUrlWithQueryParams(
      apiUrls.fileNet.attachmentsTreeStream,
      { folder, id },
    );
    const url = `${filenetServiceInstance.defaults.baseURL}${path}`;

    sourceRef.current = new EventSource(url);

    const flush = (): void => {
      if (bufferRef.current.length) {
        setState((prev) => {
          const list = prev.data?.list ?? [];
          const merged = [...list, ...bufferRef.current];
          bufferRef.current = [];
          return {
            ...prev,
            data: { list: merged },
            hasAttachments: merged.length > 0,
          };
        });
      }
      rafRef.current = requestAnimationFrame(flush);
    };
    rafRef.current = requestAnimationFrame(flush);

    function onMessage(event: MessageEvent<string>): void {
      try {
        const parsed = JSON.parse(event.data);

        if (parsed.id === -1) {
          flush();
          setState((p) => ({ ...p, isPending: false }));
          // eslint-disable-next-line @typescript-eslint/no-use-before-define
          cleanUp();
          return;
        }

        bufferRef.current.push({
          label: parsed.label,
          data: parsed,
          loaded: true,
          children: [],
          iconType: parsed.iconType,
          numericKey: String(parsed.id),
        });
      } catch {
        setState((p) => ({
          ...p,
          error: { message: 'Ошибка обработки данных' } as AppError,
        }));
        // eslint-disable-next-line @typescript-eslint/no-use-before-define
        cleanUp();
      }
    }

    function onError(): void {
      setState((p) => ({
        ...p,
        error: { message: 'Ошибка соединения' } as AppError,
        isPending: false,
      }));
      // eslint-disable-next-line @typescript-eslint/no-use-before-define
      cleanUp();
    }

    function cleanUp(): void {
      if (sourceRef.current) {
        sourceRef.current.close();
        sourceRef.current.removeEventListener('message', onMessage);
        sourceRef.current.removeEventListener('error', onError);
        sourceRef.current = null;
      }
      if (rafRef.current !== null) {
        cancelAnimationFrame(rafRef.current);
        rafRef.current = null;
      }
    }

    sourceRef.current.addEventListener('message', onMessage);
    sourceRef.current.addEventListener('error', onError);

    return () => {
      cleanUp();
    };
  }, [folder, id]);

  return state;
};
