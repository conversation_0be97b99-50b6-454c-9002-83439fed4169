import { FileNetStructureTree, ParsedDossierStructureTree } from '..';

/**
 * Парсит структуру досье FileNet в структуру обработанного досье.
 *
 * @param dossierTree - Структура дерева FileNet, которую нужно обработать.
 * @param keyAsFolder - Флаг, указывающий, следует ли использовать значение
 *   папки или ключа в качестве ключевого свойства узлов обработанного дерева.
 * @param parentKey - Значение ключа родительского узла текущего дерева досье.
 * @returns Обработанное дерево структуры досье.
 */

export const parseDossierResTree = (
  dossierTree: FileNetStructureTree[],
  keyAsFolder: boolean,
  parentKey = '',
): ParsedDossierStructureTree[] =>
  dossierTree.reduce<ParsedDossierStructureTree[]>(
    (parsedTree, dossier, index) => {
      const { label, data, loaded, children } = dossier;
      const numKey = `${parentKey}${parentKey && '-'}${index + 1}`;
      const {
        id,
        folder,
        viewDownloadPermissions,
        attachmentId,
        contentPath,
        main,
        document,
        iconType,
      } = data;

      const key = keyAsFolder && contentPath === null ? `${folder}` : numKey;

      parsedTree.push({
        ...(children && {
          children: parseDossierResTree(children, keyAsFolder, numKey),
        }),
        numericKey: numKey,
        key,
        isMain: main,
        checkable: !document,
        isDirectory: !document,
        loaded,
        isLeaf: document,
        folder,
        title: label,
        id,
        viewDownloadPermissions,
        attachmentId,
        iconType,
      });

      return parsedTree;
    },
    [],
  );
