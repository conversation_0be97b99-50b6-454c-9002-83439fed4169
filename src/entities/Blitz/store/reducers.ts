import { createSlice } from '@reduxjs/toolkit';
import { BlitzInitialState } from '..';
import { getBlitzPayloadThunk } from './actions';

const initialState: BlitzInitialState = {
  authorizationUrl: '',
  tokenUrl: '',
  error: null,
  isPending: false,
};

export const slice = createSlice({
  name: 'blitz',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(getBlitzPayloadThunk.pending, (state) => {
      state.isPending = true;
      state.error = null;
    });

    builder.addCase(
      getBlitzPayloadThunk.fulfilled,
      (state, { payload: { authorizationUrl, tokenUrl } }) => {
        state.isPending = false;
        state.tokenUrl = tokenUrl;
        state.authorizationUrl = authorizationUrl;
      },
    );

    builder.addCase(getBlitzPayloadThunk.rejected, (state, { error }) => {
      state.isPending = false;
      state.error = error;
    });
  },
});
