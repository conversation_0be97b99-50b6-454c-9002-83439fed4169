import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { PermissionsInitial } from '../types';

const initialState: PermissionsInitial = {
  ap_PlanKO: [],
  ap_AccessControl: [],
  ap_AlertList: [],
  ap_ControlNAC: [],
  ap_ControlWG: [],
  ap_ExecutionControl: [],
  ap_ExecutionControl_KO: [],
  ap_ExecutionControl_NFO: [],
  ap_FilesEPC: [],
  ap_FileStore: [],
  ap_FileStoreReport: [],
  ap_FileStoreSync: [],
  ap_FilesWGO: [],
  ap_sFilesWGO: [],
  ap_InternalWGO: [],
  ap_ListDEF: [],
  ap_ListEPC: [],
  ap_ListRef: [],
  ap_ListWG: [],
  ap_ManageWGO: [],
  ap_ManageNAC: [],
  ap_WGO: [],
  ap_Loading: [],
  ap_Notification: [],
  ap_OfferKO: [],
  ap_OfferNFO: [],
  ap_PackageDEF: [],
  ap_PlanControl: [],
  ap_PlanNFO: [],
  ap_PlanWGO: [],
  ap_RequestNoticeWG: [],
  ap_StructureEPC: [],
  ap_TemplateEPC: [],
  ap_UnplanControl: [],
};

export const slice = createSlice({
  name: 'permissions',
  initialState,
  reducers: {
    setPermissions: (
      state,
      { payload }: PayloadAction<Partial<PermissionsInitial>>,
    ) => ({ ...state, ...payload }),
  },
});
