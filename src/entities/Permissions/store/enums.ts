/** Идентификаторы действия */
export enum Actions {
  /** Создание карточки */
  CREATE_PROFILE_AT = 'at_CreateProfile',
  /** Просмотр карточки */
  VIEW_PROFILE_AT = 'at_ViewProfile',
  /** Редактирование карточки */
  EDIT_PROFILE_AT = 'at_EditProfile',
  /** Создание карточки */
  DELETE_PROFILE_AT = 'at_DeleteProfile',
  /** Отправка в САДД */
  SEND_TO_SADD_AT = 'at_SendSADD',
  /** Просмотр списка */
  VIEW_LIST_AT = 'at_ViewList',
  /** Редактирование списка */
  EDIT_LIST_AT = 'at_EditList',
  /** Просмотр файлов */
  VIEW_FILES_AT = 'at_ViewFiles',
  /** Загрузка файлов */
  LOAD_FILES_AT = 'at_LoadFiles',
  /** Даление файлов */
  DELETE_FILES_AT = 'at_DeleteFiles',
  /** Выгрузка файлов */
  DOWNLOAD_FILES_AT = 'at_DownloadFiles',
  /** Общая настройка */
  CHANGE_SETTING_AT = 'at_Setting',
}

/** Идентификаторы ресурсов */
export enum Resources {
  /** Сводный план и внеплановые проверки */
  SUMMARY_PLAN_AP = 'ap_PlanKO',
  /** Единый план и внеплановые проверки */
  ASSOCIATED_PLAN_AP = 'ap_PlanNFO',
  /** План проверок поднадзорных лиц */
  JOINED_PLAN_AP = 'ap_PlanControl',
  /** Внеплановые проверки поднадзорных лиц */
  JOINED_PLAN_UNPLANNED_CHECKS_AP = 'ap_UnplanControl',
  /** Предложения в Сводный план */
  OFFER_SUMMARY_PLAN_AP = 'ap_OfferKO',
  /** Предложения в Единый план */
  OFFER_ASSOCIATED_PLAN_AP = 'ap_OfferNFO',
  /** Контроль выполнения - кредитные организации */
  EXECUTION_CONTROL_KO_AP = 'ap_ExecutionControl_KO',
  /** Контроль выполнения - поднадзорные организации */
  EXECUTION_CONTROL_NFO_AP = 'ap_ExecutionControl_NFO',
  /** Контроль выполнения - поднадзорные лица */
  EXECUTION_CONTROL_PL_AP = 'ap_ExecutionControl',
  /** Справочник организаций */
  ORGANIZATION_DICTIONARY = 'ap_ListRef',
  /** Загрузка */
  DATA_LOAD_AP = 'ap_Loading',
  /** Синхронизация с файловым хранилищем */
  FILENET_SYNC_AP = 'ap_FileStoreSync',
  /** Файловое хранилище */
  FILENET_AP = 'ap_FileStore',
  /** Отчет "Работа пользователей с файловым хранилищем" */
  FILENET_REPORTS_AP = 'ap_FileStoreReport',
  /** Извещения пользователей */
  NOTIFICATIONS_AP = 'ap_Notification',
  /** Управление доступом */
  ACCESS_CONTROL_AP = 'ap_AccessControl',
  /** Управление кабинетами рабочих групп */
  WORKGROUP_CABINET_MANAGER_AP = 'ap_ManageWGO',
  /** УКРГ: Шаблоны структуры каталогов ЭПП */
  WORKGROUP_CABINET_EPC_TEMPLATE_AP = 'ap_TemplateEPC',
  /** УКРГ: Уведомления кабинетов рабочих групп */
  WORKGROUP_CABINET_NOTIFY_AP = 'ap_ManageNAC',
  /** УКРГ: План создания КРГ */
  WORKGROUP_CABINET_PLANING_AP = 'ap_PlanWGO',
  /** УКРГ: Состав рабочей группы (формирование) */
  WORKGROUP_CABINET_CONTROL_AP = 'ap_ControlWG',
  /** УКРГ: Уведомления и подтверждения */
  WORKGROUP_CABINET_CONFIRM_AP = 'ap_ControlNAC',
  /** КРГ: Кабинеты рабочих групп» */
  WORKGROUP_CABINET_AP = 'ap_WGO',
  /** КРГ: Состав рабочей группы (просмотр) */
  WORKGROUP_CABINET_MEMBERS = 'ap_ListWG',

  /** КРГ: Заявки и уведомления РГ */
  WORKGROUP_CABINET_REQUESTS_AP = 'ap_RequestNoticeWG',
  /** КРГ: Каталоги ЭПП */
  WORKGROUP_CABINET_EPC_STRUCTURE_AP = 'ap_StructureEPC',
  /** КРГ: Внутренняя переписка */
  WORKGROUP_CABINET_SUBSCRIBE_AP = 'ap_InternalWGO',
  /** КРГ: Входящие пакеты ДЭФ */
  WORKGROUP_CABINET_INCOMING_DEF_AP = 'ap_PackageDEF',
  /** КРГ: Файлы */
  WORKGROUP_CABINET_FILES_AP = 'ap_FilesWGO',
  /** КРГ: Поиск файлов */
  WORKGROUP_CABINET_SEARCH_FILES_AP = 'ap_sFilesWGO',
  /** КРГ: Описи ДЭФ */
  WORKGROUP_CABINET_DEF_LISTS_AP = 'ap_ListDEF',
  /** КРГ: Описи ЭПП */
  WORKGROUP_CABINET_EPC_LISTS_AP = 'ap_ListEPC',
  /** КРГ: Лента оповещений */
  WORKGROUP_CABINET_ALERTS_AP = 'ap_AlertList',
  /** КРГ: Файлы ЭПП'; */
  WORKGROUP_CABINET_EPC_FILES_AP = 'ap_FilesEPC',
}

export enum SpecialPermissions {
  FullAccess = 'wildcard_epc_full_access_name_key',
  PrintAccess = 'wildcard_file_print_pl_name_key',
  CopyAccess = 'wildcard_file_copy_name_key',
  OkatoAccess = 'wildcard_okato_12_name_key',
  FileDownloadPl = 'wildcard_file_download_pl_name_key',
  AuditQuestion = 'wildcard_audit_question_name_key',
  ActivityKind = 'wildcard_activity_kind_name_key',
  FilePrint = 'wildcard_file_print_name_key',
  FileType = 'wildcard_file_type_name_key',
  AuditQuestionPl = 'wildcard_audit_question_pl_name_key',
  FileDownloadKm = 'wildcard_file_download_km_name_key',
  Inspection = 'wildcard_inspection_name_key',
  DateRangePl = 'wildcard_date_range_pl_name_key',
  OkatoPl = 'wildcard_okato_pl_name_key',
  FileTypeNfo = 'wildcard_file_type_nfo_name_key',
  CheckListPl = 'wildcard_check_list_pl_name_key',
  FilePrintNfo = 'wildcard_file_print_nfo_name_key',
  OperRight = 'wildcard_oper_right_name_key',
  AuditQuestionNfo = 'wildcard_audit_question_nfo_name_key',
  FileCopyPl = 'wildcard_file_copy_pl_name_key',
  FileTypePl = 'wildcard_file_type_pl_name_key',
  FileCopyNfo = 'wildcard_file_copy_nfo_name_key',
  FileSearch = 'wildcard_file_search_name_key',
  CheckListNfo = 'wildcard_check_list_nfo_name_key',
  DateRangeNfo = 'wildcard_date_range_nfo_name_key',
  OkatoNfo = 'wildcard_okato_nfo_name_key',
  ReportGet = 'wildcard_report_get_name_key',
  DateRange = 'wildcard_date_range_name_key',
  FileDownload = 'wildcard_file_download_name_key',
  CheckList = 'wildcard_check_list_name_key',
  FileDownloadNfo = 'wildcard_file_download_nfo_name_key',
}
