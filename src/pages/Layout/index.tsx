import { AnimatePresence } from 'framer-motion';
import { Suspense } from 'react';
import type { FC } from 'react';
import { Outlet } from 'react-router-dom';

import { SideBar } from 'widgets/SideBar';

import { AppSpinner } from 'shared/ui/AppSpinner';

import styles from './styles.module.scss';

const Layout: FC = () => (
  <>
    <SideBar />
    <main className={styles.main} id="main">
      <Suspense fallback={<AppSpinner />}>
        <AnimatePresence exitBeforeEnter>
          <Outlet />
        </AnimatePresence>
      </Suspense>
    </main>
  </>
);

export default Layout;
