import { Button } from 'antd';
import { blitzStore } from 'entities/Blitz';

import { AppResult } from 'shared/ui/AppResult';
import { PageContainer } from 'shared/ui/PageContainer';

export const KzidNotAvailable = ({ title }: { title: string }): JSX.Element => {
  const logout = blitzStore.hooks.useBlitzLogout();

  return (
    <PageContainer containerKey="kzid-403">
      <AppResult
        status="403"
        title={title}
        extra={<Button onClick={logout}>Выход</Button>}
      />
    </PageContainer>
  );
};
