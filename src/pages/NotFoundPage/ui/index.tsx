import { Button } from 'antd';
import { FC } from 'react';

import { useRedirectBack } from 'shared/model';

import { AppResult } from 'shared/ui/AppResult';
import { PageContainer } from 'shared/ui/PageContainer';
import styles from './styles.module.scss';

export const NotFoundPage: FC = () => {
  const handleRedirect = useRedirectBack();

  return (
    <PageContainer containerKey="404" className={styles.container}>
      <AppResult
        title="Такой страницы не существует"
        status={404}
        extra={
          <Button type="primary" onClick={handleRedirect}>
            Вернуться
          </Button>
        }
      />
    </PageContainer>
  );
};
