import { createSlice } from '@reduxjs/toolkit';

type ControlAndPlansInitialState = {
  loadingStatusBox: {
    isOpened: boolean;
  };
};
const initialState: ControlAndPlansInitialState = {
  loadingStatusBox: {
    isOpened: true,
  },
};

export const slice = createSlice({
  name: 'controlAndPlans',
  initialState,
  reducers: {
    closeIsLoadingStatusBox: (state) => {
      state.loadingStatusBox.isOpened = false;
    },
  },
});
