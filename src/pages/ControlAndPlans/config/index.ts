// Эндпойнты, которые загружаются с задержкой (до 1 часа)
export const isLazyReport = (endpoint: string): boolean =>
  !!{
    '/joined_audit_plan': true,
    '/summary_plan_unplanned_audit': true,
    '/associated_plan_unplanned_audit': true,
    '/unplanned_audits': true,
    '/offer_summary_plan_nfo': true,
    '/offer_associated_plan': true,
    '/execution_control_nfo': true,
    '/execution_persons': true,
    '/execution_supervising_nfo': true,
  }[endpoint];

type FilterKey = string;
type FilterValue = { checked?: boolean; selectedIndex?: string };
type FilterInvariants = {
  [key: string]: Array<[(value: string) => boolean, FilterKey, FilterValue]>;
};

const EmptyObject = {};
/**
 * Выбор определенных фильтров может менять другие фильтры, для этого нужна эта
 * таблица (APPID-1804)
 */
export const getFiltersInvariants = (endpoint: string): FilterInvariants => {
  /**
   * Объект, содержащий конфигурацию фильтров для различных эндпоинтов. Каждый
   * ключ соответствует эндпоинту, а значение - это объект, описывающий правила
   * модификации фильтров для данного эндпоинта. Правила модификации фильтров
   * представлены в виде массивов, где каждый элемент массива - это массив,
   * содержащий:
   *
   * 1. Функцию-предикат, возвращающую boolean, определяющую, когда применять
   *    модификацию
   * 2. Ключ фильтра, который нужно модифицировать
   * 3. Объект с новыми значениями для модифицируемого фильтра
   */
  const filterInvariants: Record<string, FilterInvariants> = {
    '/offer_summary_plan_nfo': {
      // ItemId фильтра, выбор которого должен модифицировать другие фильтры
      'offerSummaryPlanNfoFilters_Доступные фильтры_Параметры_Параметры_Год проверки(внеплан.)':
        [
          [
            // Колбэк, возвращающий boolean, если true, то применяются изменения для фильтра в этом массиве
            // В данном случае нам не важно, какое значение будет у года проверки (внеплан), при любом значении
            // меняем План на индекс 1
            () => true,
            'offerSummaryPlanNfoFilters_Доступные фильтры_Сводный план_План_План',
            { selectedIndex: '1' },
          ],
        ],
      'offerSummaryPlanNfoFilters_Доступные фильтры_Сводный план_План_План': [
        [
          // Если план не равен 1 (это индекс Внеплановые проверки КО)
          // То анчекаем фильтр год проверки (внеплан)
          (value) => value !== '1',
          'offerSummaryPlanNfoFilters_Доступные фильтры_Параметры_Параметры_Год проверки(внеплан.)',
          { checked: false },
        ],
      ],
    },
    '/offer_associated_plan': {
      'offerAssociatedPlanNfoFilters_Доступные фильтры_Параметры_Параметры_Год проверки(внеплан.)':
        [
          [
            () => true,
            'offerAssociatedPlanNfoFilters_Доступные фильтры_План_План_План',
            { selectedIndex: '0' },
          ],
        ],
      'offerAssociatedPlanNfoFilters_Доступные фильтры_План_План_План': [
        [
          (value) => value !== '1',
          'offerAssociatedPlanNfoFilters_Доступные фильтры_Параметры_Параметры_Год проверки(внеплан.)',
          { checked: false },
        ],
      ],
    },
    '/summary_plan_unplanned_audit': {
      'summaryPlanFilters_Доступные фильтры_Диапазоны дат_Диапазоны дат_Год проверки(внеплан.)':
        [
          [
            () => true,
            'summaryPlanFilters_Доступные фильтры_План_План_План',
            { selectedIndex: '1' },
          ],
        ],
      'summaryPlanFilters_Доступные фильтры_План_План_План': [
        [
          (value) => value !== '1',
          'summaryPlanFilters_Доступные фильтры_Диапазоны дат_Диапазоны дат_Год проверки(внеплан.)',
          { checked: false },
        ],
      ],
    },
    '/associated_plan_unplanned_audit': {
      'associatedPlanFilters_Доступные фильтры_Диапазоны дат_Диапазоны дат_Год проверки(внеплан.)':
        [
          [
            () => true,
            'associatedPlanFilters_Доступные фильтры_План_План_План',
            { selectedIndex: '0' },
          ],
        ],
      'associatedPlanFilters_Доступные фильтры_План_План_План': [
        [
          (value) => value !== '1',
          'associatedPlanFilters_Доступные фильтры_Диапазоны дат_Диапазоны дат_Год проверки(внеплан.)',
          { checked: false },
        ],
      ],
    },
  };

  return filterInvariants[endpoint] || EmptyObject;
};
