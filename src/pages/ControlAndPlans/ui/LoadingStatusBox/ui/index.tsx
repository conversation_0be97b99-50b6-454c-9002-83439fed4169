import { CloseOutlined } from '@ant-design/icons';
import { type FC } from 'react';

import { controlAndPlansStore } from 'pages/ControlAndPlans';
import { loadingStatusStore } from 'entities/LoadingStatus';
import { useAppSelector, useCreateSliceActions } from 'shared/model';
import styles from './styles.module.scss';

export const LoadingStatusBox: FC = () => {
  const loadingStatus = useAppSelector(
    loadingStatusStore.selectors.selectLoadingStatus,
  );
  const isOpened = useAppSelector(
    controlAndPlansStore.selectors.isOpenedLoadingStatusBox,
  );
  const { closeIsLoadingStatusBox } = useCreateSliceActions(
    controlAndPlansStore.reducers.slice.actions,
  );

  if (!isOpened) {
    return null;
  }

  return (
    <div className={styles.container}>
      <div className={styles.root}>
        <div className={styles.message}>
          {loadingStatus.error ? (
            <div>{`Ошибка получения статуса загрузки: ${loadingStatus.error}`}</div>
          ) : (
            <>
              <div>{`Данные актуальны на: ${loadingStatus.currentFinishDate}`}</div>
              <div>{`Текущий статус:  ${
                loadingStatus.isLoadingDatabase
                  ? 'Идет загрузка'
                  : 'Загрузка завершена'
              }`}</div>
            </>
          )}
        </div>
        <CloseOutlined
          className={styles.closeButton}
          onClick={() => closeIsLoadingStatusBox()}
        />
      </div>
    </div>
  );
};
