import { Divider } from 'antd';

import { useCallback, useLayoutEffect, useMemo, useState } from 'react';
import type { FC } from 'react';
import { AppHeader } from 'widgets/AppHeader';
import { DirectoryOfOrganizationCard } from 'widgets/DirectoryOfOrganizationCard';
import { apiUrls } from 'shared/api';
import { useHandleCloseOpen } from 'shared/model';
import { useAxiosRequest } from 'shared/model/useAxiosRequest';
import { ApiContainer } from 'shared/ui/ApiContainer';

import { CardWithData } from 'shared/ui/CardWithData';
import { PageContainer } from 'shared/ui/PageContainer';
import { DirectoryOfOrganizationData } from '..';

import styles from './styles.module.scss';

export const DirectoryOfOrganization: FC = () => {
  const [popupEndpoint, setPopupEndpoint] = useState('');
  const [getOrganizationTabs, { data, isPending, error }] =
    useAxiosRequest<DirectoryOfOrganizationData[]>();

  const [isCardOpen, openCard, closeCard] = useHandleCloseOpen(false);

  const handleCardWithData = useCallback((endpoint) => {
    openCard();
    setPopupEndpoint(endpoint);
  }, []); // eslint-disable-line

  const memoCards = useMemo(
    () =>
      Array.isArray(data) &&
      data.map((item) => (
        <CardWithData
          key={item.key}
          title={item.title}
          icon={item.icon}
          onClick={() => handleCardWithData(item.endPoint)}
        />
      )),
    [data], // eslint-disable-line
  );

  useLayoutEffect(() => {
    getOrganizationTabs(apiUrls.directoryOfOrganizations.tabs);
  }, []); // eslint-disable-line

  return (
    <PageContainer containerKey="DirectoryOfOrganization">
      <AppHeader title="Справочник организаций" />

      <Divider className={styles.divider} />

      <ApiContainer
        className={styles.cards}
        error={error}
        isPending={isPending}
        errorStatus="warning"
      >
        <div className={styles.cards}>{memoCards}</div>
      </ApiContainer>

      <DirectoryOfOrganizationCard
        isOpened={isCardOpen}
        endpoint={popupEndpoint}
        handleClose={closeCard}
      />
    </PageContainer>
  );
};
