import { Tooltip } from 'antd';
import { createElement } from 'react';
import { TableColumnData } from 'features/DataGrid';

export const renderGroupAndRolesColumn = (
  columns: TableColumnData[],
): TableColumnData[] =>
  columns.map((column) => ({
    ...column,
    render: (text, row) =>
      createElement(
        Tooltip,
        { title: text, placement: 'topLeft' },
        createElement(row?.checkboxStatus?.isDeleted ? 's' : 'span', {}, text),
      ),
  }));
