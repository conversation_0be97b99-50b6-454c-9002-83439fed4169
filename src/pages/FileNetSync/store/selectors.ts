import { createSelector } from '@reduxjs/toolkit';
import { selectSelf } from 'shared/lib';

export const selectSelfFSync = selectSelf('fileNetSync');

export const uploadLogSelector = createSelector(
  selectSelfFSync,
  (state) => state.uploadLog,
);

export const uploadLogApiSelector = createSelector(
  uploadLogSelector,
  (state) => state.api,
);

export const uploadLogApiPendingSelector = createSelector(
  uploadLogApiSelector,
  (state) => state.isPending,
);

export const uploadLogFormDataSelector = createSelector(
  uploadLogSelector,
  (state) => state.formData,
);

export const uploadLogDetalizationSelector = createSelector(
  uploadLogFormDataSelector,
  (state) => state.detalization,
);

export const repositoriesSelector = createSelector(
  selectSelfFSync,
  (state) => state.repositories,
);

export const repositorySyncSelector = createSelector(
  selectSelfFSync,
  (state) => state.repositorySync,
);

export const summarySelector = createSelector(
  selectSelfFSync,
  (state) => state.summary,
);

export const summaryComponentAvailabilityInfoSelector = createSelector(
  summarySelector,
  (state) => state.componentAvailabilityInfo,
);

export const modalsSelector = createSelector(
  selectSelfFSync,
  (state) => state.modals,
);

export const isDiskOpenSelector = createSelector(
  modalsSelector,
  (state) => state.isDiskOpen,
);
export const isFormOpenSelector = createSelector(
  modalsSelector,
  (state) => state.isFormOpen,
);
