import { createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import moment from 'moment';

import { apiUrls, filenetServiceInstance } from 'shared/api';
import { DEFAULT_DATE_VARIANT } from 'shared/config/constants';
import { createBasicGetThunkWithUrl, downloadFile } from 'shared/lib';
import type {
  RepositoryResponse,
  RepositorySyncItem,
  SummaryResponse,
  UploadLogRequest,
} from '..';

export const postUploadLogThunk = createAsyncThunk<
  UploadLogRequest,
  number | void,
  { state: import('processes/store').RootState }
>(
  'fileNetSync/postUploadLogThunk',
  async (page, { getState, rejectWithValue }) => {
    const currentPage = page || 1;

    try {
      const { formData } = getState().fileNetSync.uploadLog;

      const body = { ...JSON.parse(JSON.stringify(formData)) };
      body.ui.first = (currentPage - 1) * formData.ui.rows;
      // Для бека нужно +1 день для формировани отчета
      body.periodTo = moment(formData.periodTo, DEFAULT_DATE_VARIANT)
        .add(1, 'day')
        .format(DEFAULT_DATE_VARIANT);

      const { data } = await filenetServiceInstance.post<UploadLogRequest>(
        apiUrls.fileNetSync.uploadLog,
        body,
      );

      return data;
    } catch (err) {
      if (axios.isAxiosError(err)) {
        return rejectWithValue(err);
      }

      throw err;
    }
  },
  {
    condition: (endpoint, { getState }) => {
      const state = getState().fileNetSync.uploadLog.api;

      return !state.isPending;
    },
  },
);

export const downloadDBReportThunk = createAsyncThunk<
  void,
  void,
  { state: import('processes/store').RootState }
>(
  'fileNetSync/downloadDBReportThunk',
  async (_, { getState, rejectWithValue }) => {
    try {
      const { formData } = getState().fileNetSync.uploadLog;

      const { data } = await filenetServiceInstance.post<Blob>(
        apiUrls.fileNet.download,
        formData,
        { responseType: 'blob' },
      );

      downloadFile(data, 'upload_log_report.xlsx');

      return undefined;
    } catch (err) {
      if (axios.isAxiosError(err)) {
        return rejectWithValue(err);
      }

      throw err;
    }
  },
  {
    condition: (endpoint, { getState }) => {
      const state = getState().fileNetSync.uploadLog.api;

      return !state.isPending;
    },
  },
);

export const getRepositoriesThunk =
  createBasicGetThunkWithUrl<RepositoryResponse>(
    'fileNetSync/getRepositoriesThunk',
    'control/find/replicator/repositories/requestPackageRepositoriesList',
    filenetServiceInstance,
  );

export const getSummaryThunk = createAsyncThunk<
  SummaryResponse,
  void,
  { state: import('processes/store').RootState }
>(
  'fileNetSync/getSummaryThunk',
  async (_, { rejectWithValue }) => {
    try {
      const { data } = await filenetServiceInstance.get<SummaryResponse>(
        apiUrls.fileNetSync.monitoring,
      );

      return data;
    } catch (err) {
      if (axios.isAxiosError(err)) {
        return rejectWithValue(err);
      }

      throw err;
    }
  },
  {
    condition: (endpoint, { getState }) => {
      const state = getState().fileNetSync.summary;

      return !state.isPending;
    },
  },
);

export const getRepositorySyncThunk = createAsyncThunk<
  RepositorySyncItem[],
  void,
  { state: import('processes/store').RootState }
>(
  'fileNetSync/getRepositorySyncThunk',
  async (_, { rejectWithValue }) => {
    try {
      const { data } = await filenetServiceInstance.get<RepositorySyncItem[]>(
        apiUrls.fileNetSync.findRepos,
      );

      return data;
    } catch (err) {
      if (axios.isAxiosError(err)) {
        return rejectWithValue(err);
      }

      throw err;
    }
  },
  {
    condition: (endpoint, { getState }) => {
      const state = getState().fileNetSync.repositorySync;

      return !state.isPending;
    },
  },
);
