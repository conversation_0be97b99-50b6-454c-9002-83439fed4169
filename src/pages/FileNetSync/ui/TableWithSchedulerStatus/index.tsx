import { FC, memo, useCallback, useState } from 'react';
import { FSyncTimelineOperations } from 'widgets/FSyncTimelineOperations';
import { DataGrid, TableColumnData } from 'features/DataGrid';
import { isDocumentStatus } from 'shared/lib/fileNetStatusCheck';
import {
  useAppDispatch,
  useAppSelector,
  useCreateSliceActions,
} from 'shared/model';
import { renderStatusIcon } from '../../lib';

import { selectors, reducers, actions } from '../../store';

import styles from './styles.module.scss';

export const TableWithSchedulerStatus: FC<{ getTableData: Callback }> = memo(
  ({ getTableData }) => {
    const formData = useAppSelector(selectors.uploadLogFormDataSelector);
    const uploadLogApiData = useAppSelector(selectors.uploadLogApiSelector);

    const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);
    const dispatch = useAppDispatch();
    const { setPage } = useCreateSliceActions(reducers.slice.actions);

    const renderExpandedRow = useCallback(
      (record) => (
        <FSyncTimelineOperations
          record={record}
          formData={formData}
          refreshTable={getTableData}
        />
      ),
      [formData, getTableData],
    );

    return (
      <DataGrid
        columns={uploadLogApiData.columns.map((column: TableColumnData) => {
          if (column.dataIndex === 'statusLabel') {
            return {
              ...column,
              render: (text, row) => (
                <span>
                  {renderStatusIcon(String(row.status))}&nbsp;{text}
                  {isDocumentStatus(String(row.status)) &&
                    row.documentExtId && <br />}
                  {isDocumentStatus(String(row.status)) &&
                    row.documentExtId &&
                    `(${row.documentExtId})`}
                </span>
              ),
            };
          }

          return column;
        })}
        rows={uploadLogApiData.rows}
        additionalClassNames={{ container: styles.table }}
        tableAdditionProps={{
          expandable: {
            expandRowByClick: true,
            expandedRowRender: renderExpandedRow,
            expandedRowKeys,
            onExpand: (expanded, record) => {
              setExpandedRowKeys(expanded ? [record.key as string] : []);
            },
          },
          pagination: {
            size: 'small',
            showSizeChanger: false,
            hideOnSinglePage: true,
            pageSize: formData.ui.rows,
            total: uploadLogApiData.totalRecords,
            disabled: uploadLogApiData.isPending,
            onChange: (page) => {
              setPage(page);
              dispatch(actions.postUploadLogThunk(page));
            },
            position: ['bottomLeft'],
          },
          loading: uploadLogApiData.isPending,
          scroll: { x: '100%', y: 'calc(100% - 10px)' },
        }}
      />
    );
  },
);
