import {
  CaretRightOutlined,
  RedoOutlined,
  StopOutlined,
} from '@ant-design/icons';
import { notification } from 'antd';
import classNames from 'classnames';
import type { FC } from 'react';
import { useCallback, useLayoutEffect } from 'react';
import { AppHeader } from 'widgets/AppHeader';

import { apiUrls, filenetServiceInstance } from 'shared/api';

import { appErrorNotification } from 'shared/lib';
import {
  createConfirmModal,
  useAppDispatch,
  useHandleCloseOpen,
} from 'shared/model';
import { useAxiosRequest } from 'shared/model/useAxiosRequest';
import { AppPopup } from 'shared/ui';
import { PageContainer } from 'shared/ui/PageContainer';

import type { RepositoriesRequest, SchedulerRequest } from '..';

import { actions, hooks } from '../store';

import { ComponentState } from './ComponentState';
import { DiskSpaceModal } from './DiskSpaceModal';
import { FormModal } from './FormModal';
import { HeadingButtons } from './HeadingButtons';
import { PackageSending } from './PackageSending';
import { RepositorySync } from './RepositorySync';
import { ScheduleConfig } from './ScheduleConfig';
import styles from './styles.module.scss';
import { TableWithSchedulerStatus } from './TableWithSchedulerStatus';

export const FileNetSync: FC = () => {
  /* ----------------------------------------------------
   *                      Хуки
   ---------------------------------------------------- */
  const dispatch = useAppDispatch();

  const [, openForm, closeForm] = hooks.useFormModal();
  const [, openDisk, closeDisk] = hooks.useFSyncDiskModal();
  const [isSyncOpen, openSync, closeSync] = useHandleCloseOpen();
  const [isSyncStatusOpen, openSyncStatus, closeSyncStatus] =
    useHandleCloseOpen();
  const [isScheduleOpen, openSchedule, closeSchedule] = useHandleCloseOpen();
  const [isPackageSendingOpen, openPackageSending, closePackageSending] =
    useHandleCloseOpen();

  const [scheduleTrigger, scheduleStatuses] = useAxiosRequest();

  /* ----------------------------------------------------
   *                      Колбэки
   ---------------------------------------------------- */

  /* Функция для запроса */
  const handleScheduleOperation = (
    data: RepositoriesRequest | SchedulerRequest | string[],
    scheduleAction: Endpoint,
    text: Record<'successMessage' | 'message', string>,
    onSuccess?: Callback,
  ): Callback =>
    createConfirmModal({
      title: 'Синхронизация с ФХ',
      message: text.message,
      onConfirm: () =>
        scheduleTrigger(
          `${filenetServiceInstance.defaults.baseURL}/${scheduleAction}`,
          {
            method: 'POST',
            data,
          },
        )
          .then(() => {
            notification.success({
              message: text.successMessage,
            });

            if (onSuccess) {
              onSuccess();
            }
          })
          .catch(() => appErrorNotification('Ошибка отправки запроса.')),
    }) as Callback;

  const getTableData = useCallback(() => {
    dispatch(actions.postUploadLogThunk());
  }, [dispatch]);

  const getRepositorySync = useCallback(() => {
    dispatch(actions.getRepositorySyncThunk());
  }, [dispatch]);

  /* ----------------------------------------------------
   *                      Сайды
   ---------------------------------------------------- */

  /* Получение данных при старте страницы */
  useLayoutEffect(() => {
    getTableData();
    dispatch(actions.getRepositoriesThunk());
  }, []); // eslint-disable-line

  /*
   * Сайд на создание интервала для получения информации,
   */
  useLayoutEffect(() => {
    const SECONDS = 15;

    const timer = setInterval(() => {
      dispatch(actions.getSummaryThunk());
    }, SECONDS * 1000);
  }, []); // eslint-disable-line

  /* ----------------------------------------------------
   *                      UI
   ---------------------------------------------------- */

  return (
    <PageContainer containerKey="filenet-sync" className={styles.pageContainer}>
      <AppHeader title="Синхронизация с ФХ" />

      <HeadingButtons
        popupCallback={{
          openSync,
          openForm,
          openSyncStatus,
          openSchedule,
          openPackageSending,
          openDisk,
        }}
        refresh={getTableData}
      />
      <TableWithSchedulerStatus getTableData={getTableData} />

      {/* ----------------------------------------------------
       *              Попап с настройкой
       ---------------------------------------------------- */}
      <AppPopup
        isOpened={isPackageSendingOpen}
        onClose={closePackageSending}
        title="Настройка автоматической досылки пакетов с ФХ"
        className={classNames(styles.popup, styles.popupForm)}
      >
        <PackageSending
          buttons={[
            {
              key: 'ok',
              title: 'Настроить',
              icon: <CaretRightOutlined />,
              type: 'primary',
              isCloseButton: true,
              onRepoButtonClick: (repos) =>
                handleScheduleOperation(
                  repos,
                  apiUrls.fileNetSync.configScheduler,
                  {
                    successMessage:
                      'Запрос на автоматическую досылку пакетов отправлен.',
                    message:
                      'Настроить расписание автоматической досылки пакетов?',
                  },
                  closePackageSending,
                ),
            },
            {
              key: 'abort',
              title: 'Отмена',
              isCloseButton: true,
              icon: <StopOutlined />,
              danger: true,
              ghost: true,
              onClick: closePackageSending,
            },
          ]}
          onOpen={() => dispatch(actions.getRepositoriesThunk())}
        />
      </AppPopup>

      {/* ----------------------------------------------------
       *           Попап с контролем состояния
       ---------------------------------------------------- */}
      <AppPopup
        className={classNames(styles.popup, styles.popupComponents)}
        isOpened={isSyncStatusOpen}
        title="Контроль состояния компонентов синхронизации"
        onClose={closeSyncStatus}
      >
        <ComponentState onOpen={() => dispatch(actions.getSummaryThunk())} />
      </AppPopup>

      {/* ----------------------------------------------------
       *           Попап с настройкой отчета
       ---------------------------------------------------- */}
      <FormModal getTableData={getTableData} />

      {/* ----------------------------------------------------
       *                      Попап синхронизации
       ---------------------------------------------------- */}
      <AppPopup
        isOpened={isSyncOpen}
        onClose={scheduleStatuses.isPending ? undefined : closeSync}
        className={classNames(styles.popup, styles.popupSync)}
        title="Синхронизация с ФХ"
      >
        <RepositorySync
          getRepositorySync={getRepositorySync}
          isPending={scheduleStatuses.isPending}
          buttons={[
            {
              key: 'start-sync',
              title: 'Начать синхронизацию',
              icon: <CaretRightOutlined />,
              type: 'primary',
              onRepoButtonClick: (repos) =>
                handleScheduleOperation(
                  { repositoryIds: repos },
                  apiUrls.fileNetSync.startScheduler,
                  {
                    message: 'Запустить синхронизацию документов?',
                    successMessage: 'Запрос на запуск синхронизации отправлен.',
                  },
                  closeSync,
                ),
            },
            {
              key: 'stop-sync',
              title: 'Остановить синхронизацию',
              icon: <StopOutlined />,
              danger: true,
              ghost: true,
              onRepoButtonClick: (repos) =>
                handleScheduleOperation(
                  { repositoryIds: repos },
                  apiUrls.fileNetSync.stopScheduler,
                  {
                    successMessage:
                      'Запрос на остановку синхронизации отправлен.',
                    message: 'Остановить синхронизацию документов?',
                  },
                  closeSync,
                ),
            },
            {
              key: 'refresh-immediately',
              title: 'Обновить немедленно',
              icon: <RedoOutlined />,
              onRepoButtonClick: (repos) =>
                handleScheduleOperation(
                  { repositoryIds: repos },
                  apiUrls.fileNetSync.immediatelyUpdateScheduler,
                  {
                    successMessage:
                      'Запрос на немедленную синхронизацию успешно отправлен.',
                    message: 'Отправить запрос на синхронизацию данных?',
                  },
                  closeSync,
                ),
            },
          ]}
        />
      </AppPopup>

      {/* ----------------------------------------------------
       *           Попап настройки расписания
       ---------------------------------------------------- */}
      <AppPopup
        isOpened={isScheduleOpen}
        onClose={closeSchedule}
        className={classNames(styles.popup, styles.popupSync)}
        title="Синхронизация с ФХ"
      >
        <ScheduleConfig>
          {(props) => (
            <RepositorySync
              getRepositorySync={getRepositorySync}
              showSettings
              buttons={[
                {
                  key: 'ok',
                  title: 'Настроить расписание',
                  icon: <CaretRightOutlined />,
                  type: 'primary',
                  onRepoButtonClick: (repos) =>
                    handleScheduleOperation(
                      {
                        repositories: { repositoryIds: repos },
                        schedulerSettings: props.schedulerSettings,
                      },
                      apiUrls.fileNetSync.schedulerSettings,
                      {
                        successMessage:
                          'Запрос на настройку нового расписания отправлен.',
                        message: 'Настроить расписание синхронизации?',
                      },
                      closeSchedule,
                    ),
                },
                {
                  key: 'abort',
                  title: 'Отмена',
                  isCloseButton: true,
                  icon: <StopOutlined />,
                  danger: true,
                  ghost: true,
                  onClick: closeSchedule,
                },
              ]}
            />
          )}
        </ScheduleConfig>
      </AppPopup>

      <DiskSpaceModal />
    </PageContainer>
  );
};
