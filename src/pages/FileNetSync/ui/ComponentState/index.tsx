import {
  AlertTwoTone,
  CheckCircleTwoTone,
  WarningTwoTone,
} from '@ant-design/icons';
import { Tooltip } from 'antd';
import type { FC, ReactNode } from 'react';
import { useEffect, useMemo } from 'react';

import { DataGrid } from 'features/DataGrid';
import type { TableColumnData, TableRowData } from 'features/DataGrid';

import { useAppSelector } from 'shared/model';
import { AppResult } from 'shared/ui/AppResult';

import { selectors } from '../../store';

import styles from './styles.module.scss';

/** Проверка на то что данные валидны */
const _checkDateValue = (date: unknown): string =>
  typeof date === 'string' && date.length !== 0
    ? date
    : '(Неизвестный формат даты)';

/** Рендер статуса в таблице */
const _renderAdditionalComponentStatusInfo = ({
  lastInactiveTimeFrom,
  lastInactiveTimeTo,
  lastAliveDatetime,
  status,
}: TableRowData): string =>
  lastInactiveTimeFrom
    ? `Был ${status === 'NORMAL' ? 'недоступен' : 'доступен'} с
      ${_checkDateValue(lastInactiveTimeTo)} по
      ${_checkDateValue(lastAliveDatetime)}`
    : `${status === 'NORMAL' ? 'Доступен' : 'Недоступен'} с
      ${_checkDateValue(lastAliveDatetime)}`;

/** Рендер дополнительного статуса репозитория */
const _renderAdditionalRepositoryStatusInfo = ({
  componentName,
  replicatorRepository,
  uploadRepository,
  lastAliveDatetime,
}: TableRowData): ReactNode =>
  !(componentName !== 'REPLICATOR' || !replicatorRepository) && (
    <>
      {replicatorRepository.lastInactiveTimeFrom
        ? `Был ${
            replicatorRepository.schedulerStatus === 'SERVICE'
              ? 'остановлен'
              : 'в работе'
          } с ${_checkDateValue(
            replicatorRepository.lastInactiveTimeFrom,
          )} по ${_checkDateValue(replicatorRepository.lastInactiveTimeTo)}`
        : `${
            replicatorRepository.schedulerStatus === 'SERVICE'
              ? 'В работе'
              : 'Остановлен'
          } с ${_checkDateValue(lastAliveDatetime)}`}
      {uploadRepository && (
        <>
          <br />
          Время последней загрузки файлов:&nbsp;
          {_checkDateValue(uploadRepository.lastActionDatetime)}
        </>
      )}
    </>
  );

/** Рендер доступности */
const _renderAvailability = (status: string): JSX.Element => {
  switch (status) {
    case 'NORMAL':
      return (
        <Tooltip title="Исправно">
          <CheckCircleTwoTone twoToneColor="#52c41a" />
        </Tooltip>
      );

    case 'WARNING':
      return (
        <Tooltip title="Некоторые компоненты синхронизации могут не функционировать">
          <WarningTwoTone twoToneColor="#faad14" />
        </Tooltip>
      );

    default:
      return (
        <Tooltip title="Неисправность системы">
          <AlertTwoTone twoToneColor="#ff7d7d" />
        </Tooltip>
      );
  }
};

/** Рендер статуса */
const _renderStatus = (status: string): JSX.Element => {
  switch (status) {
    case 'SERVICE':
      return (
        <Tooltip title="В работе">
          <CheckCircleTwoTone twoToneColor="#52c41a" />
        </Tooltip>
      );

    case 'STOPPED':
      return (
        <Tooltip title="Остановлено">
          <WarningTwoTone twoToneColor="#faad14" />
        </Tooltip>
      );

    default:
      return (
        <Tooltip title="Состояние неизвестно">
          <AlertTwoTone twoToneColor="#ff7d7d" />
        </Tooltip>
      );
  }
};

/** Массив колонок для таблицы */
const columns: TableColumnData[] = [
  {
    key: 'componentName',
    title: 'Имя компонента',
    dataIndex: 'componentName',
    width: 'auto',
    render: (componentName) => (
      <span>
        {{
          IMPORTATION_SERVICE: 'Загрузка документов',
          NEVA_RECEIVER: 'Приём файлов от ТПС Нева',
          REPLICATOR: 'Репликатор',
          PORTAL: 'Веб-интерфейс',
        }[componentName] ?? 'N/A'}
      </span>
    ),
  },
  {
    key: 'discriminator',
    title: 'Сервер / Репозиторий',
    dataIndex: 'discriminator',
    ellipsis: false,
  },
  {
    key: 'availability',
    title: 'Доступность',
    dataIndex: 'status',
    width: 120,
    align: 'center',
    render: _renderAvailability,
  },
  {
    key: 'status',
    title: 'Состояние',
    dataIndex: 'status',
    width: 120,
    align: 'center',
    render: (status, row) =>
      row.componentName === 'REPLICATOR'
        ? _renderStatus(row?.replicatorRepository?.schedulerStatus ?? '')
        : _renderAvailability(status),
  },
  {
    key: 'additional-info',
    title: 'Дополнительная информация',
    dataIndex: 'lastAliveDatetime',
    ellipsis: false,
    render: (_text, row) => (
      <span>
        {_renderAdditionalComponentStatusInfo(row)}
        <br />
        {_renderAdditionalRepositoryStatusInfo(row)}
      </span>
    ),
  },
];

/** Компонент с отображением статуса */
export const ComponentState: FC<{
  onOpen: Callback;
}> = ({ onOpen }) => {
  /* ----------------------------------------------------
   *                      Селекторы
   ---------------------------------------------------- */
  const {
    error,
    isPending,
    replicatorRepositoriesStatusInfo: { repositories: replicatorRepos },
    uploadRepositoryInfo: { repositories: uploadRepos },
    componentAvailabilityInfo: { components },
  } = useAppSelector(selectors.summarySelector);

  /* ----------------------------------------------------
   *                      Мемоизация
   ---------------------------------------------------- */

  /* Мапа с репами репликатора по айди */
  const replicatorReposMap = useMemo<
    Map<string, typeof replicatorRepos[0]>
  >(() => {
    const map = new Map<string, typeof replicatorRepos[0]>();

    replicatorRepos.forEach((repo) => {
      map.set(repo.repositoryId, repo);
    });

    return map;
  }, [replicatorRepos]);

  /* Мапа с репами репликатора по айди */
  const uploadReposMap = useMemo<Map<string, typeof uploadRepos[0]>>(() => {
    const map = new Map<string, typeof uploadRepos[0]>();

    uploadRepos.forEach((repo) => {
      map.set(repo.repositoryId, repo);
    });

    return map;
  }, [uploadRepos]);

  /* Мемоизированные ряды */
  const rows = useMemo(
    () =>
      components.map((row) => ({
        ...row,
        key: row.id,
        replicatorRepository: replicatorReposMap.get(row.discriminator),
        uploadRepository: uploadReposMap.get(row.discriminator),
      })),
    [components, uploadReposMap, replicatorReposMap],
  );

  /* ----------------------------------------------------
   *                      Сайды
   ---------------------------------------------------- */

  useEffect(onOpen, []); // eslint-disable-line

  /* ----------------------------------------------------
   *                        UI
   ---------------------------------------------------- */

  if (error && rows.length === 0) {
    return (
      <AppResult
        title="Что то пошло не так, попробуйте обновить страницу"
        status={500}
      />
    );
  }

  return (
    <div className={styles.content}>
      <DataGrid
        rows={rows}
        columns={columns}
        hideColumnSearch
        hideSorter
        tableAdditionProps={{
          loading: rows.length === 0 && isPending,
          bordered: true,
          size: 'small',
          pagination: {
            showSizeChanger: false,
            position: ['bottomLeft'],
            hideOnSinglePage: true,
            size: 'small',
            pageSize: 10,
          },
          scroll: { x: '100%', y: '70vh' },
        }}
      />
    </div>
  );
};
