@import '../styles.module';
@import 'src/app/styles/mixins';

.popup {
  @extend %popup;
}

.container {
  width: 100%;
  @include defaultBorder;

  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;

  &Content {
    display: flex;
    width: 100%;

    &Text {
      margin: 0;
      width: 20%;
    }

    &Action {
      width: 80% !important;
    }
  }
}

.buttonsRow {
  display: flex;
  width: 100%;
  justify-content: space-between;
}
