import { Typography } from 'antd';
import { useEffect, useState } from 'react';
import type { FC } from 'react';
import { DataGrid } from 'features/DataGrid';
import type { TableColumnData, TableRowData } from 'features/DataGrid';
import { useAppSelector } from 'shared/model';
import { AppResult } from 'shared/ui/AppResult';
import { AppSpinner } from 'shared/ui/AppSpinner';
import { ButtonsContainer } from 'shared/ui/ButtonsContainer';
import type { ButtonWithCallback } from '../..';

import { parseToCallbackButtons } from '../../lib';
import { selectors } from '../../store';
import styles from './styles.module.scss';

const columns: TableColumnData[] = [
  {
    key: 'repo-name',
    dataIndex: 'label',
    title: 'Название репозитория',
  },
];

export const PackageSending: FC<{
  buttons: ButtonWithCallback[];
  onOpen: Callback;
  isPending?: boolean;
}> = ({ buttons, onOpen, isPending }) => {
  const [checkedRepos, setCheckedRepos] = useState<string[]>([]);
  const [rows, setRows] = useState<TableRowData[]>([]);

  const repositories = useAppSelector(selectors.repositoriesSelector);

  useEffect(onOpen, []); // eslint-disable-line

  useEffect(() => {
    if (!repositories.error && repositories.repos.length > 0) {
      const newRows: TableRowData[] = [];
      const checkedKeys: string[] = [];

      repositories.repos.forEach((repo) => {
        if (repo.classifier) {
          checkedKeys.push(repo.value);
        }

        newRows.push({ ...repo, key: repo.value });
      });

      setRows(newRows);
      setCheckedRepos(checkedKeys);
    }
  }, [repositories.error, repositories.repos]);

  if (repositories.isPending) {
    return <AppSpinner />;
  }

  if (repositories.error) {
    return (
      <AppResult
        title="Что то пошло не так, попробуйте обновить страницу"
        status={500}
      />
    );
  }

  return (
    <>
      <div className={styles.container}>
        <Typography.Text>
          Выберите репозитории, для которых необходима автоматическая отправка
          запросов на досылку пакета:
        </Typography.Text>
        {repositories.repos.length === 0 ? (
          <AppResult title="Репозитории не найдены" status={404} />
        ) : (
          <DataGrid
            columns={columns}
            rows={rows}
            tableAdditionProps={{
              pagination: {
                hideOnSinglePage: true,
                position: ['bottomLeft'],
                pageSize: 10,
                showSizeChanger: false,
              },
              size: 'small',
              bordered: true,
              rowSelection: {
                onChange: (selectedRepos) => {
                  setCheckedRepos(selectedRepos as string[]);
                },
                selectedRowKeys: checkedRepos,
              },
            }}
          />
        )}
      </div>

      <ButtonsContainer
        buttons={parseToCallbackButtons(buttons, checkedRepos, isPending)}
      />
    </>
  );
};
