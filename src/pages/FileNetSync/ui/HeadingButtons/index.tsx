import {
  ApiOutlined,
  CloudSyncOutlined,
  DatabaseOutlined,
  FileOutlined,
  FileSyncOutlined,
  RedoOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { Bad<PERSON>, Button, Divider, Radio } from 'antd';
import { FC } from 'react';
import { filenetServiceInstance } from 'shared/api';

import { useAppSelector } from 'shared/model';
import { useCreateSliceActions } from 'shared/model/useCreateSliceActions';
import { ButtonsContainer } from 'shared/ui/ButtonsContainer';

import { enums, reducers, selectors } from '../../store';
import styles from './styles.module.scss';

interface HeaderProps {
  popupCallback: Record<
    | 'openForm'
    | 'openPackageSending'
    | 'openSchedule'
    | 'openSync'
    | 'openSyncStatus'
    | 'openDisk',
    Callback
  >;
  refresh: Callback;
}

export const HeadingButtons: FC<HeaderProps> = ({ popupCallback, refresh }) => {
  /* ----------------------------------------------------
   *                      Селекторы
   ---------------------------------------------------- */
  const detalization = useAppSelector(selectors.uploadLogDetalizationSelector);
  const { summaryStatus } = useAppSelector(
    selectors.summaryComponentAvailabilityInfoSelector,
  );

  /* ----------------------------------------------------
   *                      Хуки
   ---------------------------------------------------- */

  const { setDetalization } = useCreateSliceActions(reducers.slice.actions);

  /* ----------------------------------------------------
   *                      UI
   ---------------------------------------------------- */

  return (
    <div className={styles.container}>
      <div className={styles.buttons}>
        <ButtonsContainer
          buttons={[
            {
              type: 'primary',
              icon: <SettingOutlined />,
              key: 'storageSyncSettings',
              title: 'Настройки отчета',
              onClick: popupCallback.openForm,
            },
            {
              type: 'primary',
              icon: <CloudSyncOutlined />,
              key: 'storageSync',
              title: 'Синхронизация',
              onClick: popupCallback.openSync,
            },
            {
              type: 'default',
              icon: <SettingOutlined />,
              key: 'storageScheduleSettings',
              title: 'Настройка расписания',
              onClick: popupCallback.openSchedule,
            },
          ]}
        />

        <ButtonsContainer
          buttons={[
            {
              icon: <FileSyncOutlined />,
              type: 'primary',
              title: 'Настройка досылки пакетов',
              key: 'sending-package',
              onClick: popupCallback.openPackageSending,
            },
            {
              icon: <DatabaseOutlined />,
              type: 'default',
              title: 'Диск',
              key: 'Disk-btn',
              onClick: popupCallback.openDisk,
            },
          ]}
        />
      </div>

      <Divider />

      <div className={styles.buttons}>
        <div className={styles.content}>
          <Badge
            className={styles.badge}
            color={
              { NORMAL: '#52c41a', WARNING: '#faad14' }[summaryStatus] ??
              '#ff7d7d'
            }
            dot
          >
            <Button
              type="primary"
              onClick={popupCallback.openSyncStatus}
              icon={<ApiOutlined />}
            >
              Контроль состояния синхронизации
            </Button>
          </Badge>

          <ButtonsContainer
            buttons={[
              {
                title: 'Получить отчет',
                icon: <FileOutlined />,
                type: 'default',
                href: `${filenetServiceInstance.defaults.baseURL}/control/replicator/get/report`,
                target: '_blank',
                key: 'get-report',
              },
            ]}
          />
        </div>

        <div className={styles.content}>
          <Radio.Group
            value={detalization}
            onChange={(e) => setDetalization(e.target.value)}
          >
            <Radio value={enums.Detalization.ALL}>Все сообщения</Radio>
            <Radio value={enums.Detalization.PACKAGE}>Уровень пакета</Radio>
            <Radio value={enums.Detalization.DOCUMENT}>Уровень файла</Radio>
          </Radio.Group>
        </div>
        <ButtonsContainer
          buttons={[
            {
              title: 'Обновить',
              type: 'primary',
              icon: <RedoOutlined />,
              onClick: refresh,
              key: 'refresh',
            },
          ]}
        />
      </div>
    </div>
  );
};
