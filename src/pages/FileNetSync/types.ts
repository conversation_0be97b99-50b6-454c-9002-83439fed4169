import { enums } from './store';

export interface UploadLogResponsePayload {
  detalization: enums.Detalization;
  importStatus: enums.ImportStatus;
  periodFrom: string;
  periodTo: string;
  title: string;
  ui: {
    filters: null;
    first: number;
    rows: number;
    sortField: 'actionDatetime';
    sortOrder: '-1';
  };
  repositories?: string[];
}

export interface UploadLogCommit {
  attachmentId: string | null;
  commandId: string;
  commit: string;
  contentLength: string | null;
  contentPath: string | null;
  date: string;
  datetime: string;
  documentExtId: string | null;
  documentId: string | null;
  documentTitle: string | null;
  documents: string[];
  extendedMessageInfo: string | null;
  order: string;
  parentCommit: string | null;
  related: string[];
  repository: string;
  status: string;
  statusLabel: string;
  trace: string | null;
}

export interface UploadLogRequest {
  data: UploadLogCommit[];
  page: number;
  rows: number;
  totalPages: number;
  totalRecords: number;
}

/** Элемент репозитория с сервера */
export interface RepositoryItem {
  classifier: null | boolean;
  label: string;
  value: string;
}

export interface RepositoryResponse {
  data: RepositoryItem[];
}

export interface SummaryResponse {
  /** Список ПК контроль состояния синхронизации */
  componentAvailabilityInfo: {
    components: Array<
      Record<
        | 'componentName'
        | 'discriminator'
        | 'lastAliveDatetime'
        | 'lastInactiveTimeFrom'
        | 'lastInactiveTimeTo'
        | 'id'
        | 'status',
        string
      >
    >;
    summaryStatus: string;
  };
  /** Репозитории репликатора */
  replicatorRepositoriesStatusInfo: {
    repositories: Array<{
      lastActiveTime: string;
      lastInactiveTimeFrom: string | null;
      lastInactiveTimeTo: string | null;
      repositoryId: string;
      schedulerStatus: string;
    }>;
  };
  uploadRepositoryInfo: {
    repositories: Array<{
      lastActionDatetime: string;
      repositoryId: string;
    }>;
  };
}

export interface RepositorySyncItem {
  repositoryId: string;
  scheduler: {
    status: string;
    tasks: Array<{
      cronDescription: string;
      cronTriggerExpression: string;
      taskId: string;
    }>;
  };
}

export interface FileNetSyncInitialState {
  /** Обьект модалок фсинка */
  modals: Record<'isDiskOpen' | 'isFormOpen', boolean>;
  /** Данные по репозиториям */
  repositories: ApiDefaultKeys & {
    /** Массив элементов репозиториев */
    repos: RepositoryItem[];
  };
  /** Данные по репозиториям для синхронизации */
  repositorySync: ApiDefaultKeys & { repos: RepositorySyncItem[] };
  /** Данные по доступности и репликатору */
  summary: ApiDefaultKeys & SummaryResponse;
  /** Данные основной таблицы */
  uploadLog: {
    /** Контент страницы */
    api: ApiDefaultKeys &
      Pick<UploadLogRequest, 'page' | 'totalRecords'> & {
        commits: UploadLogRequest['data'];
      } & TableColumnsAndRows;
    /** Данные из формы */
    formData: UploadLogResponsePayload;
  };
}

/** Пейлоад для репозиториев */
export interface RepositoriesRequest {
  repositoryIds: string[];
}

/** Пейлоад для шедулера */
export interface SchedulerRequest {
  repositories: RepositoriesRequest;
  schedulerSettings: {
    periodType: enums.PeriodType;
    periodValue: string;
  };
}

/** Тип для кнопки с бробросом колбэка */
export type ButtonWithCallback = AdditionalButton & {
  isCloseButton?: boolean;
  onRepoButtonClick?: (repos: string[]) => Callback;
};
