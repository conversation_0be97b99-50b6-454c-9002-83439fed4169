export const pluralNounForms = (
  nounForms: Record<'one' | 'twoToFour' | 'many', string>,
  value: string,
): string => {
  const count = parseInt(value, 10);

  if (Number.isNaN(count)) return 'Ошибка';

  if (count > 4 && count <= 20) {
    return nounForms.many;
  }

  const remainder = count % 10;

  return remainder === 1
    ? nounForms.one
    : remainder > 1 && remainder <= 4
    ? nounForms.twoToFour
    : nounForms.many;
};

export const pluralAdjectiveForms = (
  nounForms: Record<'one' | 'many', string>,
  value: string,
): string => {
  const count = parseInt(value, 10);

  if (Number.isNaN(count)) return 'Ошибка';

  if (count > 4 && count <= 20) {
    return nounForms.many;
  }
  const remainder = count % 10;
  if (remainder === 1) {
    return nounForms.one;
  }
  return nounForms.many;
};
