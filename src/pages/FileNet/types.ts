import { TableColumnData, TableRowData } from 'features/DataGrid';
import { OrganizationUnitsData } from 'entities/FilenetAttachmentsTree';

export type TableType = 'dossier' | 'cards';

export interface PageParams {
  /** Id открытой карточки / досье */
  popupId: string | number | null;
  /** @link TableType */
  tableType: TableType;
}

export type SelectedFiltersType =
  | 'gibrSelectedFilter'
  | 'koSelectedFilter'
  | 'organizationsSelectedFilter'
  | 'territorialStructureSelectedFilter';

export interface FileNetInitialState {
  /** Стейт апи для таблиц */
  apiData: {
    docTypeIds: TableRowData[];
    unitIds: TableRowData[];
  };
  /** Стейт таблиц досье и файлов */
  mainTable: Record<TableType, CardAndDossierData> &
    Record<SelectedFiltersType, TableRowData | null> & {
      /** Колонки досье проверки */
      auditColumns: TableColumnData[];
      /** Колонки файлового хранилища */
      fileColumns: TableColumnData[];
      /** Состояние селекта false - карточки файлов | true - досье проверки */
      isAuditCards: boolean;
      /** Фильтр Кредитные организации */
    } & ApiDefaultKeys;
  /** Стейт меню с таблицами */
  tables: {
    /** Таблица файловой структуры уровней */
    fileStructure: TableColumnsAndRows & ApiDefaultKeys;
    /** Закешированны ли фильтры */
    isCached: boolean;
  };
}

export type CardAndDossierData = Pick<TableColumnsAndRows, 'rows'> & {
  pagination: { currentPage: number; total: number };
};

export interface ElasticTableData {
  auditCard: boolean;
  author: null | string;
  canceled: boolean;
  contentPath: string;
  id: number;
  modifiedDatetime: string;
  organizationUnitsData: OrganizationUnitsData[];
  public: boolean;
  publishedDatetime: string;
  title: Title;
  uploadDatetime: string;
  version: string;
}

export interface ElasticTableResponse {
  model: {
    data: ElasticTableData[];
    page: 0;
    rows: 10;
    totalPages: 137;
    totalRecords: 1367;
  };
  organizationUnitTypeCategory: null;
  publicCategory: null;
  rubricCategory: null;
}

export interface FilenetSelectedFiltersProps {
  docTypeIdsSelector: FileNetInitialState['apiData']['docTypeIds'];
  mainTableSelector: FileNetInitialState['mainTable'];
  onClose: (filterType: SelectedFiltersType) => void;
  onCloseTypeId: (typeId: TableRowData) => void;
  onRadioChange: (tableType: TableType) => void;
  tableType: TableType;
}
