import { Drawer } from 'antd';
import { FC } from 'react';
import { SelectedFiltersType } from 'pages/FileNet';
import { OrganizationalStructures } from 'widgets/OrganizationalStructures';
import { DataGrid, DataGridProps, TableRowData } from 'features/DataGrid';
import { indexFilesStore } from 'entities/IndexFilesTable';
import { useAppSelector } from 'shared/model';
import { ApiContainer } from 'shared/ui';
import { selectors } from '../../store';

import styles from './styles.module.scss';

const defaultTableAdditionalProps: DataGridProps['tableAdditionProps'] = {
  size: 'small',
  scroll: { x: '100%', y: 312 },
  pagination: {
    showSizeChanger: false,
    pageSize: 8,
    size: 'small',
    position: ['bottomCenter'],
    hideOnSinglePage: true,
  },
};

const defaultTableClassNames: DataGridProps['additionalClassNames'] = {
  container: styles.table,
};

export const FiltersDrawer: FC<{
  handleClearFormData: Callback;
  handleRowCheck: (selectedRows: TableRowData[]) => void;
  handleRowClick: (rowData: TableRowData, type: SelectedFiltersType) => void;
  isOpened: boolean;
  onClose: Callback;
}> = ({
  isOpened,
  onClose,
  handleRowClick,
  handleRowCheck,
  handleClearFormData,
}) => {
  const docTypeIds = useAppSelector(selectors.docTypeIdsSelector);

  const [{ isPending, error, table }, refresh] =
    indexFilesStore.hooks.useGetIndexFiles();

  return (
    <Drawer
      className={styles.drawer}
      placement="left"
      open={isOpened}
      onClose={onClose}
      width={600}
    >
      <OrganizationalStructures
        additionalClassNames={defaultTableClassNames}
        onFilterClick={handleRowClick}
        handleClearFormData={handleClearFormData}
        tableAdditionProps={defaultTableAdditionalProps}
        additionalPanels={[
          {
            key: 'fileStructure',
            header: 'Файловая структура',
            element: (
              <ApiContainer
                error={error}
                isPending={isPending}
                errorStatus="warning"
                errorTitle="Ошибка загрузки файловой структуры"
                refresh={refresh}
              >
                <DataGrid
                  columns={table.columns}
                  rows={table.rows}
                  additionalClassNames={defaultTableClassNames}
                  tableAdditionProps={{
                    ...defaultTableAdditionalProps,
                    rowSelection: {
                      selectedRowKeys: docTypeIds.map(({ key }) => key),
                      onChange: (_selectedRowKeys, selectedRows) => {
                        handleRowCheck(selectedRows);
                      },
                    },
                  }}
                />
              </ApiContainer>
            ),
          },
        ]}
      />
    </Drawer>
  );
};
