import { CloseOutlined } from '@ant-design/icons';
import { Radio, Tag } from 'antd';
import { FC } from 'react';
import { FilenetSelectedFiltersProps, SelectedFiltersType } from '../..';

import styles from './styles.module.scss';

export const FilenetSelectedFilters: FC<FilenetSelectedFiltersProps> = ({
  tableType,
  docTypeIdsSelector,
  mainTableSelector,
  onRadioChange,
  onClose,
  onCloseTypeId,
}) => (
  <div className={styles.additionalContainer}>
    <Radio.Group
      className={styles.dossierAndCardButtons}
      value={tableType}
      disabled={mainTableSelector.isPending}
      onChange={({ target: { value: selectedTableType } }) => {
        onRadioChange(selectedTableType);
      }}
    >
      <Radio.Button
        value="cards"
        key="cards-radio"
        className={styles.radioButton}
      >
        Карточки файлов
      </Radio.Button>
      <Radio.Button
        value="dossier"
        key="dossier-radio"
        className={styles.radioButton}
      >
        Досье проверки
      </Radio.Button>
    </Radio.Group>
    {(
      [
        'gibrSelectedFilter',
        'koSelectedFilter',
        'organizationsSelectedFilter',
        'territorialStructureSelectedFilter',
      ] as SelectedFiltersType[]
    ).map((filterType) => {
      if (mainTableSelector[filterType] === null) {
        return null;
      }

      const title = mainTableSelector[filterType]?.name || '';

      return (
        mainTableSelector[filterType] !== null && (
          <Tag
            closable
            color="#1890FF"
            key={filterType}
            className={styles.text}
            title={title}
            onClose={() => {
              onClose(filterType);
            }}
            closeIcon={<CloseOutlined className={styles.textCLoseButton} />}
          >
            {title}
          </Tag>
        )
      );
    })}
    {!!docTypeIdsSelector.length && (
      <div className={styles.additionalContainer}>
        {docTypeIdsSelector.map((docTypeId) => {
          const { label, name } = docTypeId;
          return (
            <Tag
              closable
              color="#1890FF"
              key={name}
              className={styles.text}
              title={name}
              onClose={() => onCloseTypeId(docTypeId)}
              closeIcon={<CloseOutlined className={styles.textCLoseButton} />}
            >
              {label}
            </Tag>
          );
        })}
      </div>
    )}
  </div>
);
