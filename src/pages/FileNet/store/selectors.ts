import { createSelector } from '@reduxjs/toolkit';
import { selectSelf } from 'shared/lib/selectSelf';
import { slice } from './reducer';

const selectSefFileNet = selectSelf(slice.name);

export const apiDataSelector = createSelector(
  selectSefFileNet,
  (state) => state.apiData,
);

export const docTypeIdsSelector = createSelector(
  apiDataSelector,
  (state) => state.docTypeIds,
);

export const unitIdsSelector = createSelector(
  apiDataSelector,
  (state) => state.unitIds,
);

export const tablesApiSelector = createSelector(
  selectSefFileNet,
  (state) => state.tables,
);

export const isCachedSelector = createSelector(
  tablesApiSelector,
  (state) => state.isCached,
);

export const tableFileStructureSelector = createSelector(
  tablesApiSelector,
  (state) => state.fileStructure,
);

export const mainTableSelector = createSelector(
  selectSefFileNet,
  (state) => state.mainTable,
);
