import type { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@reduxjs/toolkit';
import { TableRowData } from 'features/DataGrid';
import { fileStructureStore } from 'entities/FileStructure';

import type { FileNetInitialState, SelectedFiltersType } from '../types';
import { getFilesAndDossier } from './actions';

const initialState: FileNetInitialState = {
  apiData: {
    docTypeIds: [],
    unitIds: [],
  },
  /** Фильтры */
  tables: {
    isCached: false,
    fileStructure: { error: null, isPending: false, columns: [], rows: [] },
  },
  mainTable: {
    isAuditCards: false,
    territorialStructureSelectedFilter: null,
    gibrSelectedFilter: null,
    koSelectedFilter: null,
    organizationsSelectedFilter: null,
    auditColumns: [
      {
        title: 'Код проверки',
        dataIndex: 'title',
        key: 'title',
      },
      {
        title: 'ID досье',
        dataIndex: 'id',
        key: 'id',
      },
      {
        title: 'Опубликовано',
        dataIndex: 'publishedDatetime',
        key: 'publishedDatetime',
      },
      {
        title: 'Загрузка в КЗ ИД',
        dataIndex: 'uploadDatetime',
        key: 'uploadDatetime',
      },
      {
        title: 'МИ',
        dataIndex: 'MI',
        key: 'MI',
      },
      {
        title: 'КО-ПО',
        dataIndex: 'KO',
        key: 'KO',
      },
      {
        title: 'Регион',
        dataIndex: 'region',
        key: 'region',
      },
    ],
    fileColumns: [
      {
        title: 'Наименование файла',
        dataIndex: 'title',
        key: 'title',
      },
      {
        title: 'Полный путь',
        dataIndex: 'contentPath',
        key: 'contentPath',
      },
      {
        title: 'ID файла',
        dataIndex: 'id',
        key: 'id',
      },
      {
        title: 'Опубликовано',
        dataIndex: 'publishedDatetime',
        key: 'publishedDatetime',
      },
      {
        title: 'Загрузка в КЗ ИД',
        dataIndex: 'uploadDatetime',
        key: 'uploadDatetime',
      },
      {
        title: 'МИ',
        dataIndex: 'MI',
        key: 'MI',
      },
      {
        title: 'КО-ПО',
        dataIndex: 'KO',
        key: 'KO',
      },
      {
        title: 'Регион',
        dataIndex: 'region',
        key: 'region',
      },
    ],
    cards: {
      rows: [],
      pagination: { total: 0, currentPage: 1 },
    },
    dossier: {
      rows: [],
      pagination: { total: 0, currentPage: 1 },
    },
    error: null,
    isPending: false,
  },
};

export const slice = createSlice({
  name: 'fileNet',
  initialState,
  reducers: {
    setIsAuditCards: (state, { payload }: PayloadAction<boolean>) => {
      state.mainTable.isAuditCards = payload;
    },
    setTableIsCached: (state) => {
      state.tables.isCached = true;
    },
    setUnitIds: (
      state,
      { payload }: PayloadAction<typeof initialState.apiData.unitIds>,
    ) => {
      state.apiData.unitIds = payload;
    },
    setDocTypeIds: (
      state,
      { payload }: PayloadAction<typeof initialState.apiData.docTypeIds>,
    ) => {
      state.apiData.docTypeIds = payload;
    },

    setMainTableSelectedFilter: (
      state,
      {
        payload,
      }: PayloadAction<{
        type: SelectedFiltersType;
        value: TableRowData | null;
      }>,
    ) => {
      state.mainTable[payload.type] = payload.value;
    },
    handleClearFormData: (state) => {
      state.apiData = initialState.apiData;
      state.mainTable.gibrSelectedFilter = null;
      state.mainTable.koSelectedFilter = null;
      state.mainTable.territorialStructureSelectedFilter = null;
      state.mainTable.organizationsSelectedFilter = null;
    },
    setPage: (state, { payload }: PayloadAction<number>): void => {
      // Страница не может быть нулевой
      if (payload > 0) {
        if (state.mainTable.isAuditCards) {
          state.mainTable.dossier.pagination.currentPage = payload;
        } else {
          state.mainTable.cards.pagination.currentPage = payload;
        }
      }
    },
  },
  extraReducers: (builder) => {
    /* Парсер танков на получение фильтров */

    builder.addCase(
      fileStructureStore.actions.getFileStructureThunk.pending,
      (state) => {
        state.tables.fileStructure.isPending = true;
        state.tables.fileStructure.error = null;
      },
    );

    builder.addCase(
      fileStructureStore.actions.getFileStructureThunk.fulfilled,
      (state, { payload }) => {
        state.tables.fileStructure.isPending = false;
        state.tables.fileStructure.rows = payload.rows;
        state.tables.fileStructure.columns = payload.columns;
      },
    );

    builder.addCase(
      fileStructureStore.actions.getFileStructureThunk.rejected,
      (state, { error }) => {
        state.tables.fileStructure.isPending = false;
        state.tables.fileStructure.error = error;
      },
    );

    builder.addCase(getFilesAndDossier.pending, (state) => {
      state.mainTable.isPending = true;
      state.mainTable.error = null;
    });

    builder.addCase(getFilesAndDossier.fulfilled, (state, { payload }) => {
      if (payload.isAuditCards) {
        state.mainTable.dossier.rows = payload.rows;
        state.mainTable.dossier.pagination.total = payload.totalItems;
      } else {
        state.mainTable.cards.rows = payload.rows;
        state.mainTable.cards.pagination.total = payload.totalItems;
      }
      state.mainTable.isPending = false;
    });

    builder.addCase(getFilesAndDossier.rejected, (state, { error }) => {
      state.mainTable.isPending = false;
      state.mainTable.error = error;
    });
  },
});
