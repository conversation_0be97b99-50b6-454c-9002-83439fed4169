import { useEffect, useMemo, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import type { TableRowData } from 'features/DataGrid';
import type { PageParams, TableType } from '../..';

const TABLE_TYPE_QUERY = 'tableType';
const POPUP_ID_QUERY = 'popupId';

export const useQueryWithParams = (params?: {
  radioHandler?: (param: boolean) => void;
}): [PageParams, typeof handlers] => {
  /* ----------------------------------------------------
   *                      Хук
   ---------------------------------------------------- */

  /* Хук на параметры в запросе */
  const [searchParams, setSearchParams] = useSearchParams();

  const [pageParams, setPageParams] = useState<PageParams>({
    tableType: 'cards',
    popupId: null,
  });

  /* ----------------------------------------------------
   *                      Сайды
   ---------------------------------------------------- */

  /* Сайд на получение параметров */
  useEffect(() => {
    const tableType = searchParams.get(TABLE_TYPE_QUERY) as TableType;

    if (tableType && ['dossier', 'cards'].includes(tableType)) {
      const popupId = searchParams.get(POPUP_ID_QUERY);

      setPageParams((prev) => ({ ...prev, tableType, popupId }));
      if (params && params.radioHandler) {
        // isAuditCards true = Досье проверки
        params.radioHandler(tableType === 'dossier');
      }
    } else {
      setSearchParams({});
    }
  }, [searchParams]); // eslint-disable-line

  /* ----------------------------------------------------
   *                      Колбэки
   ---------------------------------------------------- */

  const onRowClick =
    (tableType: TableType) =>
    (row: TableRowData): void => {
      if (row.id) {
        setSearchParams({
          [TABLE_TYPE_QUERY]: tableType,
          [POPUP_ID_QUERY]: String(row.id),
        });
      }
    };

  const handlePopupClose = (): void => {
    searchParams.delete(POPUP_ID_QUERY);
    setSearchParams(searchParams);
  };

  const handleRadioClick = (tableType: TableType): void => {
    setSearchParams({ [TABLE_TYPE_QUERY]: tableType });
    setPageParams((prev) => {
      if (params && params.radioHandler) {
        // isAuditCards true = Досье проверки
        params.radioHandler(tableType === 'dossier');
      }

      return {
        ...prev,
        tableType,
      };
    });
  };

  /* ----------------------------------------------------
   *                      Мемоизация
   ---------------------------------------------------- */

  const handlers = useMemo(
    () => ({
      handleRadioClick,
      handlePopupClose,
      onRowClick,
    }),
    [searchParams], // eslint-disable-line
  );

  return [pageParams, handlers];
};
