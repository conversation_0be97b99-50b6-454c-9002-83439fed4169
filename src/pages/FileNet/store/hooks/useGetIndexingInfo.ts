import { useCallback, useEffect, useState } from 'react';
import { apiUrls, filenetServiceInstance } from 'shared/api';
import { appErrorNotification } from 'shared/lib';
import { useAxiosRequest } from 'shared/model';

interface DocumentStatistic {
  deletedCount: number;
  notContentIndexedCount: number;
  notIndexedCount: number;
  totalCount: number;
}

export interface IndexingInfo {
  documentStatistic: DocumentStatistic;
  enableContentIndexing: boolean;
  lastImportingDate: string;
}

export const useGetIndexingInfo = (): [typeof indexingInfo, Callback] => {
  const [data, setData] = useState<IndexingInfo>({
    lastImportingDate: '',
    enableContentIndexing: false,
    documentStatistic: {
      deletedCount: 0,
      notContentIndexedCount: 0,
      notIndexedCount: 0,
      totalCount: 0,
    },
  });

  const [getIndexingInfo, indexingInfo] = useAxiosRequest<IndexingInfo>(
    filenetServiceInstance,
  );

  const getInfo = useCallback(async () => {
    try {
      await getIndexingInfo(apiUrls.fileNet.filenetIndexingInfo).then((res) => {
        setData(res);
      });
    } catch (err) {
      appErrorNotification(
        'Произошла ошибка при получении информации о синхронизации',
        err as AppError,
      );
      throw err;
    }
  }, [getIndexingInfo]);

  useEffect(() => {
    getInfo();
  }, [getInfo]);

  return [{ ...indexingInfo, data }, getInfo];
};
