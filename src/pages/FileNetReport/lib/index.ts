import { createConfirmModal } from 'shared/model';

export const createModalButtonsList = (
  onBuild: Callback,
  onClear: Callback,
  onClose: Callback,
  isPending: boolean,
): AdditionalButton[] => [
  {
    key: 'build',
    title: 'Построить отчет',
    disabled: isPending,
    type: 'primary',
    onClick: onBuild,
  },
  {
    key: 'clear',
    title: 'Очистить',
    disabled: isPending,
    ghost: true,
    danger: true,
    onClick: createConfirmModal({
      isCallback: true,
      title: 'Очистить',
      message: 'Все введенные данные формы будут очищены!',
      onConfirm: onClear,
      isConfirmDanger: true,
      buttonNames: { confirm: 'Очистить' },
    }),
  },
  {
    key: 'close',
    title: 'Закрыть',
    disabled: isPending,
    type: 'primary',
    ghost: true,
    onClick: onClose,
  },
];
