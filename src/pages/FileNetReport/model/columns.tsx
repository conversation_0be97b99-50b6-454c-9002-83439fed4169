import {
  CheckCircleTwoTone,
  CloseCircleTwoTone,
  LoadingOutlined,
} from '@ant-design/icons';
import { Button, Popover } from 'antd';
import { TableColumnData } from 'features/DataGrid';
import { filenetServiceInstance } from 'shared/api';
import { downloadFileNetFile } from 'shared/lib';

export const columns: TableColumnData[] = [
  {
    dataIndex: 'date',
    title: 'Дата',
    width: '10%',
    key: 'date',
    ellipsis: false,
  },
  {
    dataIndex: 'name',
    title: 'Наименование',
    width: '20%',
    ellipsis: false,
    key: 'name',
  },
  {
    dataIndex: 'params',
    title: 'Параметры',
    width: '55%',
    ellipsis: false,
    key: 'params',
  },
  {
    dataIndex: 'status',
    title: 'Ссылка',
    key: 'status',
    width: '15%',
    ellipsis: false,
    render: (text, row) => {
      switch (row.status) {
        case 'READY':
          return (
            <Button
              type="link"
              download
              icon={<CheckCircleTwoTone twoToneColor="#52c41a" />}
              onClick={() => {
                filenetServiceInstance
                  .get<{ descriptorKey: string; filename: string }>(
                    `${filenetServiceInstance.defaults.baseURL}/report/do/download/${row.id}`,
                  )
                  .then(({ data: { descriptorKey, filename } }) =>
                    downloadFileNetFile(descriptorKey, filename),
                  );
              }}
            >
              Скачать
            </Button>
          );
        case 'ERROR':
          return (
            <span>
              <CloseCircleTwoTone twoToneColor="#ff7d7d" /> Во время выполнения
              произошла ошибка&nbsp;
              <Popover
                overlayStyle={{
                  width: '70vw',
                  height: 'min-content',
                  maxHeight: '70vh',
                  overflowY: 'auto',
                  overflowX: 'hidden',
                  paddingRight: '0',
                  boxShadow: '0 0 15px black',
                }}
                color="rgb(225,225,225)"
                placement="left"
                title="Стэк ошибки:"
                content={row.error}
                trigger="click"
              >
                <Button type="link" size="small">
                  Подробнее
                </Button>
              </Popover>
            </span>
          );
        case 'VALIDATE_ERROR':
          return (
            <span>
              <CloseCircleTwoTone twoToneColor="#ff7d7d" /> {row.error}
            </span>
          );
        default:
          return (
            <span>
              <LoadingOutlined /> Отчет формируется...
            </span>
          );
      }
    },
  },
];
