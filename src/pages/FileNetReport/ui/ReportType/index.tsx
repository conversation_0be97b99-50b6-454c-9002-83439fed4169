import { Radio, Tooltip } from 'antd';
import { FC, ReactElement } from 'react';
// eslint-disable-next-line import/no-internal-modules
import { Detailing, ReportFormVariant } from 'pages/FileNetReport/store/enums';
import { useAppSelector } from 'shared/model';
import { useCreateSliceActions } from 'shared/model/useCreateSliceActions';
import { InputRow } from 'shared/ui/InputRow';
import { enums, selectors, reducers } from '../../store';

interface ReportTypeProps {
  type: 'usersModal' | 'workModal';
}

export const ReportType: FC<ReportTypeProps> = ({ type }): ReactElement => {
  const reportFormVariant = useAppSelector(selectors.reportFormVariantSelector);
  const currentDetailngStatus = useAppSelector(
    selectors.currentDetailngStatusSelector,
  );
  const { setReportFormVariant } = useCreateSliceActions(
    reducers.slice.actions,
  );

  return (
    <InputRow title="Итоговая форма отчета">
      <Radio.Group
        buttonStyle="solid"
        value={reportFormVariant}
        onChange={(event) => setReportFormVariant(event.target.value)}
      >
        {Object.values(enums.ReportFormVariant).map((value) => {
          const isDisabled =
            currentDetailngStatus === Detailing.COUNT &&
            value === ReportFormVariant.XLSX_FLAT &&
            type === 'workModal';

          return (
            <Tooltip
              key={value}
              title={
                isDisabled
                  ? 'Для выбора этой формы отчета необходимо выбрать полную форму детализации отчета'
                  : ''
              }
            >
              <Radio.Button disabled={isDisabled} value={value}>
                {value}
              </Radio.Button>
            </Tooltip>
          );
        })}
      </Radio.Group>
    </InputRow>
  );
};
