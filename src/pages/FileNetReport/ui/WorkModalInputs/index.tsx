import { Checkbox, DatePicker, Radio, Row, Tooltip } from 'antd';
import moment from 'moment/moment';
import { FC, useMemo } from 'react';
// eslint-disable-next-line import/no-internal-modules
import { Detailing, ReportFormVariant } from 'pages/FileNetReport/store/enums';
import {
  DATE_VARIANTS_LIST,
  DEFAULT_DATE_VARIANT,
} from 'shared/config/constants';
import { useAppSelector } from 'shared/model';
import { useCreateSliceActions } from 'shared/model/useCreateSliceActions';
import { InputRow } from 'shared/ui/InputRow';

import { selectors, reducers, enums } from '../../store';

import styles from './styles.module.scss';

export const WorkModalInputs: FC = () => {
  /* ----------------------------------------------------
   *                      Селекторы
   ---------------------------------------------------- */
  const { isPending, periodFrom, periodTo, detailing, actionTypes } =
    useAppSelector(selectors.workModalApiSelector);
  const reportFormVariant = useAppSelector(selectors.reportFormVariantSelector);
  /* ----------------------------------------------------
   *                      Хуки
   ---------------------------------------------------- */
  const { setDetailingStatus, setWorkModalPeriod, setActionTypes } =
    useCreateSliceActions(reducers.slice.actions);

  /* ----------------------------------------------------
   *                      Memo
   ---------------------------------------------------- */

  /* Массив значений енама Экшен тайпа */
  const actionTypesValues = useMemo(() => Object.values(enums.ActionTypes), []);
  const fileActionTypesValues = useMemo(
    () => Object.values(enums.FileActionTypes),
    [],
  ) as unknown as enums.ActionTypes[];
  const documentActionTypesValues = useMemo(
    () => Object.values(enums.DocumentActionTypes),
    [],
  ) as unknown as enums.ActionTypes[];

  /* ----------------------------------------------------
   *                      Функции
   ---------------------------------------------------- */

  /* Функция для чека чекбоксов с индетерминейтом */
  const onCheckboxChange = (
    checked: boolean,
    value: enums.ActionTypes,
  ): void => {
    setActionTypes(
      checked
        ? [...actionTypes, value]
        : actionTypes.filter((item) => item !== value),
    );
  };

  /* Функция для рендера чекбоксов */
  const renderCheckboxes = (arr: typeof actionTypesValues): JSX.Element[] =>
    arr.map((value) => (
      <Checkbox
        key={value}
        checked={actionTypes.includes(value)}
        onChange={(event) => onCheckboxChange(event.target.checked, value)}
      >
        {value}
      </Checkbox>
    ));

  /* ----------------------------------------------------
   *                      UI
   ---------------------------------------------------- */

  return (
    <Row>
      <InputRow title="Действия над материалами">
        <div className={styles.checkboxGrid}>
          <div className={styles.checkboxColumn}>
            <Checkbox
              indeterminate={
                actionTypes.length > 0 &&
                actionTypes.length < actionTypesValues.length
              }
              checked={actionTypes.length === actionTypesValues.length}
              onChange={(event) =>
                setActionTypes(event.target.checked ? actionTypesValues : [])
              }
            >
              Все операции
            </Checkbox>
          </div>
          <div className={styles.checkboxColumn}>
            <Checkbox
              indeterminate={
                actionTypes.some((item) =>
                  fileActionTypesValues.includes(item),
                ) &&
                !fileActionTypesValues.every((item) =>
                  actionTypes.includes(item),
                )
              }
              checked={fileActionTypesValues.every((item) =>
                actionTypes.includes(item),
              )}
              onChange={(event) => {
                setActionTypes(
                  event.target.checked
                    ? [...new Set([...actionTypes, ...fileActionTypesValues])]
                    : actionTypes.filter(
                        (item) => !fileActionTypesValues.includes(item),
                      ),
                );
              }}
            >
              Действия с файлами
            </Checkbox>
            {renderCheckboxes(actionTypesValues.slice(0, 4))}
          </div>
          <div className={styles.checkboxColumn}>
            <Checkbox
              indeterminate={
                actionTypes.some((item) =>
                  documentActionTypesValues.includes(item),
                ) &&
                !documentActionTypesValues.every((item) =>
                  actionTypes.includes(item),
                )
              }
              checked={documentActionTypesValues.every((item) =>
                actionTypes.includes(item),
              )}
              onChange={(event) => {
                setActionTypes(
                  event.target.checked
                    ? [
                        ...new Set([
                          ...actionTypes,
                          ...documentActionTypesValues,
                        ]),
                      ]
                    : actionTypes.filter(
                        (item) => !documentActionTypesValues.includes(item),
                      ),
                );
              }}
            >
              Действия с карточками
            </Checkbox>
            {renderCheckboxes(actionTypesValues.slice(4, 6))}
          </div>
        </div>
      </InputRow>
      <InputRow title="За период">
        <DatePicker.RangePicker
          disabled={isPending}
          allowClear={false}
          className={styles.input}
          format={DATE_VARIANTS_LIST}
          onChange={(_, value) => setWorkModalPeriod(value)}
          value={[
            moment(periodFrom, DEFAULT_DATE_VARIANT),
            moment(periodTo, DEFAULT_DATE_VARIANT),
          ]}
        />
      </InputRow>
      <InputRow title="Детализация отчета">
        <Radio.Group
          value={detailing}
          buttonStyle="solid"
          onChange={(event) => setDetailingStatus(event.target.value)}
        >
          {Object.values(enums.Detailing).map((value) => {
            const isDisabled =
              reportFormVariant === ReportFormVariant.XLSX_FLAT &&
              value === Detailing.COUNT;

            return (
              <Tooltip
                title={
                  isDisabled
                    ? 'Для выбора этой формы детализации необходимо выбрать другую итоговую форму отчета'
                    : ''
                }
              >
                <Radio disabled={isDisabled} value={value} key={value}>
                  {value}
                </Radio>
              </Tooltip>
            );
          })}
        </Radio.Group>
      </InputRow>
    </Row>
  );
};
