import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Divider, StepProps, Steps } from 'antd';
import { FC, useState } from 'react';

import {
  createBasicClosableNotice,
  useAppDispatch,
  useAppSelector,
  useCreateSliceActions,
} from 'shared/model';
import { AppPopup } from 'shared/ui';

import { ButtonsContainer } from 'shared/ui/ButtonsContainer';

import { StepperItem } from '../..';
import { constants } from '../../config';
import { createModalButtonsList } from '../../lib';
import { selectors, reducers, actions } from '../../store';

import styles from './styles.module.scss';

/** Подкомпонент модалього окна работ */
export const WorkModal: FC<{ steps: StepperItem[] }> = ({ steps }) => {
  const isWorkModalOpen = useAppSelector(selectors.workModalSelector);
  const isPending = useAppSelector(selectors.workModalPendingSelector);
  const { closeWorkModal, clearWorkModal } = useCreateSliceActions(
    reducers.slice.actions,
  );
  const [current, setCurrent] = useState(0);

  const dispatch = useAppDispatch();
  const items = steps.map<StepProps>((step) => ({
    key: step.key,
    title: step.title,
  }));

  return (
    <AppPopup
      isOpened={isWorkModalOpen}
      onClose={closeWorkModal}
      title="Работа пользователей с ФХ"
      className={styles.popup}
    >
      <Steps current={current} size="small" items={items} />
      <Divider />

      <div className={styles.stepperContent}>
        {current >= 0 && current < steps.length && steps[current].element}
      </div>

      <div className={styles.steppinator}>
        {current > 0 && steps[current - 1] && (
          <Button
            icon={<LeftOutlined />}
            onClick={() => setCurrent((prev) => prev - 1)}
          >
            Настроить {steps[current - 1].title.toLowerCase()}
          </Button>
        )}
        {current >= 0 && current < steps.length && steps[current + 1] && (
          <Button
            icon={<RightOutlined />}
            type="primary"
            onClick={() => setCurrent((prev) => prev + 1)}
          >
            Настроить {steps[current + 1].title.toLowerCase()}
          </Button>
        )}
      </div>

      <Divider />
      <ButtonsContainer
        className={styles.buttons}
        buttons={createModalButtonsList(
          () =>
            dispatch(actions.postWorkFormThunk())
              .unwrap()
              .then(() => {
                clearWorkModal();
                setCurrent(0);
                createBasicClosableNotice({
                  description: constants.POPUP_DESCRIPTION,
                  message: constants.POPUP_MESSAGE,
                });
              }),
          () => {
            clearWorkModal();
            setCurrent(0);
          },
          closeWorkModal,
          isPending,
        )}
      />
    </AppPopup>
  );
};
