import { DeleteOutlined, FolderOutlined } from '@ant-design/icons';
import { Table } from 'antd';
import { FC, useMemo, useState } from 'react';
import {
  organizationalStructuresStore,
  OrgStructuresRows,
} from 'widgets/OrganizationalStructures';
import { DataGrid, TableRowData } from 'features/DataGrid';
import { getEnumKeyByEnumValue } from 'shared/lib';
import {
  createConfirmModal,
  useAppSelector,
  useCreateSliceActions,
} from 'shared/model';
import { deleteKeysFromObject } from 'shared/model/handleKeysFromObject';

import { reducers, selectors } from '../../store';

const ORG_STRUCTURE_TAB_NAME = 'tabName';

export const OrganizationalObjects: FC = () => {
  const [selected, setSelected] = useState<{
    keys: Key[];
    rows: TableRowData[];
  }>({ rows: [], keys: [] });
  const { openOrgStructure, deleteOrgStructuresRow } = useCreateSliceActions(
    reducers.slice.actions,
  );
  const { gibr, so, ts, ko } = useAppSelector(selectors.orgStructuresSelector);

  const rows = useMemo(() => {
    const orgRowsByIndex = [gibr, ko, so, ts];

    return [
      organizationalStructuresStore.enums.OrgStructureEnum.GIBR,
      organizationalStructuresStore.enums.OrgStructureEnum.KO,
      organizationalStructuresStore.enums.OrgStructureEnum.SO,
      organizationalStructuresStore.enums.OrgStructureEnum.TS,
    ].reduce((acc, tabName, index) => {
      if (orgRowsByIndex[index].length > 0) {
        orgRowsByIndex[index].forEach((orgRow) => {
          acc.push({
            title: tabName,
            tabName,
            ...(deleteKeysFromObject(orgRow, ['children']) as TableRowData),
          });
        });
      }

      return acc;
    }, [] as TableRowData[]);
  }, [gibr, so, ts, ko]);

  return (
    <DataGrid
      columns={[
        {
          dataIndex: ORG_STRUCTURE_TAB_NAME,
          key: ORG_STRUCTURE_TAB_NAME,
          title: 'Орг. структура',
          ellipsis: false,
        },
        {
          dataIndex: 'typeStr',
          key: 'typeStr',
          title: 'Тип орг. единицы',
          ellipsis: false,
        },
        {
          dataIndex: 'parentName',
          key: 'parentName',
          width: '20%',
          title: 'Объект верхнего уровня',
          ellipsis: false,
        },
        {
          dataIndex: 'name',
          key: 'name',
          width: '25%',
          title: 'Наименование',
          ellipsis: false,
        },
        {
          dataIndex: 'displayCode',
          key: 'displayCode',
          title: 'Номер',
          width: '15%',
          ellipsis: false,
        },
      ]}
      rows={rows}
      tableAdditionProps={{
        pagination: {
          hideOnSinglePage: true,
          showSizeChanger: false,
          pageSize: 10,
          position: ['bottomCenter'],
        },
        size: 'small',
        scroll: { x: '100%', y: 340 },
        bordered: true,
        rowSelection: {
          selections: [Table.SELECTION_ALL, Table.SELECTION_NONE],
          selectedRowKeys: selected.keys,
          onChange: (selectedRowKeys, selectedRows) => {
            setSelected({ rows: selectedRows, keys: selectedRowKeys });
          },
        },
      }}
      additionalButtons={[
        {
          icon: <FolderOutlined />,
          type: 'primary',
          title: 'Выбрать',
          key: 'select-sabah',
          onClick: openOrgStructure,
        },
        {
          icon: <DeleteOutlined />,
          type: 'primary',
          danger: true,
          ghost: true,
          title: 'Удалить выбранное',
          disabled: selected.rows.length === 0,
          tooltip: 'Необходимо выбрать элементы',
          key: 'delete-sabah',
          onClick: createConfirmModal({
            isCallback: true,
            title: 'Удаление',
            message: 'Вы дейстивтельно хотите удалить выбранные элементы?',
            isConfirmDanger: true,
            buttonNames: {
              confirm: 'Удалить',
            },
            onConfirm: () => {
              const rowsByOrgType = selected.rows.reduce((acc, row) => {
                const orgTypeKey = getEnumKeyByEnumValue(
                  organizationalStructuresStore.enums.OrgStructureEnum,
                  row[ORG_STRUCTURE_TAB_NAME],
                )?.toLowerCase();

                if (orgTypeKey) {
                  if (!acc[orgTypeKey]) {
                    acc[orgTypeKey] = [];
                  }
                  acc[orgTypeKey].push(row.id);
                }

                return acc;
              }, {} as Record<string, number[]>);

              Object.entries(rowsByOrgType).forEach(([orgType, ids]) => {
                deleteOrgStructuresRow({
                  orgType: orgType as keyof OrgStructuresRows,
                  ids,
                });
              });

              setSelected({ rows: [], keys: [] });
            },
          }),
        },
      ]}
    />
  );
};
