import { FC } from 'react';
import { OrganizationalStructures } from 'widgets/OrganizationalStructures';
import { useAppSelector, useCreateSliceActions } from 'shared/model';
import { AppPopup } from 'shared/ui';

import { selectors, reducers } from '../../store';

import styles from './styles.module.scss';

export const OrgStructureModal: FC = () => {
  const isOpened = useAppSelector(selectors.isOrgStructuresOpenSelector);
  const ids = useAppSelector(selectors.orgStructuresIdsSelector);
  const { closeOrgStructure, addOrgStructuresRows } = useCreateSliceActions(
    reducers.slice.actions,
  );

  return (
    <AppPopup
      isOpened={isOpened}
      onClose={closeOrgStructure}
      className={styles.popup}
      title="Организационные структуры"
    >
      <OrganizationalStructures
        rowIdsForFilter={ids}
        checkboxHandlers={{
          onAdd: (orgType, rows) => {
            addOrgStructuresRows({ orgType, rows });
            closeOrgStructure();
          },
        }}
        columnsProps={{ ellipsis: false }}
        tableAdditionProps={{
          size: 'small',
          bordered: true,
          scroll: { x: '100%', y: 244 },
          tableLayout: 'fixed',
          pagination: {
            position: ['bottomLeft'],
            hideOnSinglePage: true,
            showSizeChanger: false,
            pageSize: 6,
          },
        }}
      />
    </AppPopup>
  );
};
