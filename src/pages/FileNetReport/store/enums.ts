export enum ReportFormVariant {
  HTML = 'HTML',
  XLSX = 'XLS',
  XLSX_FLAT = 'XLS (плоский)',
  DOCX = 'DOC',
  RTF = 'RTF',
}

export enum UserStatus {
  ALL = 'Все пользователи',
  ACTIVE = 'Активные пользователи',
  BLOCKED = 'Неактивные пользователи',
}

export enum FileActionTypes {
  FILE_OPEN = 'Просмотр файла в визуализаторе',
  FILE_DOWNLOAD = 'Выгрузка файла',
  FILE_PRINT = 'Печать файла',
  FILE_COPY = 'Копирование фрагмента текста',
}

export enum DocumentActionTypes {
  DOCUMENT_OPEN = 'Просмотр карточки материала',
  AUDIT_DOCUMENT_OPEN = 'Просмотр досье проверки',
}

export enum ActionTypes {
  FILE_OPEN = FileActionTypes.FILE_OPEN,
  FILE_DOWNLOAD = FileActionTypes.FILE_DOWNLOAD,
  FILE_PRINT = FileActionTypes.FILE_PRINT,
  FILE_COPY = FileActionTypes.FILE_COPY,

  DOCUMENT_OPEN = DocumentActionTypes.DOCUMENT_OPEN,
  AUDIT_DOCUMENT_OPEN = DocumentActionTypes.AUDIT_DOCUMENT_OPEN,
}

export enum Detailing {
  FULL = 'Полная',
  COUNT = 'Количественные показатели',
}
