import { createSelector } from '@reduxjs/toolkit';
import { selectSelf } from 'shared/lib';

export const selectSelfFNR = selectSelf('fileNetReport');

export const userModalSelector = createSelector(
  selectSelfFNR,
  (state) => state.isUsersModalOpen,
);
export const workModalSelector = createSelector(
  selectSelfFNR,
  (state) => state.isWorkModalOpen,
);
export const reportFormVariantSelector = createSelector(
  selectSelfFNR,
  (state) => state.reportFormVariant,
);
export const userModalApiSelector = createSelector(
  selectSelfFNR,
  (state) => state.userModalApi,
);

export const userModalPendingSelector = createSelector(
  userModalApiSelector,
  (state) => state.isPending,
);

export const workModalApiSelector = createSelector(
  selectSelfFNR,
  (state) => state.workModalApi,
);

export const workModalPendingSelector = createSelector(
  workModalApiSelector,
  (state) => state.isPending,
);

export const materialIdenticatorSelector = createSelector(
  selectSelfFNR,
  (state) => state.materialIdenticator,
);

export const fileTypesSelector = createSelector(
  selectSelfFNR,
  (state) => state.fileTypes,
);

export const fileTypesSelectedRowsSelector = createSelector(
  fileTypesSelector,
  (state) => state.selectedRows,
);

export const orgStructuresSelector = createSelector(
  selectSelfFNR,
  (state) => state.orgStructures,
);

export const orgStructuresIdsSelector = createSelector(
  selectSelfFNR,
  (state) => state.orgStructures.ids,
);

export const isOrgStructuresOpenSelector = createSelector(
  orgStructuresSelector,
  (state) => state.isOpened,
);

export const currentDetailngStatusSelector = createSelector(
  selectSelfFNR,
  (state) => state.workModalApi.detailing,
);
