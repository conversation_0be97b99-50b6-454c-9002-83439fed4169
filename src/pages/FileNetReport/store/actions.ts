import { createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

import { apiUrls, filenetServiceInstance } from 'shared/api';
import { getEnumKeyByEnumValue } from 'shared/lib/getEnumKeyByValue';
import { ErrorWithoutShow } from 'shared/model';
import { pickKeysFromObject } from 'shared/model/handleKeysFromObject';

import { IdenticatorItem } from './types';
import { enums } from '.';

export const getMaterialIdsThunk = createAsyncThunk<IdenticatorItem, number>(
  'fileNetReport/getMaterialIdsThunk',
  async (id, { rejectWithValue }) => {
    try {
      const {
        data: { title, type },
      } = await filenetServiceInstance.get<{ title: string; type: string }>(
        apiUrls.fileNet.report.materialIdentification(id),
      );

      return { title, id, type, key: id };
    } catch (err) {
      if (axios.isAxiosError(err)) {
        return rejectWithValue(err);
      }

      throw err;
    }
  },
  {
    serializeError() {
      return new ErrorWithoutShow();
    },
  },
);

export const postUserFormThunk = createAsyncThunk<
  void,
  undefined,
  {
    state: import('processes/store').RootState;
  }
>(
  'fileNetReport/postUserFormThunk',
  async (_, { getState, rejectWithValue }) => {
    try {
      const { userModalApi, reportFormVariant } = getState().fileNetReport;

      await filenetServiceInstance.post(apiUrls.fileNet.report.postUserForm, {
        ...pickKeysFromObject(userModalApi, ['periodFrom', 'periodTo']),
        userStatus: getEnumKeyByEnumValue(
          enums.UserStatus,
          userModalApi.userStatus,
        ),
        format: getEnumKeyByEnumValue(
          enums.ReportFormVariant,
          reportFormVariant,
        ),
      });

      return undefined;
    } catch (err) {
      if (axios.isAxiosError(err)) {
        return rejectWithValue(err);
      }

      throw err;
    }
  },
);

export const postWorkFormThunk = createAsyncThunk<
  void,
  undefined,
  {
    state: import('processes/store').RootState;
  }
>(
  'fileNetReport/postWorkFormThunk',
  async (_, { getState, rejectWithValue }) => {
    try {
      const {
        workModalApi,
        reportFormVariant,
        materialIdenticator: { ids: materials },
        fileTypes: { selectedKeys: rubrics },
        orgStructures: { ids: organizationUnits },
      } = getState().fileNetReport;

      await filenetServiceInstance.post(apiUrls.fileNet.report.postWorkForm, {
        ...pickKeysFromObject(workModalApi, ['periodFrom', 'periodTo']),
        detailing: getEnumKeyByEnumValue(
          enums.Detailing,
          workModalApi.detailing,
        ),
        actionTypes: workModalApi.actionTypes.map((item) =>
          getEnumKeyByEnumValue(enums.ActionTypes, item),
        ),

        format: getEnumKeyByEnumValue(
          enums.ReportFormVariant,
          reportFormVariant,
        ),
        ...(materials.length > 0 && { materials }),
        ...(rubrics.length > 0 && { rubrics }),
        ...(organizationUnits.length > 0 && { organizationUnits }),
      });

      return undefined;
    } catch (err) {
      if (axios.isAxiosError(err)) {
        return rejectWithValue(err);
      }

      throw err;
    }
  },
);
