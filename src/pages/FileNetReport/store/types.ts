import { OrgStructuresRows } from 'widgets/OrganizationalStructures';
import { TableRowData } from 'features/DataGrid';
import { ActionTypes, Detailing, ReportFormVariant, UserStatus } from './enums';

export interface FNRData {
  date: string;
  error: null | string;
  id: number;
  name: string;
  params: string;
  status: string;
  userName: string;
}

export interface FNRResponse {
  data: FNRData[];
  page: number;
  rows: number;
  totalPages: number;
  totalRecords: number;
}

/** Элемент идентификатора */
export interface IdenticatorItem {
  id: number;
  key: Key;
  title: string;
  type: string;
}

export interface FNRInitialState {
  fileTypes: {
    isOpened: boolean;
    selectedKeys: Key[];
    selectedRows: TableRowData[];
  };
  isUsersModalOpen: boolean;
  isWorkModalOpen: boolean;
  materialIdenticator: ApiDefaultKeys & {
    ids: number[];
    items: IdenticatorItem[];
  };
  orgStructures: {
    ids: number[];
    isOpened: boolean;
  } & OrgStructuresRows;
  reportFormVariant: ReportFormVariant;
  userModalApi: ApiDefaultKeys & {
    periodFrom: string;
    periodTo: string;
    userStatus: UserStatus;
  };
  workModalApi: ApiDefaultKeys & {
    actionTypes: ActionTypes[];
    detailing: Detailing;
    periodFrom: string;
    periodTo: string;
  };
}

/*
 * Интерфейс для степпера модального окна настройки отчетов
 */
export interface StepperItem {
  /** Эллемент который будет отрисован на конкретном шаге */
  element: JSX.Element;
  key: Key;
  title: Title;
}
