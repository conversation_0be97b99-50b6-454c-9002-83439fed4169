/** Fix для Lazy load, рендерит только дефольные импорты */
// eslint-disable-next-line no-restricted-exports
export { FileNetReport as default } from './ui';

export * as fileNetReportModel from './model';
export * as fileNetReportConfig from './config';
export * as fileNetReportStore from './store';

// Fix для типов
// eslint-disable-next-line import/no-internal-modules
export * from './store/types';
