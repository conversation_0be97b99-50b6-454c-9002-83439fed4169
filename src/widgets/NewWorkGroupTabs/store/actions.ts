import { createAsyncThunk } from '@reduxjs/toolkit';
import axios, { AxiosError } from 'axios';
import { NestedTabsWithLazyTable } from 'features/DataGrid';
import { appInstance } from 'shared/api';
import { generateUrlWithQueryParams } from 'shared/lib';
import { getTabRequestParams } from '../lib';
import { TabType } from '../types';
import { slice } from './reducer';

// ===== hasNested Tabs Actions =====

// Action для загрузки hasNested табов
export const loadHasNestedTabs = createAsyncThunk<
  NestedTabsWithLazyTable,
  { endpoint: string; rowId: string; cabinetId?: string },
  { rejectValue: AxiosError }
>(
  'tabs/loadHasNestedTabs',
  async ({ endpoint, rowId, cabinetId }, { rejectWithValue }) => {
    try {
      // Формируем URL для загрузки табов
      const tabsUrl = `${endpoint}/tabs`;
      const params: Record<string, string> = {};

      // Определяем параметры в зависимости от эндпоинта
      if (endpoint === 'krg3_request_notice' && rowId) {
        params.cabinetId = cabinetId || '';
        params.requestNoticeId = rowId;
      } else {
        params.rowId = rowId;
      }

      const url = generateUrlWithQueryParams(tabsUrl, params);
      const { data } = await appInstance.post<NestedTabsWithLazyTable>(url);

      return data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(error);
      }
      throw error;
    }
  },
);

// Action для перезагрузки hasNested табов
export const refetchHasNestedTabs = createAsyncThunk<
  NestedTabsWithLazyTable,
  { endpoint: string; rowId: string; cabinetId?: string },
  { rejectValue: AxiosError }
>(
  'tabs/refetchHasNestedTabs',
  async ({ endpoint, rowId, cabinetId }, { rejectWithValue }) => {
    try {
      // Формируем URL для загрузки табов
      const tabsUrl = `${endpoint}/tabs`;
      const params: Record<string, string> = {};

      // Определяем параметры в зависимости от эндпоинта
      if (endpoint === 'krg3_request_notice' && rowId) {
        params.cabinetId = cabinetId || '';
        params.requestNoticeId = rowId;
      } else {
        params.rowId = rowId;
      }

      const url = generateUrlWithQueryParams(tabsUrl, params);
      const { data } = await appInstance.post<NestedTabsWithLazyTable>(url);

      return data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(error);
      }
      throw error;
    }
  },
);

export const refetchRowTabs = createAsyncThunk<
  void,
  {
    endpoint: string;
    getNestedData: (page?: number) => void;
    page: number;
    rowId: string;
    cabinetId?: string;
  },
  { rejectValue: AxiosError }
>(
  'tabs/refetchRowTabs',
  async (
    { rowId, endpoint, cabinetId, getNestedData, page },
    { dispatch, rejectWithValue },
  ) => {
    try {
      // 1. Инвалидируем табы для конкретной строки
      dispatch(slice.actions.invalidateTabsForRow({ rowId }));

      // 2. Параллельно запускаем обновление таблицы и табов
      const promises: Promise<any>[] = [
        // Обновляем таблицу
        new Promise<void>((resolve) => {
          getNestedData(page);
          resolve();
        }),
      ];

      // Если есть hasNested табы для этой строки, перезагружаем их
      promises.push(
        dispatch(refetchHasNestedTabs({ endpoint, rowId, cabinetId })).unwrap(),
      );

      // Ждем завершения всех операций
      await Promise.all(promises);

      return Promise.resolve();
    } catch (error) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(error);
      }
      throw error;
    }
  },
);

// ===== Lazy Tabs Actions =====

interface FetchTabDataParams {
  endpoint: string;
  page: number;
  parentRowId: string;
  tabKey: string;
}

export const fetchTabData = createAsyncThunk<
  TableColumnsAndRowsWithPagination,
  FetchTabDataParams,
  { rejectValue: AxiosError }
>(
  'lazyTabs/fetchTabData',
  async ({ parentRowId, endpoint, page }, { rejectWithValue }) => {
    try {
      const params = getTabRequestParams(endpoint, parentRowId, page);
      const url = generateUrlWithQueryParams(endpoint, params);

      const { data } = await appInstance.get<TableColumnsAndRowsWithPagination>(
        url,
      );

      return data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(error);
      }
      throw error;
    }
  },
  {
    condition: ({ parentRowId, tabKey }, { getState }) => {
      const state = getState() as {
        newWorkGroupTabs: {
          lazyTabs: {
            loadingStates: Record<string, { isLoading: boolean }>;
          };
        };
      };

      const key = `${parentRowId}-${tabKey}`;
      const loadingState = state.newWorkGroupTabs.lazyTabs.loadingStates[key];

      // Не выполняем запрос если уже загружается
      return !loadingState?.isLoading;
    },
  },
);

// ===== Refetch Actions =====

// Интерфейс для параметров рефетча
interface RefetchParams {
  tabType: TabType;
  cabinetId?: string;
  endpoint?: string;
  rowId?: string;
  tabKey?: string;
}

// Универсальный action для рефетча табов
export const refetchTabs = createAsyncThunk<
  void,
  RefetchParams,
  { rejectValue: AxiosError }
>(
  'tabs/refetchTabs',
  async (
    { tabType, rowId, tabKey, endpoint, cabinetId },
    { dispatch, rejectWithValue },
  ) => {
    try {
      switch (tabType) {
        case TabType.EAGER:
          // Для жадных табов ничего не делаем - они обновляются при полной перезагрузке таблицы
          break;

        case TabType.HAS_NESTED:
          // Для hasNested табов перезагружаем табы для конкретной строки
          if (rowId && endpoint) {
            await dispatch(
              refetchHasNestedTabs({ cabinetId, endpoint, rowId }),
            );
          }
          break;

        case TabType.LAZY:
          // Для ленивых табов перезагружаем конкретный таб
          if (rowId && tabKey && endpoint) {
            await dispatch(
              fetchTabData({
                endpoint,
                page: 1,
                parentRowId: rowId,
                tabKey,
              }),
            );
          }
          break;

        default:
          throw new Error('Неизвестный тип таба');
      }

      return Promise.resolve();
    } catch (error) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(error);
      }
      throw error;
    }
  },
);

// Action для рефетча при действиях пользователя (удаление, отметка основным)
export const refetchAfterUserAction = createAsyncThunk<
  void,
  {
    actionType: 'delete' | 'markMain' | 'updateCount';
    affectedRows: Array<{
      rowId: string;
      tabType: TabType;
      cabinetId?: string;
      endpoint?: string;
      tabKey?: string;
    }>;
  },
  { rejectValue: AxiosError }
>('tabs/refetchAfterUserAction', async ({ affectedRows }, { dispatch }) => {
  // Группируем строки по типу таба для оптимизации
  const groupedByType = affectedRows.reduce((acc, row) => {
    if (!acc[row.tabType]) {
      acc[row.tabType] = [];
    }
    acc[row.tabType].push(row);
    return acc;
  }, {} as Record<TabType, typeof affectedRows>);

  // Обрабатываем каждый тип табов
  const refetchPromises = Object.entries(groupedByType).map(([tabType, rows]) =>
    Promise.all(
      rows.map((row) =>
        dispatch(
          refetchTabs({
            cabinetId: row.cabinetId,
            endpoint: row.endpoint,
            rowId: row.rowId,
            tabKey: row.tabKey,
            tabType: tabType as TabType,
          }),
        ),
      ),
    ),
  );

  await Promise.all(refetchPromises);
});
