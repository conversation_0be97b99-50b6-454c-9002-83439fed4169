import { PayloadAction } from '@reduxjs/toolkit';
import { TabsState } from '../../types';

export const mainTabsReducers = {
  setMainTab: (
    state: TabsState,
    { payload }: PayloadAction<{ key: string; endpoint: string }>
  ) => {
    const { key, endpoint } = payload;
    state.mainTabs.activeKey = key;
    state.mainTabs.endpoint = endpoint;
  },

  resetMainTab: (state: TabsState) => {
    state.mainTabs.activeKey = '0';
    state.mainTabs.endpoint = '';
  },
};
