import { PayloadAction } from '@reduxjs/toolkit';
import { NestedTabsWithLazyTable } from 'features/DataGrid';
import { TabsState } from '../../types';

export const hasNestedTabsReducers = {
  setHasNestedTabsData: (
    state: TabsState,
    { payload }: PayloadAction<{
      rowId: string;
      tabsData: NestedTabsWithLazyTable;
    }>
  ) => {
    const { rowId, tabsData } = payload;
    state.hasNestedTabs.tabsData[rowId] = tabsData;
    state.hasNestedTabs.loadingStates[rowId] = {
      isLoading: false,
      error: undefined,
    };
  },

  setActiveHasNestedKeys: (
    state: TabsState,
    { payload }: PayloadAction<{ rowId: string; keys: string[] }>
  ) => {
    const { rowId, keys } = payload;
    state.hasNestedTabs.activeKeys[rowId] = keys;
  },

  setHasNestedLoading: (
    state: TabsState,
    { payload }: PayloadAction<{ rowId: string; isLoading: boolean }>
  ) => {
    const { rowId, isLoading } = payload;
    state.hasNestedTabs.loadingStates[rowId] = {
      isLoading,
      error: undefined,
    };
  },

  setHasNestedError: (
    state: TabsState,
    { payload }: PayloadAction<{ rowId: string; error: string }>
  ) => {
    const { rowId, error } = payload;
    state.hasNestedTabs.loadingStates[rowId] = {
      isLoading: false,
      error,
    };
  },

  clearHasNestedTabsForRow: (
    state: TabsState,
    { payload }: PayloadAction<{ rowId: string }>
  ) => {
    const { rowId } = payload;
    delete state.hasNestedTabs.tabsData[rowId];
    delete state.hasNestedTabs.activeKeys[rowId];
    delete state.hasNestedTabs.loadingStates[rowId];
  },

  clearAllHasNestedTabs: (state: TabsState) => {
    state.hasNestedTabs.tabsData = {};
    state.hasNestedTabs.activeKeys = {};
    state.hasNestedTabs.loadingStates = {};
  },
};
