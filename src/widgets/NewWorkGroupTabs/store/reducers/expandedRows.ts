import { PayloadAction } from '@reduxjs/toolkit';
import { TabsState } from '../../types';

export const expandedRowsReducers = {
  setExpandedRows: (
    state: TabsState,
    { payload }: PayloadAction<{ keys: string[] }>
  ) => {
    const { keys } = payload;
    state.expandedRows.keys = keys;
  },

  toggleExpandedRow: (
    state: TabsState,
    { payload }: PayloadAction<{ key: string }>
  ) => {
    const { key } = payload;
    const index = state.expandedRows.keys.indexOf(key);
    
    if (index === -1) {
      state.expandedRows.keys.push(key);
    } else {
      state.expandedRows.keys.splice(index, 1);
    }
  },

  clearExpandedRows: (state: TabsState) => {
    state.expandedRows.keys = [];
  },
};
