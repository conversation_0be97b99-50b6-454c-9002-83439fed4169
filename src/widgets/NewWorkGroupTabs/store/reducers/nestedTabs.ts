import { PayloadAction } from '@reduxjs/toolkit';
import { TabsState } from '../../types';

export const nestedTabsReducers = {
  setNestedTab: (
    state: TabsState,
    { payload }: PayloadAction<{ key: string; endpoint: string }>
  ) => {
    const { key, endpoint } = payload;
    state.nestedTabs.activeKey = key;
    state.nestedTabs.endpoint = endpoint;
  },

  resetNestedTab: (state: TabsState) => {
    state.nestedTabs.activeKey = '0';
    state.nestedTabs.endpoint = '';
  },
};
