import { PayloadAction } from '@reduxjs/toolkit';
import { TabsWithLazyTable } from 'features/DataGrid';
import { LazyTabsState } from '../../types';

export const lazyTabsReducers = {
  setActiveTabKeys: (
    state: LazyTabsState,
    { payload }: PayloadAction<{ keys: string[], rowId: string; }>
  ) => {
    const { rowId, keys } = payload;
    state.activeTabKeys[rowId] = keys;
  },
  
  initializeTabs: (
    state: LazyTabsState,
    { payload }: PayloadAction<{
      parentRowId: string;
      tabs: TabsWithLazyTable[];
    }>
  ) => {
    const { parentRowId, tabs } = payload;
    
    tabs.forEach(tab => {
      const tabKey = `${parentRowId}-${tab.key}`;
      
      // Инициализируем только если таба еще нет
      if (!state.tabsData[tabKey]) {
        state.tabsData[tabKey] = {
          ...tab,
          tabStatus: tab.tableData ? {
            isLoading: false,
            isLoaded: true,
            isError: false,
          } : {
            isLoading: false,
            isLoaded: false,
            isError: false,
          },
        };
        
        state.loadingStates[tabKey] = {
          isLoading: false,
          isLoaded: !!tab.tableData,
          isError: false,
        };
      }
    });
  },
  
  clearTabsData: (
    state: LazyTabsState,
    action: PayloadAction<{ parentRowId: string }>,
  ) => {
    const { parentRowId } = action.payload;
    
    // Удаляем данные всех табов для данной строки
    Object.keys(state.tabsData).forEach(key => {
      if (key.startsWith(`${parentRowId}-`)) {
        delete state.tabsData[key];
        delete state.loadingStates[key];
      }
    });
    
    delete state.activeTabKeys[parentRowId];
  },
  clearAllTabsData: (state: LazyTabsState) => {
    state.tabsData = {};
    state.loadingStates = {};
    state.activeTabKeys = {};
  },
};