import { PayloadAction } from '@reduxjs/toolkit';
import { TabsWithLazyTable } from 'features/DataGrid';
import { TabsState } from '../../types';

export const lazyTabsReducers = {
  setActiveTabKeys: (
    state: TabsState,
    { payload }: PayloadAction<{ keys: string[], rowId: string; }>
  ) => {
    const { rowId, keys } = payload;
    state.lazyTabs.activeTabKeys[rowId] = keys;
  },

  initializeTabs: (
    state: TabsState,
    { payload }: PayloadAction<{
      parentRowId: string;
      tabs: TabsWithLazyTable[];
    }>
  ) => {
    const { parentRowId, tabs } = payload;

    tabs.forEach(tab => {
      const tabKey = `${parentRowId}-${tab.key}`;

      // Инициализируем только если таба еще нет
      if (!state.lazyTabs.tabsData[tabKey]) {
        state.lazyTabs.tabsData[tabKey] = {
          ...tab,
          tabStatus: tab.tableData ? {
            isLoading: false,
            isLoaded: true,
            isError: false,
          } : {
            isLoading: false,
            isLoaded: false,
            isError: false,
          },
        };

        state.lazyTabs.loadingStates[tabKey] = {
          isLoading: false,
          isLoaded: !!tab.tableData,
          isError: false,
        };
      }
    });
  },

  clearTabsData: (
    state: TabsState,
    action: PayloadAction<{ parentRowId: string }>,
  ) => {
    const { parentRowId } = action.payload;

    // Удаляем данные всех табов для данной строки
    Object.keys(state.lazyTabs.tabsData).forEach(key => {
      if (key.startsWith(`${parentRowId}-`)) {
        delete state.lazyTabs.tabsData[key];
        delete state.lazyTabs.loadingStates[key];
      }
    });

    delete state.lazyTabs.activeTabKeys[parentRowId];
  },
  clearAllTabsData: (state: TabsState) => {
    state.lazyTabs.tabsData = {};
    state.lazyTabs.loadingStates = {};
    state.lazyTabs.activeTabKeys = {};
  },
};
