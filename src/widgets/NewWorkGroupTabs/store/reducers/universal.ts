import { TabsState } from '../../types';

export const universalReducers = {
  resetAllTabsState: (state: TabsState) => {
    // Сброс основных табов
    state.mainTabs.activeKey = '0';
    state.mainTabs.endpoint = '';
    
    // Сброс nested табов
    state.nestedTabs.activeKey = '0';
    state.nestedTabs.endpoint = '';
    
    // Закрытие всех expandable строк
    state.expandedRows.keys = [];
    
    // Очистка всех ленивых табов
    state.lazyTabs.activeTabKeys = {};
    state.lazyTabs.loadingStates = {};
    state.lazyTabs.tabsData = {};
    
    // Очистка всех hasNested табов
    state.hasNestedTabs.tabsData = {};
    state.hasNestedTabs.activeKeys = {};
    state.hasNestedTabs.loadingStates = {};
  },

  resetTabsForPagination: (state: TabsState) => {
    // При пагинации сбрасываем только состояние табов, но не основные вкладки
    
    // Закрытие всех expandable строк
    state.expandedRows.keys = [];
    
    // Очистка всех ленивых табов
    state.lazyTabs.activeTabKeys = {};
    state.lazyTabs.loadingStates = {};
    state.lazyTabs.tabsData = {};
    
    // Очистка всех hasNested табов
    state.hasNestedTabs.tabsData = {};
    state.hasNestedTabs.activeKeys = {};
    state.hasNestedTabs.loadingStates = {};
  },
};
