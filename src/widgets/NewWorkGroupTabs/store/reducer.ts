import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import axios from 'axios';
import { NestedTabsWithLazyTable, TabsWithLazyTable } from 'features/DataGrid';
import { TabsState } from '../types';
import {
  loadHasNestedTabs,
  refetchHasNestedTabs,
  fetchTabData,
} from './actions';

// ===== Initial State =====

const initialState: TabsState = {
  expandedRows: {
    keys: [],
  },
  hasNestedTabs: {
    activeKeys: {},
    loadingStates: {},
    tabsData: {},
  },
  lazyTabs: {
    activeTabKeys: {},
    loadingStates: {},
    tabsData: {},
  },
  mainTabs: {
    activeKey: '0',
    endpoint: '',
  },
  nestedTabs: {
    activeKey: '0',
    endpoint: '',
  },
};

export const slice = createSlice({
  name: 'newWorkGroupTabs',
  initialState,
  reducers: {
    // ===== Main Tabs Reducers =====
    setMainTab: (
      state: TabsState,
      { payload }: PayloadAction<{ endpoint: string; key: string }>,
    ) => {
      const { key, endpoint } = payload;
      state.mainTabs.activeKey = key;
      state.mainTabs.endpoint = endpoint;
    },

    resetMainTab: (state: TabsState) => {
      state.mainTabs.activeKey = '0';
      state.mainTabs.endpoint = '';
    },

    // ===== Nested Tabs Reducers =====
    setNestedTab: (
      state: TabsState,
      { payload }: PayloadAction<{ endpoint: string; key: string }>,
    ) => {
      const { key, endpoint } = payload;
      state.nestedTabs.activeKey = key;
      state.nestedTabs.endpoint = endpoint;
    },

    resetNestedTab: (state: TabsState) => {
      state.nestedTabs.activeKey = '0';
      state.nestedTabs.endpoint = '';
    },

    // ===== Expanded Rows Reducers =====
    setExpandedRowKeys: (
      state: TabsState,
      { payload }: PayloadAction<{ keys: React.Key[] }>,
    ) => {
      const { keys } = payload;
      state.expandedRows.keys = keys.map(String);
    },

    clearExpandedRows: (state: TabsState) => {
      state.expandedRows.keys = [];
    },

    // ===== Lazy Tabs Reducers =====
    setActiveTabKeys: (
      state: TabsState,
      { payload }: PayloadAction<{ keys: string[]; rowId: string }>,
    ) => {
      const { rowId, keys } = payload;
      state.lazyTabs.activeTabKeys[rowId] = keys;
    },

    initializeTabs: (
      state: TabsState,
      {
        payload,
      }: PayloadAction<{
        parentRowId: string;
        tabs: TabsWithLazyTable[];
      }>,
    ) => {
      const { parentRowId, tabs } = payload;

      tabs.forEach((tab) => {
        const tabKey = `${parentRowId}-${tab.key}`;

        // Инициализируем только если таба еще нет
        if (!state.lazyTabs.tabsData[tabKey]) {
          state.lazyTabs.tabsData[tabKey] = {
            ...tab,
            tabStatus: tab.tableData
              ? {
                  isLoading: false,
                  isLoaded: true,
                  isError: false,
                }
              : {
                  isLoading: false,
                  isLoaded: false,
                  isError: false,
                },
          };

          state.lazyTabs.loadingStates[tabKey] = {
            isLoading: false,
            isLoaded: !!tab.tableData,
            isError: false,
          };
        }
      });
    },

    clearLazyTabsForRow: (
      state: TabsState,
      { payload }: PayloadAction<{ rowId: string }>,
    ) => {
      const { rowId } = payload;

      // Удаляем все табы для данной строки
      Object.keys(state.lazyTabs.tabsData).forEach((key) => {
        if (key.startsWith(`${rowId}-`)) {
          delete state.lazyTabs.tabsData[key];
          delete state.lazyTabs.loadingStates[key];
        }
      });

      delete state.lazyTabs.activeTabKeys[rowId];
    },

    clearAllLazyTabs: (state: TabsState) => {
      state.lazyTabs.activeTabKeys = {};
      state.lazyTabs.loadingStates = {};
      state.lazyTabs.tabsData = {};
    },

    // ===== HasNested Tabs Reducers =====
    setHasNestedTabsData: (
      state: TabsState,
      {
        payload,
      }: PayloadAction<{
        rowId: string;
        tabsData: NestedTabsWithLazyTable;
      }>,
    ) => {
      const { rowId, tabsData } = payload;
      state.hasNestedTabs.tabsData[rowId] = tabsData;
      state.hasNestedTabs.loadingStates[rowId] = {
        isLoading: false,
        error: undefined,
      };
    },

    setActiveHasNestedKeys: (
      state: TabsState,
      { payload }: PayloadAction<{ keys: string[]; rowId: string }>,
    ) => {
      const { rowId, keys } = payload;
      state.hasNestedTabs.activeKeys[rowId] = keys;
    },

    setHasNestedLoading: (
      state: TabsState,
      { payload }: PayloadAction<{ isLoading: boolean; rowId: string }>,
    ) => {
      const { rowId, isLoading } = payload;
      state.hasNestedTabs.loadingStates[rowId] = {
        isLoading,
        error: undefined,
      };
    },

    setHasNestedError: (
      state: TabsState,
      { payload }: PayloadAction<{ error: string; rowId: string }>,
    ) => {
      const { rowId, error } = payload;
      state.hasNestedTabs.loadingStates[rowId] = {
        isLoading: false,
        error,
      };
    },

    clearHasNestedTabsForRow: (
      state: TabsState,
      { payload }: PayloadAction<{ rowId: string }>,
    ) => {
      const { rowId } = payload;
      delete state.hasNestedTabs.tabsData[rowId];
      delete state.hasNestedTabs.activeKeys[rowId];
      delete state.hasNestedTabs.loadingStates[rowId];
    },

    clearAllHasNestedTabs: (state: TabsState) => {
      state.hasNestedTabs.tabsData = {};
      state.hasNestedTabs.activeKeys = {};
      state.hasNestedTabs.loadingStates = {};
    },

    // ===== Universal Reducers =====
    resetAllTabsState: (state: TabsState) => {
      // Сброс основных табов
      state.mainTabs.activeKey = '0';
      state.mainTabs.endpoint = '';

      // Сброс nested табов
      state.nestedTabs.activeKey = '0';
      state.nestedTabs.endpoint = '';

      // Закрытие всех expandable строк
      state.expandedRows.keys = [];

      // Очистка всех ленивых табов
      state.lazyTabs.activeTabKeys = {};
      state.lazyTabs.loadingStates = {};
      state.lazyTabs.tabsData = {};

      // Очистка всех hasNested табов
      state.hasNestedTabs.tabsData = {};
      state.hasNestedTabs.activeKeys = {};
      state.hasNestedTabs.loadingStates = {};
    },
  },
  extraReducers: (builder) => {
    // ===== HasNested Tabs Extra Reducers =====
    builder
      // Загрузка hasNested табов
      .addCase(loadHasNestedTabs.pending, (state, action) => {
        const { rowId } = action.meta.arg;
        state.hasNestedTabs.loadingStates[rowId] = {
          isLoading: true,
          error: undefined,
        };
      })
      .addCase(loadHasNestedTabs.fulfilled, (state, action) => {
        const { rowId } = action.meta.arg;
        const tabsData = action.payload;

        state.hasNestedTabs.tabsData[rowId] = tabsData;
        state.hasNestedTabs.loadingStates[rowId] = {
          isLoading: false,
          error: undefined,
        };

        // Инициализируем активные ключи пустым массивом если их нет
        if (!state.hasNestedTabs.activeKeys[rowId]) {
          state.hasNestedTabs.activeKeys[rowId] = [];
        }
      })
      .addCase(loadHasNestedTabs.rejected, (state, action) => {
        const { rowId } = action.meta.arg;
        const errorMessage = axios.isAxiosError(action.payload)
          ? `HTTP ${action.payload.response?.status || 'ошибка'}: ${
              action.payload.message
            }`
          : action.error?.message || 'Ошибка загрузки';

        state.hasNestedTabs.loadingStates[rowId] = {
          isLoading: false,
          error: errorMessage,
        };
      })

      // Перезагрузка hasNested табов
      .addCase(refetchHasNestedTabs.pending, (state, action) => {
        const { rowId } = action.meta.arg;
        state.hasNestedTabs.loadingStates[rowId] = {
          isLoading: true,
          error: undefined,
        };
      })
      .addCase(refetchHasNestedTabs.fulfilled, (state, action) => {
        const { rowId } = action.meta.arg;
        const tabsData = action.payload;

        state.hasNestedTabs.tabsData[rowId] = tabsData;
        state.hasNestedTabs.loadingStates[rowId] = {
          isLoading: false,
          error: undefined,
        };
      })
      .addCase(refetchHasNestedTabs.rejected, (state, action) => {
        const { rowId } = action.meta.arg;
        const errorMessage = axios.isAxiosError(action.payload)
          ? `HTTP ${action.payload.response?.status || 'ошибка'}: ${
              action.payload.message
            }`
          : action.error?.message || 'Ошибка перезагрузки';

        state.hasNestedTabs.loadingStates[rowId] = {
          isLoading: false,
          error: errorMessage,
        };
      })

      // ===== Lazy Tabs Extra Reducers =====
      .addCase(fetchTabData.pending, (state, action) => {
        const { parentRowId, tabKey } = action.meta.arg;
        const key = `${parentRowId}-${tabKey}`;

        state.lazyTabs.loadingStates[key] = {
          isLoading: true,
          isLoaded: false,
          isError: false,
        };

        if (state.lazyTabs.tabsData[key]) {
          state.lazyTabs.tabsData[key].tabStatus = {
            isLoading: true,
            isLoaded: false,
            isError: false,
          };
        }
      })
      .addCase(fetchTabData.fulfilled, (state, action) => {
        const { parentRowId, tabKey, page } = action.meta.arg;
        const key = `${parentRowId}-${tabKey}`;
        const result = action.payload;

        state.lazyTabs.loadingStates[key] = {
          isLoading: false,
          isLoaded: true,
          isError: false,
        };

        if (state.lazyTabs.tabsData[key]) {
          state.lazyTabs.tabsData[key] = {
            ...state.lazyTabs.tabsData[key],
            tableData: result,
            pagination: {
              currentPage: page,
              pageSize: result?.pagination?.pageSize || 10,
              total: result?.pagination?.total || result?.rows?.length || 0,
            },
            tabStatus: {
              isLoading: false,
              isLoaded: true,
              isError: false,
            },
          };
        } else {
          // Если таб еще не инициализирован, создаем его
          state.lazyTabs.tabsData[key] = {
            key: tabKey,
            label: '',
            endpoint: action.meta.arg.endpoint,
            tableData: result,
            pagination: {
              currentPage: page,
              pageSize: result?.pagination?.pageSize || 10,
              total: result?.pagination?.total || result?.rows?.length || 0,
            },
            tabStatus: {
              isLoading: false,
              isLoaded: true,
              isError: false,
            },
          };
        }
      })
      .addCase(fetchTabData.rejected, (state, action) => {
        const { parentRowId, tabKey } = action.meta.arg;
        const key = `${parentRowId}-${tabKey}`;

        const errorMessage = axios.isAxiosError(action.payload)
          ? `HTTP ${action.payload.response?.status || 'ошибка'}: ${
              action.payload.message
            }`
          : action.error?.message || 'Ошибка загрузки данных таба';

        state.lazyTabs.loadingStates[key] = {
          isLoading: false,
          isLoaded: false,
          isError: true,
          error: errorMessage,
        };

        if (state.lazyTabs.tabsData[key]) {
          state.lazyTabs.tabsData[key].tabStatus = {
            isLoading: false,
            isLoaded: false,
            isError: true,
          };
        }
      });
  },
});
