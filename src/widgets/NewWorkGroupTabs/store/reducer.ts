import { createSlice } from '@reduxjs/toolkit';
import * as extraReducers from './extraReducers';
import { initialState } from './initialState';
import * as reducers from './reducers';

export const slice = createSlice({
  name: 'newWorkGroupTabs',
  initialState,
  reducers: {
    ...reducers.mainTabsReducers,
    ...reducers.nestedTabsReducers,
    ...reducers.expandedRowsReducers,
    ...reducers.lazyTabsReducers,
    ...reducers.hasNestedTabsReducers,
    ...reducers.universalReducers,
  },
  extraReducers: (builder) => {
    extraReducers.lazyTabsExtraReducers(builder);
    extraReducers.hasNestedTabsExtraReducers(builder);
  },
});
