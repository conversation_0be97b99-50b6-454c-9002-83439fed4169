import { createSelector } from '@reduxjs/toolkit';
import { selectSelf } from 'shared/lib';
import { TabData } from '../../types';

interface RootState {
  newWorkGroupTabs: {
    mainTabs: {
      activeKey: string;
      endpoint: string;
    };
    nestedTabs: {
      activeKey: string;
      endpoint: string;
    };
    expandedRows: {
      keys: string[];
    };
    lazyTabs: {
      activeTabKeys: Record<string, string[]>;
      loadingStates: Record<string, {
        isError: boolean;
        isLoaded: boolean;
        isLoading: boolean;
      }>;
      tabsData: Record<string, TabData>;
    };
    hasNestedTabs: {
      tabsData: Record<string, any>;
      activeKeys: Record<string, string[]>;
      loadingStates: Record<string, { isLoading: boolean; error?: string }>;
    };
  };
}

// Базовые селекторы
export const tabsSelector = selectSelf('newWorkGroupTabs');
export const lazyTabsSelector = createSelector(
  tabsSelector,
  (state) => state?.lazyTabs
);

// Селекторы для доступа к частям состояния
export const selectTabsData = createSelector(
  lazyTabsSelector,
  (state) => state?.tabsData || {}
);

export const selectLoadingStates = createSelector(
  lazyTabsSelector,
  (state) => state?.loadingStates || {}
);

export const selectActiveTabKeys = createSelector(
  lazyTabsSelector,
  (state) => state?.activeTabKeys || {}
);

export const selectTabDataByKey = createSelector(
  [
    selectTabsData,
    (_state: RootState, parentRowId: string, tabKey: string) =>
      `${parentRowId}-${tabKey}`,
  ],
  (tabsData, key) => tabsData[key],
);

export const selectTabLoadingState = createSelector(
  [
    selectLoadingStates,
    (_state: RootState, parentRowId: string, tabKey: string) =>
      `${parentRowId}-${tabKey}`,
  ],
  (loadingStates, key) =>
    loadingStates[key] || { isLoading: false, isLoaded: false, isError: false },
);

export const selectActiveTabKeysForRow = createSelector(
  [selectActiveTabKeys, (_state: RootState, rowId: string) => rowId],
  (activeTabKeys, rowId) => activeTabKeys[rowId] || [],
);

export const selectTabsForRow = createSelector(
  [selectTabsData, (_state: RootState, parentRowId: string) => parentRowId],
  (tabsData, parentRowId) => {
    const tabs: TabData[] = [];

    Object.entries(tabsData).forEach(([key, tab]) => {
      if (key.startsWith(`${parentRowId}-`)) {
        tabs.push(tab);
      }
    });

    return tabs;
  },
);
