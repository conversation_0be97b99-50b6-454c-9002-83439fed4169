import { createSelector } from '@reduxjs/toolkit';
import { selectSelf } from 'shared/lib';

// Базовый селектор
export const tabsSelector = selectSelf('newWorkGroupTabs');

// Селекторы для основных табов
export const selectMainTabsState = createSelector(
  tabsSelector,
  (state) => state?.mainTabs || { activeKey: '0', endpoint: '' }
);

export const selectMainActiveKey = createSelector(
  selectMainTabsState,
  (mainTabs) => mainTabs.activeKey
);

export const selectMainEndpoint = createSelector(
  selectMainTabsState,
  (mainTabs) => mainTabs.endpoint
);
