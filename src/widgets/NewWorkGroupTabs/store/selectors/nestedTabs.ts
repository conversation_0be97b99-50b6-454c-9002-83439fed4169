import { createSelector } from '@reduxjs/toolkit';
import { selectSelf } from 'shared/lib';

// Базовый селектор
const tabsSelector = selectSelf('newWorkGroupTabs');

// Селекторы для nested табов
export const selectNestedTabsState = createSelector(
  tabsSelector,
  (state) => state?.nestedTabs || { activeKey: '0', endpoint: '' }
);

export const selectNestedActiveKey = createSelector(
  selectNestedTabsState,
  (nestedTabs) => nestedTabs.activeKey
);

export const selectNestedEndpoint = createSelector(
  selectNestedTabsState,
  (nestedTabs) => nestedTabs.endpoint
);
