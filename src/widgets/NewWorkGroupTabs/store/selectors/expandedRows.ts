import { createSelector } from '@reduxjs/toolkit';
import { selectSelf } from 'shared/lib';

// Базовый селектор
const tabsSelector = selectSelf('newWorkGroupTabs');

// Селекторы для expandable строк
export const selectExpandedRowsState = createSelector(
  tabsSelector,
  (state) => state?.expandedRows || { keys: [] }
);

export const selectExpandedRowKeys = createSelector(
  selectExpandedRowsState,
  (expandedRows) => expandedRows.keys
);
