import { createSelector } from '@reduxjs/toolkit';
import { NestedTabsWithLazyTable } from 'features/DataGrid';
import { selectSelf } from 'shared/lib';

// Базовый селектор
const tabsSelector = selectSelf('newWorkGroupTabs');

// Селекторы для hasNested табов
export const selectHasNestedTabsState = createSelector(
  tabsSelector,
  (state) => state?.hasNestedTabs || { tabsData: {}, activeKeys: {}, loadingStates: {} }
);

export const selectHasNestedTabsForRow = createSelector(
  [selectHasNestedTabsState, (state: any, rowId: string) => rowId],
  (hasNestedTabs, rowId): NestedTabsWithLazyTable | null =>
    hasNestedTabs.tabsData[rowId] || null
);

export const selectActiveHasNestedKeys = createSelector(
  [selectHasNestedTabsState, (state: any, rowId: string) => rowId],
  (hasNestedTabs, rowId): string[] =>
    hasNestedTabs.activeKeys[rowId] || []
);

export const selectHasNestedLoadingState = createSelector(
  [selectHasNestedTabsState, (state: any, rowId: string) => rowId],
  (hasNestedTabs, rowId) =>
    hasNestedTabs.loadingStates[rowId] || { isLoading: false, error: undefined }
);
