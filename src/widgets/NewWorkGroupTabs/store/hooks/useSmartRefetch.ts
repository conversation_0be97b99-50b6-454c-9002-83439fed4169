import { useCallback } from 'react';
import { TableRowData } from 'features/DataGrid';
import { useAppDispatch } from 'shared/model';
import { TabType } from '../../types';
import { refetchAfterUserAction } from '../actions';
import { slice } from '../reducer';
import { useTabTypeDetection } from './useTabTypeDetection';

export const useSmartRefetch = (
  activeEndpoint: string,
  getTableData: (page?: number) => void,
  currentPage: number,
  cabinetId?: string,
): {
  refetchAfterDelete: (rows: TableRowData[]) => Promise<void>;
  refetchAfterMarkMain: (row: TableRowData) => Promise<void>;
  refetchAfterUpdateCount: (rows: TableRowData[]) => Promise<void>;
  refetchOnPagination: () => void;
} => {
  const dispatch = useAppDispatch();
  const { detectMultipleTabTypes } = useTabTypeDetection();

  // Общая функция для обработки рефетча
  const handleRefetch = useCallback(
    async (
      rows: TableRowData[],
      actionType: 'delete' | 'markMain' | 'updateCount',
    ): Promise<void> => {
      const tabTypeInfos = detectMultipleTabTypes(rows, activeEndpoint);

      // Проверяем, есть ли жадные табы - если да, нужна полная перезагрузка
      const hasEagerTabs = tabTypeInfos.some(
        (info) => info.tabType === TabType.EAGER,
      );

      if (hasEagerTabs) {
        // Полная перезагрузка таблицы для жадных табов
        getTableData(currentPage);
      } else {
        // Умный рефетч для других типов табов
        await dispatch(
          refetchAfterUserAction({
            actionType,
            affectedRows: tabTypeInfos.map((info) => ({
              cabinetId,
              endpoint: info.endpoint,
              rowId: info.rowId,
              tabKey: info.tabKey,
              tabType: info.tabType,
            })),
          }),
        );
      }
    },
    [
      dispatch,
      detectMultipleTabTypes,
      activeEndpoint,
      getTableData,
      currentPage,
      cabinetId,
    ],
  );

  const refetchAfterDelete = useCallback(
    async (deletedRows: TableRowData[]) => {
      await handleRefetch(deletedRows, 'delete');
    },
    [handleRefetch],
  );

  const refetchAfterMarkMain = useCallback(
    async (affectedRow: TableRowData) => {
      await handleRefetch([affectedRow], 'markMain');
    },
    [handleRefetch],
  );

  const refetchAfterUpdateCount = useCallback(
    async (affectedRows: TableRowData[]) => {
      await handleRefetch(affectedRows, 'updateCount');
    },
    [handleRefetch],
  );

  const refetchOnPagination = useCallback(() => {
    // При пагинации всегда сбрасываем состояние всех табов
    dispatch(slice.actions.resetAllTabsState());
  }, [dispatch]);

  return {
    refetchAfterDelete,
    refetchAfterMarkMain,
    refetchAfterUpdateCount,
    refetchOnPagination,
  };
};
