import { useCallback } from 'react';
import { TableRowData } from 'features/DataGrid';
import { useAppDispatch } from 'shared/model';
import { TabType } from '../../types';
import { refetchAfterUserAction } from '../actions';
import { useTabTypeDetection } from './useTabTypeDetection';

export const useSmartRefetch = (
  activeEndpoint: string,
  getTableData: (page?: number) => void,
  getNestedData: (page?: number) => void,
  currentPage: number,
  cabinetId?: string
): {
  refetchAfterDelete: (rows: TableRowData[]) => Promise<void>;
  refetchAfterMarkMain: (row: TableRowData) => Promise<void>;
  refetchAfterUpdateCount: (rows: TableRowData[]) => Promise<void>;
  refetchOnPagination: () => void;
} => {
  const dispatch = useAppDispatch();
  const { detectMultipleTabTypes } = useTabTypeDetection();

  const refetchAfterDelete = useCallback(async (
    deletedRows: TableRowData[]
  ) => {
    const tabTypeInfos = detectMultipleTabTypes(deletedRows, activeEndpoint);

    // Проверяем, есть ли жадные табы - если да, нужна полная перезагрузка
    const hasEagerTabs = tabTypeInfos.some(info => info.tabType === TabType.EAGER);

    if (hasEagerTabs) {
      // Полная перезагрузка таблицы для жадных табов
      getTableData(currentPage);
    } else {
      // Умный рефетч для других типов табов
      await dispatch(refetchAfterUserAction({
        actionType: 'delete',
        affectedRows: tabTypeInfos.map(info => ({
          cabinetId,
          endpoint: info.endpoint,
          rowId: info.rowId,
          tabKey: info.tabKey,
          tabType: info.tabType,
        })),
      }));
    }
  }, [dispatch, detectMultipleTabTypes, activeEndpoint, getTableData, currentPage, cabinetId]);

  const refetchAfterMarkMain = useCallback(async (
    affectedRow: TableRowData
  ) => {
    const tabTypeInfo = detectMultipleTabTypes([affectedRow], activeEndpoint)[0];

    if (tabTypeInfo.tabType === TabType.EAGER) {
      // Полная перезагрузка таблицы для жадных табов
      getTableData(currentPage);
    } else {
      // Умный рефетч для других типов табов
      await dispatch(refetchAfterUserAction({
        actionType: 'markMain',
        affectedRows: [{
          cabinetId,
          endpoint: tabTypeInfo.endpoint,
          rowId: tabTypeInfo.rowId,
          tabKey: tabTypeInfo.tabKey,
          tabType: tabTypeInfo.tabType,
        }],
      }));
    }
  }, [dispatch, detectMultipleTabTypes, activeEndpoint, getTableData, currentPage, cabinetId]);

  const refetchAfterUpdateCount = useCallback(async (
    affectedRows: TableRowData[]
  ) => {
    const tabTypeInfos = detectMultipleTabTypes(affectedRows, activeEndpoint);

    // Проверяем, есть ли жадные табы
    const hasEagerTabs = tabTypeInfos.some(info => info.tabType === TabType.EAGER);

    if (hasEagerTabs) {
      // Полная перезагрузка таблицы для жадных табов
      getTableData(currentPage);
    } else {
      // Умный рефетч для других типов табов
      await dispatch(refetchAfterUserAction({
        actionType: 'updateCount',
        affectedRows: tabTypeInfos.map(info => ({
          cabinetId,
          endpoint: info.endpoint,
          rowId: info.rowId,
          tabKey: info.tabKey,
          tabType: info.tabType,
        })),
      }));
    }
  }, [dispatch, detectMultipleTabTypes, activeEndpoint, getTableData, currentPage, cabinetId]);

  const refetchOnPagination = useCallback(() => {
    // При пагинации всегда сбрасываем состояние всех табов
    dispatch({ type: 'newWorkGroupTabs/resetTabsForPagination' });
  }, [dispatch]);

  return {
    refetchAfterDelete,
    refetchAfterMarkMain,
    refetchAfterUpdateCount,
    refetchOnPagination,
  };
};
