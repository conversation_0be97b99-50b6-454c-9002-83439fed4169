import { useCallback } from 'react';
import { useAppDispatch } from 'shared/model';
import { TableRowData } from 'features/DataGrid';
import { TabType } from '../../types';
import { refetchAfterUserAction } from '../actions';
import { useTabTypeDetection } from './useTabTypeDetection';

export const useSmartRefetch = (
  activeEndpoint: string,
  getTableData: (page?: number) => void,
  getNestedData: (page?: number) => void,
  currentPage: number
) => {
  const dispatch = useAppDispatch();
  const { detectMultipleTabTypes } = useTabTypeDetection();

  const refetchAfterDelete = useCallback(async (
    deletedRows: TableRowData[]
  ) => {
    const tabTypeInfos = detectMultipleTabTypes(deletedRows, activeEndpoint);
    
    // Проверяем, есть ли жадные табы - если да, нужна полная перезагрузка
    const hasEagerTabs = tabTypeInfos.some(info => info.tabType === TabType.EAGER);
    
    if (hasEagerTabs) {
      // Полная перезагрузка таблицы для жадных табов
      getTableData(currentPage);
    } else {
      // Умный рефетч для других типов табов
      await dispatch(refetchAfterUserAction({
        actionType: 'delete',
        affectedRows: tabTypeInfos.map(info => ({
          rowId: info.rowId,
          tabType: info.tabType,
          endpoint: info.endpoint,
          tabKey: info.tabKey,
        })),
      }));
    }
  }, [dispatch, detectMultipleTabTypes, activeEndpoint, getTableData, currentPage]);

  const refetchAfterMarkMain = useCallback(async (
    affectedRow: TableRowData
  ) => {
    const tabTypeInfo = detectMultipleTabTypes([affectedRow], activeEndpoint)[0];
    
    if (tabTypeInfo.tabType === TabType.EAGER) {
      // Полная перезагрузка таблицы для жадных табов
      getTableData(currentPage);
    } else {
      // Умный рефетч для других типов табов
      await dispatch(refetchAfterUserAction({
        actionType: 'markMain',
        affectedRows: [{
          rowId: tabTypeInfo.rowId,
          tabType: tabTypeInfo.tabType,
          endpoint: tabTypeInfo.endpoint,
          tabKey: tabTypeInfo.tabKey,
        }],
      }));
    }
  }, [dispatch, detectMultipleTabTypes, activeEndpoint, getTableData, currentPage]);

  const refetchAfterUpdateCount = useCallback(async (
    affectedRows: TableRowData[]
  ) => {
    const tabTypeInfos = detectMultipleTabTypes(affectedRows, activeEndpoint);
    
    // Проверяем, есть ли жадные табы
    const hasEagerTabs = tabTypeInfos.some(info => info.tabType === TabType.EAGER);
    
    if (hasEagerTabs) {
      // Полная перезагрузка таблицы для жадных табов
      getTableData(currentPage);
    } else {
      // Умный рефетч для других типов табов
      await dispatch(refetchAfterUserAction({
        actionType: 'updateCount',
        affectedRows: tabTypeInfos.map(info => ({
          rowId: info.rowId,
          tabType: info.tabType,
          endpoint: info.endpoint,
          tabKey: info.tabKey,
        })),
      }));
    }
  }, [dispatch, detectMultipleTabTypes, activeEndpoint, getTableData, currentPage]);

  const refetchOnPagination = useCallback(() => {
    // При пагинации всегда сбрасываем состояние всех табов
    dispatch({ type: 'newWorkGroupTabs/resetTabsForPagination' });
  }, [dispatch]);

  return {
    refetchAfterDelete,
    refetchAfterMarkMain,
    refetchAfterUpdateCount,
    refetchOnPagination,
  };
};
