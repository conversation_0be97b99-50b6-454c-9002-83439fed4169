import { UseLazyTabsDataReturn } from 'widgets/NewWorkGroupTabs';
import { TabsWithLazyTable } from 'features/DataGrid';
import { useLazyTabsRedux } from './useLazyTabsRedux';

interface UseLazyTabsDataOptions {
  activeTabKeys?: string[];
  onActiveTabKeysChange?: (keys: string[]) => void;
}

// Временная обертка для обратной совместимости
// Использует Redux версию внутри
export const useLazyTabsData = (
  initialTabs: TabsWithLazyTable[],
  parentRowId: string | number | undefined,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _options?: UseLazyTabsDataOptions,
): UseLazyTabsDataReturn =>
  // Игнорируем options, так как Redux управляет состоянием
  useLazyTabsRedux(initialTabs, parentRowId);
