import {
  CopyOutlined,
  DeleteTwoTone,
  DiffOutlined,
  DownloadOutlined,
  EditOutlined,
  EyeOutlined,
  FileExclamationOutlined,
  SafetyOutlined,
  SnippetsOutlined,
} from '@ant-design/icons';
import { notification } from 'antd';
import {
  HandleRow,
  NWGConfig,
  NWGStore,
  TogglePopup,
  Permissions,
  SmartRefetch,
} from 'widgets/NewWorkGroupTabs';
import { TableRowData } from 'features/DataGrid';
import { createInputsModal } from 'features/InputsModal';
import { permissionsConfig, permissionsStore } from 'entities/Permissions';
import { apiUrls, appInstance } from 'shared/api';
import {
  FileDeletionHintsByBindingKeys,
  fileDeletionHintsByBinding,
} from 'shared/config';
import {
  appErrorNotification,
  generateUrlWithQueryParams,
  getDurationAsString,
} from 'shared/lib';
import { createConfirmModal } from 'shared/model';

const DISABLE_KEY = 'disabled';

const handleSetStatus = async (
  statusKeyNew: string,
  refetch: Callback,
  row: TableRowData,
): Promise<void> => {
  const res = await appInstance.post<{
    messages: { message: string; type: string }[];
    success: boolean;
  }>(apiUrls.workGroup.request.switchCollapseItem, {
    ids: [row.rowId?.id as string],
    statusKeyNew,
  });

  if (res.data.success) {
    notification.success({
      message: `Статус пункта № ${row.number} успешно изменен`,
    });
    refetch();
  } else {
    notification.warning({ message: res.data.messages[0].message });
  }
};

const handleUnlinkDef = (fileId: string): Promise<void> =>
  appInstance.post(
    generateUrlWithQueryParams(apiUrls.workGroup.packageDef.unlinkDefFile, {
      fileId,
    }),
  );

const AT_EDIT_PROFILE = 'EDIT_PROFILE_AT';
const AT_VIEW_PROFILE = 'VIEW_PROFILE_AT';
const AT_EDIT_LIST = 'EDIT_LIST_AT';
const AT_DOWNLOAD_FILES = 'DOWNLOAD_FILES_AT';
const AT_DELETE_FILES = 'DELETE_FILES_AT';

export const useTableSingleButtons = (
  row: TableRowData,
  refetch: Callback,
  handleRow: HandleRow,
  togglePopup: TogglePopup,
  tableEndpoint: Endpoint,
  permissions: Permissions,
  isRequest?: boolean,
  smartRefetch?: SmartRefetch,
): [
  AdditionalButton[],
  AdditionalButton[],
  AdditionalButton[],
  AdditionalButton[],
  AdditionalButton[],
  AdditionalButton[],
  AdditionalButton[],
  AdditionalButton[],
  AdditionalButton[],
] => {
  const [getFile, getFileViewerLink] = NWGStore.hooks.useFilesActions();
  const isDisabled = (key: string): boolean => Boolean(!row?.rowId?.[key]);

  const canEdit = {
    epc: permissions.ap_ListEPC.includes(
      permissionsStore.enums.Actions.EDIT_LIST_AT,
    ),
    defInventory: permissions.ap_ListDEF.includes(
      permissionsStore.enums.Actions.EDIT_LIST_AT,
    ),
  };

  const havePermissionsByKey = (
    actionKey: keyof typeof permissionsStore.enums.Actions,
  ): boolean => {
    if (tableEndpoint === apiUrls.workGroup.nestedTabsEndpoints.request) {
      return permissions.ap_RequestNoticeWG.includes(
        permissionsStore.enums.Actions[actionKey],
      );
    }

    if (tableEndpoint === apiUrls.workGroup.nestedTabsEndpoints.packageDef) {
      return permissions.ap_PackageDEF.includes(
        permissionsStore.enums.Actions[actionKey],
      );
    }

    if (tableEndpoint === apiUrls.workGroup.nestedTabsEndpoints.internal) {
      return permissions.ap_InternalWGO.includes(
        permissionsStore.enums.Actions[actionKey],
      );
    }

    if (tableEndpoint === apiUrls.workGroup.nestedTabsEndpoints.fileData) {
      return permissions.ap_FilesWGO.includes(
        permissionsStore.enums.Actions[actionKey],
      );
    }

    return false;
  };

  const downloadButtons: AdditionalButton[] = [
    {
      title: '',
      key: havePermissionsByKey(AT_DOWNLOAD_FILES) ? '1' : DISABLE_KEY,
      icon: <DownloadOutlined />,
      disabled: isDisabled('canDownload'),
      tooltip: isDisabled('canDownload')
        ? 'Нет прав для скачивания файла'
        : 'Скачать файл',
      tooltipProps: {
        placement: 'topRight',
      },
      onClick: () => {
        if (row?.rowId?.id) {
          getFile(apiUrls.workGroup.fileData.downloadFileById(row.rowId.id));
        }
      },
    },
    {
      title: '',
      key:
        !isDisabled('isFileSignExist') &&
        havePermissionsByKey(AT_DOWNLOAD_FILES)
          ? '2'
          : DISABLE_KEY,
      icon: <SafetyOutlined />,
      tooltipProps: {
        placement: 'topRight',
      },
      disabled: isDisabled('canDownload'),
      tooltip: isDisabled('canDownload')
        ? 'Нет прав для скачивания файла'
        : 'Скачать файл с подписью',
      onClick: () => {
        if (row?.rowId?.id) {
          getFile(
            apiUrls.workGroup.fileData.downloadFileByIdWithSign(row.rowId.id),
          );
        }
      },
    },
    {
      title: '',
      key: havePermissionsByKey('VIEW_FILES_AT') ? '3' : DISABLE_KEY,
      icon: <EyeOutlined />,
      tooltipProps: {
        placement: 'topRight',
      },
      disabled: isDisabled('canView'),
      tooltip: isDisabled('canView')
        ? 'Нет прав для просмотра в визуализаторе'
        : 'Открыть в визуализаторе',
      onClick: () => {
        if (row?.rowId?.id) {
          getFileViewerLink(row.rowId.id);
        }
      },
    },
  ];

  const isRequestOrInternal =
    {
      [apiUrls.workGroup.nestedTabsEndpoints.request]: true,
      [apiUrls.workGroup.nestedTabsEndpoints.internal]: true,
    }[tableEndpoint] || false;

  const deletePermissonKey = ({
    [apiUrls.workGroup.nestedTabsEndpoints.fileData]: AT_DELETE_FILES,
    [apiUrls.workGroup.nestedTabsEndpoints.request]: AT_EDIT_PROFILE,
    [apiUrls.workGroup.nestedTabsEndpoints.internal]: AT_EDIT_PROFILE,
  }[tableEndpoint] ||
    AT_EDIT_PROFILE) as keyof typeof permissionsStore.enums.Actions;

  const deleteButtons: AdditionalButton[] = [
    {
      title: '',
      tooltipProps: {
        placement: 'topRight',
      },
      icon: (
        <DeleteTwoTone
          twoToneColor={
            !row.rowId?.isRemovable || !havePermissionsByKey(deletePermissonKey)
              ? '#ABABAB'
              : '#FF4D4F'
          }
        />
      ),
      key: '1',
      disabled:
        isDisabled('isRemovable') || !havePermissionsByKey(deletePermissonKey),
      tooltip: !havePermissionsByKey(deletePermissonKey)
        ? 'Нет прав на удаление'
        : isDisabled('isRemovable')
        ? Object.keys(fileDeletionHintsByBinding)
            .reduce(
              (acc, key) => {
                if (key in (row?.rowId || {}) && (row?.rowId || {})[key]) {
                  return [
                    ...acc,
                    fileDeletionHintsByBinding[
                      key as FileDeletionHintsByBindingKeys
                    ] || '',
                  ];
                }
                return acc;
              },
              ['Невозможно удалить файл'],
            )
            .filter((hint) => hint)
            .join('. ')
        : 'Удалить файл',
      onClick: () =>
        createConfirmModal({
          title: 'Внимание',
          message: 'Вы действительно хотите удалить файл?',
          onConfirm: async () => {
            if (row.rowId?.id) {
              try {
                const res = await appInstance.delete<boolean>(
                  isRequestOrInternal
                    ? apiUrls.workGroup.nestedTabsEndpoints
                        .removeRequestOrInternalFileLink
                    : apiUrls.workGroup.fileData.deleteFileById(row.rowId.id),
                  isRequestOrInternal
                    ? { params: { fileId: row.rowId.id } }
                    : {},
                );
                if (res.data) {
                  notification.success({
                    message: 'Файл успешно удален',
                  });
                  // Используем умный рефетч если доступен, иначе обычный
                  if (smartRefetch?.refetchAfterDelete) {
                    await smartRefetch.refetchAfterDelete([row]);
                  } else {
                    refetch();
                  }
                } else {
                  notification.warning({
                    message: 'Произошла ошибка удаления из файлового хранилища',
                  });
                }
              } catch (err) {
                appErrorNotification(
                  'Произошла ошибка удаления файла',
                  err as AppError,
                );
              }
            }
          },
        }),
    },
  ];

  const mainFileButtons: AdditionalButton[] = [
    {
      title: 'Отметить основным',
      key: '1',
      disabled: !havePermissionsByKey(AT_EDIT_PROFILE),
      tooltip: !havePermissionsByKey(AT_EDIT_PROFILE)
        ? permissionsConfig.warnMessages.noPermissionsDefault('отметку')
        : undefined,
      onClick: () =>
        createConfirmModal({
          title: 'Отметка основного файла',
          message: 'Вы действительно хотите отметить файл основным?',
          onConfirm: async () => {
            await appInstance.get(
              generateUrlWithQueryParams(
                isRequest
                  ? apiUrls.workGroup.request.markMainFile
                  : apiUrls.workGroup.internal.markMainFile,
                {
                  fileId: row.rowId?.id,
                },
              ),
            );
            notification.success({ message: 'Файл успешно отмечен' });
            // Используем умный рефетч если доступен, иначе обычный
            if (smartRefetch?.refetchAfterMarkMain) {
              await smartRefetch.refetchAfterMarkMain(row);
            } else {
              refetch();
            }
          },
        }),
    },
  ];

  const statusButtons: AdditionalButton[] = [
    {
      title: 'Исполнен полностью',
      key: 'item_completed',
      size: 'small',
      disabled: !havePermissionsByKey(AT_EDIT_PROFILE),
      tooltip: !havePermissionsByKey(AT_EDIT_PROFILE)
        ? permissionsConfig.warnMessages.noPermissionsDefault(
            'изменение статуса',
          )
        : undefined,
      onClick: () =>
        createConfirmModal({
          message: 'Перевести в статус "Исполнен полностью" ?',
          title: 'Подтвердите действие',
          onConfirm: () => handleSetStatus('item_completed', refetch, row),
        }),
    },
    {
      title: 'Исполнен частично',
      key: 'item_loaded',
      disabled: !havePermissionsByKey(AT_EDIT_PROFILE),
      tooltip: !havePermissionsByKey(AT_EDIT_PROFILE)
        ? permissionsConfig.warnMessages.noPermissionsDefault(
            'изменение статуса',
          )
        : undefined,
      size: 'small',
      onClick: () =>
        createConfirmModal({
          message: 'Перевести в статус "Исполнен частично" ?',
          title: 'Подтвердите действие',
          onConfirm: () => handleSetStatus('item_loaded', refetch, row),
        }),
    },
    {
      title: 'Не исполнен',
      key: 'item_not_completed',
      size: 'small',
      disabled: !havePermissionsByKey(AT_EDIT_PROFILE),
      tooltip: !havePermissionsByKey(AT_EDIT_PROFILE)
        ? permissionsConfig.warnMessages.noPermissionsDefault(
            'изменение статуса',
          )
        : undefined,
      onClick: () =>
        createConfirmModal({
          message: 'Перевести в статус "Не исполнен" ?',
          title: 'Подтвердите действие',
          onConfirm: () => handleSetStatus('item_not_completed', refetch, row),
        }),
    },
  ];

  const requestInternalEdit: AdditionalButton[] = [
    {
      title: '',
      key: 'edit',
      icon: <EditOutlined />,
      disabled: !havePermissionsByKey(AT_EDIT_PROFILE) || row.dateSend !== null,
      tooltip: !havePermissionsByKey(AT_EDIT_PROFILE)
        ? permissionsConfig.warnMessages.noPermissionsDefault(
            'редактирование записи',
          )
        : row.dateSend !== null
        ? 'Запись отправлена в САДД, редактирование невозможно'
        : 'Редактирование записи',

      tooltipProps: {
        placement: 'topRight',
      },

      onClick: async () => {
        if (isRequest) {
          try {
            const res = await appInstance.post<{
              canEdit: boolean;
              duration: number;
              startTime: string;
              user: string;
            }>(
              generateUrlWithQueryParams(
                apiUrls.workGroup.request.canUpdateCheck,
                { requestNoticeId: row?.rowId?.id },
              ),
            );

            const timeEnd = getDurationAsString(res.data.duration);

            if (res.data.canEdit) {
              handleRow({
                ...row?.rowId,
                userName: '',
                duration: String(res.data.duration),
                nestedTable: row?.nestedTable,
                isRequest,
              });
              togglePopup('requestUpdate');
            } else {
              createConfirmModal({
                title: 'Внимание',
                message: `Запись редактируется пользователем ${res.data.user}, с ${res.data.startTime}.
               Время ожидания окончания редактирования ${timeEnd}.
                Открыть в режиме просмотра ?`,
                onConfirm: () => {
                  handleRow({
                    ...row?.rowId,
                    viewOnly: 'true',
                    userName: res.data.user,
                    waitTime: `${timeEnd}`,
                    nestedTable: row?.nestedTable,
                    isRequest,
                  });
                  togglePopup('requestUpdate');
                },
              });
            }
          } catch (e) {
            appErrorNotification(
              'Произошла ошибка проверки доступности редактирования',
              e as AppError,
            );
          }
        } else {
          handleRow({
            ...row?.rowId,
            nestedTable: row?.nestedTable,
          });
          togglePopup('internalUpdate');
        }
      },
    },
    {
      title: '',
      key: isRequest ? 'viewOnly' : DISABLE_KEY,
      icon: <EyeOutlined />,
      disabled: !havePermissionsByKey(AT_VIEW_PROFILE),
      tooltip: !havePermissionsByKey(AT_VIEW_PROFILE)
        ? permissionsConfig.warnMessages.noPermissionsDefault('просмотр записи')
        : 'Просмотр записи',

      tooltipProps: {
        placement: 'topRight',
      },

      onClick: () => {
        handleRow({
          ...row?.rowId,
          viewOnly: 'true',
          userName: '',
          nestedTable: row?.nestedTable,
        });
        togglePopup(isRequest ? 'requestUpdate' : 'internalUpdate');
      },
    },
    {
      title: '',
      key: row.rowId?.isDifferentialAccessEnabled ? 'dif' : DISABLE_KEY,
      icon: <DiffOutlined />,
      disabled: !havePermissionsByKey(AT_EDIT_PROFILE),
      tooltip: !havePermissionsByKey(AT_EDIT_PROFILE)
        ? permissionsConfig.warnMessages.noPermissionsDefault(
            'прикрепление файлов',
          )
        : 'Прикрепить файлы дифференциального доступа',
      tooltipProps: {
        placement: 'topRight',
      },
      onClick: () => {
        handleRow(row?.rowId || {});
        togglePopup(isRequest ? 'requestDiffFiles' : 'internalDiffFiles');
      },
    },
  ];

  const defButtons: AdditionalButton[] = [
    {
      title: '',
      key: 'def',
      icon: <EditOutlined />,
      disabled: !canEdit.defInventory,
      tooltip: !canEdit.defInventory
        ? permissionsConfig.warnMessages.noPermissionsDefault('редактирование')
        : 'Редактировать комментарий системной описи ДЭФ',
      tooltipProps: {
        placement: 'topRight',
      },
      onClick: () =>
        createInputsModal({
          endpoint: generateUrlWithQueryParams(
            apiUrls.workGroup.inventory.updateComment,
            { id: row.rowId?.id || '' },
          ),
          inputs: [
            {
              title: 'Комментарий',
              type: 'text',
              key: 'comment',
              defaultValue: (row.rowId?.comment as string) || '',
            },
          ],
          title: 'Редактирование комментария',
          onSubmit: async () => refetch(),
        }),
    },
  ];

  const editEpc: AdditionalButton[] = [
    {
      title: '',
      key: 'def',
      disabled: !canEdit.epc,
      icon: <EditOutlined />,
      tooltip: !canEdit.epc
        ? permissionsConfig.warnMessages.noPermissionsDefault('редактирование')
        : 'Редактирование описи ЭПП',
      tooltipProps: {
        placement: 'topRight',
      },
      onClick: () => {
        handleRow(row.rowId || {});
        togglePopup('epcEdit');
      },
    },
  ];

  const fileBind: AdditionalButton[] = [
    {
      title: '',
      key: 'def',
      icon: <DiffOutlined />,
      disabled: Boolean(
        !row?.rowId?.enableLinkToDef || !havePermissionsByKey(AT_EDIT_LIST),
      ),
      tooltip: !row?.rowId?.enableLinkToDef
        ? NWGConfig.validationErrors.omniRoute('к пакету ДЭФ')
        : `${
            havePermissionsByKey(AT_EDIT_LIST)
              ? 'Редактировать привязку к пакету ДЭФ'
              : 'Нет прав для редактирования привязки к пакету ДЭФ'
          }`,
      tooltipProps: {
        placement: 'topRight',
      },
      onClick: () => {
        handleRow(row.rowId || {});
        togglePopup('defBind');
      },
    },
    {
      title: '',
      disabled: Boolean(
        !row?.rowId?.enableLinkToRequest || !havePermissionsByKey(AT_EDIT_LIST),
      ),
      key: 'request',
      icon: <SnippetsOutlined />,
      tooltip: !row?.rowId?.enableLinkToRequest
        ? NWGConfig.validationErrors.omniRoute('к заявке или пункту заявки')
        : `${
            havePermissionsByKey(AT_EDIT_LIST)
              ? 'Редактировать привязку к заявке или пункту заявки'
              : 'Нет прав для редактирования привязки к заявке или пункту заявки'
          }`,
      tooltipProps: {
        placement: 'topRight',
      },
      onClick: () => {
        handleRow(row.rowId || {});
        togglePopup('requestBind');
      },
    },
    {
      title: '',
      disabled: Boolean(!row?.rowId?.enableLinkToInternal),
      key: 'internal',
      icon: <CopyOutlined />,
      tooltip: !row?.rowId?.enableLinkToInternal
        ? NWGConfig.validationErrors.omniRoute(
            'к уведомлению внутренней переписки',
          )
        : 'Редактировать привязку к уведомлению внутренней переписки',
      tooltipProps: {
        placement: 'topRight',
      },
      onClick: () => {
        handleRow(row.rowId || {});
        togglePopup('internalBind');
      },
    },
  ];

  const unlinkDefButtons: AdditionalButton[] = [
    {
      title: '',
      key: row.rowId?.enableUnlinkDef ? 'unlinkDef' : DISABLE_KEY,
      icon: <FileExclamationOutlined />,
      disabled: !havePermissionsByKey(AT_EDIT_LIST),
      tooltip: !havePermissionsByKey(AT_EDIT_LIST)
        ? permissionsConfig.warnMessages.noPermissionsDefault('отвязывание')
        : 'Отвязать файл от пакета ДЭФ',
      tooltipProps: {
        placement: 'topRight',
      },
      onClick: () => {
        createConfirmModal({
          title: 'Внимание',
          message: 'Вы действительно хотите отвязать файл от пакета ДЭФ ?',
          onConfirm: async () => {
            if (row.rowId?.id) {
              await handleUnlinkDef(row.rowId.id);
              notification.success({
                message: `Файл "${row.name}" успешно отвязан от пакета ДЭФ`,
              });
              refetch();
            }
          },
        });
      },
    },
  ];

  const filteredRequestEdit = requestInternalEdit.filter(
    (button) => button.key !== DISABLE_KEY,
  );

  const filteredDownloadButtons = downloadButtons.filter(
    (button) => button.key !== DISABLE_KEY,
  );

  const filteredStatusButtons = statusButtons.filter(
    (button) => button.key !== row.rowId?.statusId,
  );

  const filteredDeleteButtons = deleteButtons.filter(
    (button) => button.key !== DISABLE_KEY,
  );

  const filteredUnlinkDefButtons = unlinkDefButtons.filter(
    (button) => button.key !== DISABLE_KEY,
  );

  /* TODO: отключил кнопку до реализации на беке задачи appid-175 */
  const filteredBind = fileBind.filter((button) => button.key !== 'internal');

  return [
    filteredDownloadButtons,
    filteredDeleteButtons,
    mainFileButtons,
    filteredStatusButtons,
    filteredRequestEdit,
    editEpc,
    defButtons,
    filteredBind,
    filteredUnlinkDefButtons,
  ];
};
