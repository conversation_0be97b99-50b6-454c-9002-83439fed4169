import axios from 'axios';
import { useCallback, useLayoutEffect } from 'react';
import {
  SortObject,
  TabsItemsResponse,
  NWGConfig,
  Permissions,
  NestedTabsKeys,
} from 'widgets/NewWorkGroupTabs';
import { ColumnFilters } from 'features/CustomColumnFilters';
import { permissionsStore } from 'entities/Permissions';
import { apiUrls } from 'shared/api';
import { appErrorNotification, generateUrlWithQueryParams } from 'shared/lib';
import { useAxiosRequest } from 'shared/model';

export const useMainContent = (
  tableEndpoint: Endpoint,
  cabinetId: string,
  handleNestedTab: (key: string, endpoint: Endpoint) => void,
  resetSelect: Callback,
  handleTotal: (total: number) => void,
  handlePage: (page: number) => void,
  additional: Record<string, boolean | string>,
  filters: ColumnFilters[],
  sortOrder: SortObject,
  isPermissionsComplete: boolean,
  permissions: Permissions,
): [typeof tableStatuses, (pageNumber?: number) => void] => {
  const canView = {
    request: permissions.ap_RequestNoticeWG.includes(
      permissionsStore.enums.Actions.VIEW_LIST_AT,
    ),
    packageDef: permissions.ap_PackageDEF.includes(
      permissionsStore.enums.Actions.VIEW_LIST_AT,
    ),
    internal: permissions.ap_InternalWGO.includes(
      permissionsStore.enums.Actions.VIEW_LIST_AT,
    ),
    fileData: permissions.ap_FilesWGO.includes(
      permissionsStore.enums.Actions.VIEW_LIST_AT,
    ),
  } as const;

  const initialActiveKey = ((): number => {
    if (canView.request) {
      return NestedTabsKeys.REQUEST_KEY;
    }
    if (canView.packageDef) {
      return NestedTabsKeys.PACKAGE_DEF_KEY;
    }
    if (canView.internal) {
      return NestedTabsKeys.INTERNAL_KEY;
    }
    if (canView.fileData) {
      return NestedTabsKeys.FILE_DATA_KEY;
    }
    return NestedTabsKeys.NO_TABS_KEY;
  })();

  const [getTable, tableStatuses] = useAxiosRequest<
    TableColumnsAndRowsWithPagination | TabsItemsResponse[]
  >();
  const controller = new AbortController();

  const getData = useCallback(
    async (page?: number): Promise<void> => {
      await getTable(
        generateUrlWithQueryParams(
          tableEndpoint,
          {
            cabinetId,
            pageSize: 10,
            pageNumber: page || 1,
            ...additional,
            ...(sortOrder && sortOrder),
          },
          { parseAllValues: true },
        ),
        NWGConfig.constants.TABLE_FILTERS_ENDPOINTS.includes(tableEndpoint)
          ? { method: 'POST', data: filters, signal: controller.signal }
          : { signal: controller.signal },
      )
        .then((res) => {
          if (Array.isArray(res)) {
            if (
              initialActiveKey !== NestedTabsKeys.NO_TABS_KEY
            ) {
              handleNestedTab(
                String(initialActiveKey),
                res[initialActiveKey - 1].endpoint,
              );
            }
            handlePage(1);
            resetSelect();
          } else {
            handleTotal(res.pagination.total);
            if (page) {
              handlePage(page);
            } else {
              handlePage(1);
            }
          }
        })
        .catch((err) => {
          if (axios.isAxiosError(err)) {
            appErrorNotification(
              NWGConfig.requestErrors.mainContent,
              err as AppError,
            );
          }
        });
    },
    [
      additional,
      cabinetId,
      controller.signal,
      filters,
      getTable,
      handleNestedTab,
      handlePage,
      handleTotal,
      initialActiveKey,
      resetSelect,
      sortOrder,
      tableEndpoint,
    ],
  );

  const getTableData = useCallback(
    (page?: number) => {
      getData(page);
    },
    [getData], // eslint-disable-line
  );

  useLayoutEffect(() => {
    if (
      tableEndpoint !== '' &&
      isPermissionsComplete &&
      initialActiveKey !== NestedTabsKeys.NO_TABS_KEY
    ) {
      getData();
    }

    return () => {
      controller.abort();
    };
    // eslint-disable-next-line
  }, [
    tableEndpoint,
    additional,
    filters,
    sortOrder,
    isPermissionsComplete,
    initialActiveKey,
  ]);

  return [
    {
      ...tableStatuses,
      data: Array.isArray(tableStatuses.data)
        ? tableStatuses.data.map((item) => {
            const isDisabled = ((): boolean => {
              if (
                item.endpoint === apiUrls.workGroup.nestedTabsEndpoints.request
              ) {
                return !canView.request;
              }

              if (
                item.endpoint ===
                apiUrls.workGroup.nestedTabsEndpoints.packageDef
              ) {
                return !canView.packageDef;
              }

              if (
                item.endpoint === apiUrls.workGroup.nestedTabsEndpoints.internal
              ) {
                return !canView.internal;
              }

              if (
                item.endpoint === apiUrls.workGroup.nestedTabsEndpoints.fileData
              ) {
                return !canView.fileData;
              }

              return false;
            })();

            return { ...item, disabled: isDisabled };
          })
        : tableStatuses.data,
    },
    getTableData,
  ];
};
