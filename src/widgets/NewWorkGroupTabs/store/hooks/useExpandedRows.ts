import { useCallback } from 'react';
import { useAppDispatch, useAppSelector } from 'shared/model';
import { selectExpandedRowKeys } from '../selectors';

interface UseExpandedRowsReturn {
  expandedRowKeys: React.Key[];
  onExpandedRowsChange: (keys: readonly React.Key[]) => void;
}

export const useExpandedRows = (): UseExpandedRowsReturn => {
  const dispatch = useAppDispatch();
  const expandedRowKeys = useAppSelector(selectExpandedRowKeys);

  const onExpandedRowsChange = useCallback(
    (keys: readonly React.Key[]) => {
      dispatch({
        type: 'newWorkGroupTabs/setExpandedRowKeys',
        payload: { keys: [...keys] },
      });
    },
    [dispatch],
  );

  return { expandedRowKeys, onExpandedRowsChange };
};
