import { useState, useCallback } from 'react';

interface UseExpandedRowsReturn {
  expandedRowKeys: React.Key[];
  onExpandedRowsChange: (keys: readonly React.Key[]) => void;
}

export const useExpandedRows = (): UseExpandedRowsReturn => {
  const [expandedRowKeys, setExpandedRowKeys] = useState<React.Key[]>([]);

  const onExpandedRowsChange = useCallback(
    (keys: readonly React.Key[]) => setExpandedRowKeys([...keys]),
    [],
  );

  return { expandedRowKeys, onExpandedRowsChange };
};
