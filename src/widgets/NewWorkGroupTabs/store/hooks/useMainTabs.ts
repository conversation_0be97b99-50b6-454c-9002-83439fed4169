import axios from 'axios';
import { useCallback, useLayoutEffect, useState } from 'react';
import {
  NWGConfig,
  TabsItemsResponse,
  NWGStore,
  Permissions,
  MainKeys,
} from 'widgets/NewWorkGroupTabs';
import { permissionsStore } from 'entities/Permissions';
import { apiUrls } from 'shared/api';
import { appErrorNotification, generateUrlWithQueryParams } from 'shared/lib';
import { useAxiosRequest } from 'shared/model';

export const useMainTabs = (
  cabinetId: string,
  handleTab: (key: string, endpoint: Endpoint) => void,
  isPermissionsComplete: boolean,
  permissions: Permissions,
): [typeof tabsStatuses, number, Callback] => {
  const [counter, setCounter] = useState(0);
  const [getTabs, tabsStatuses] = useAxiosRequest<TabsItemsResponse[]>();
  const [getUnreadCounter, unreadCounter] = useAxiosRequest<number>();

  const isInputOutputDef = (() =>
    permissions.ap_RequestNoticeWG.includes(
      permissionsStore.enums.Actions.VIEW_LIST_AT,
    ) ||
    permissions.ap_PackageDEF.includes(
      permissionsStore.enums.Actions.VIEW_LIST_AT,
    ) ||
    permissions.ap_InternalWGO.includes(
      permissionsStore.enums.Actions.VIEW_LIST_AT,
    ) ||
    permissions.ap_FilesWGO.includes(
      permissionsStore.enums.Actions.VIEW_LIST_AT,
    ))();

  const canView = {
    inputOutputDef: isInputOutputDef,
    members: permissions.ap_ListWG.includes(
      permissionsStore.enums.Actions.VIEW_LIST_AT,
    ),
    def: permissions.ap_ListDEF.includes(
      permissionsStore.enums.Actions.VIEW_LIST_AT,
    ),
    epc: permissions.ap_ListEPC.includes(
      permissionsStore.enums.Actions.VIEW_LIST_AT,
    ),
    alert: permissions.ap_AlertList.includes(
      permissionsStore.enums.Actions.VIEW_LIST_AT,
    ),
  } as const;

  const tabKeyInitial = ((): number => {
    if (canView.inputOutputDef) {
      return MainKeys.INPUT_OUTPUT_DEF_KEY;
    }

    if (canView.members) {
      return MainKeys.MEMBERS_KEY;
    }
    if (canView.def) {
      return MainKeys.DEF_INVENTORY_KEY;
    }
    if (canView.epc) {
      return MainKeys.EPC_KEY;
    }
    if (canView.alert) {
      return MainKeys.NOTICE_FEED_KEY;
    }

    return MainKeys.NO_TABS_KEY;
  })();

  const getCounter = useCallback(async () => {
    await getUnreadCounter(
      generateUrlWithQueryParams(apiUrls.workGroup.notification.checkUnread, {
        cabinetId,
      }),
    )
      .then((res) => setCounter(res))
      .catch((err) => {
        // eslint-disable-next-line no-console
        console.log(err);
      });
  }, [cabinetId, getUnreadCounter]);

  const getTabsData = async (): Promise<void> => {
    await getTabs(apiUrls.workGroup.mainTabs)
      .then((res) => {
        handleTab(String(tabKeyInitial), res[tabKeyInitial - 1].endpoint);
      })
      .catch((err) => {
        if (axios.isAxiosError(err)) {
          appErrorNotification(
            NWGConfig.requestErrors.mainTabs,
            err as AppError,
          );
        }
      });
  };

  useLayoutEffect(() => {
    if (
      isPermissionsComplete &&
      tabKeyInitial !== MainKeys.NO_TABS_KEY
    ) {
      getTabsData();
      getCounter();
    }
  }, [isPermissionsComplete, tabKeyInitial]); // eslint-disable-line

  useLayoutEffect(() => {
    const timer = setTimeout(getCounter, 100000);

    return () => clearTimeout(timer);
  }, [unreadCounter]); // eslint-disable-line

  return [
    {
      ...tabsStatuses,
      data:
        tabsStatuses.data?.map((item) => {
          const isDisabled = ((): boolean => {
            if (
              item.endpoint === apiUrls.workGroup.tabsEndpoints.inputOutputDef
            ) {
              return !canView.inputOutputDef;
            }

            if (item.endpoint === apiUrls.workGroup.tabsEndpoints.members) {
              return !canView.members;
            }

            if (
              item.endpoint === apiUrls.workGroup.tabsEndpoints.defInventory
            ) {
              return !canView.def;
            }

            if (item.endpoint === apiUrls.workGroup.tabsEndpoints.epc) {
              return !canView.epc;
            }

            if (item.endpoint === apiUrls.workGroup.tabsEndpoints.noticeFeed) {
              return !canView.alert;
            }

            return false;
          })();

          return { ...item, disabled: isDisabled };
        }) || [],
    },
    counter,
    getCounter,
  ];
};
