import { useCallback } from 'react';
import { TableRowData } from 'features/DataGrid';
import { TabType } from '../../types';

export interface TabTypeInfo {
  tabType: TabType;
  rowId: string;
  endpoint?: string;
  tabKey?: string;
}

export const useTabTypeDetection = () => {
  const detectTabType = useCallback((
    row: TableRowData,
    activeEndpoint: string
  ): TabTypeInfo => {
    const rowId = String(row.rowId?.id || '');
    
    // 1. Проверяем жадные табы (данные уже есть в nestedTable)
    if (row.nestedTable) {
      return {
        tabType: TabType.EAGER,
        rowId,
      };
    }
    
    // 2. Проверяем hasNested табы (флаг hasNested)
    if (row.rowId?.hasNested) {
      return {
        tabType: TabType.HAS_NESTED,
        rowId,
        endpoint: activeEndpoint,
      };
    }
    
    // 3. По умолчанию считаем ленивыми табами
    // (будут загружаться по endpoint из метаданных)
    return {
      tabType: TabType.LAZY,
      rowId,
      endpoint: activeEndpoint,
    };
  }, []);

  const detectMultipleTabTypes = useCallback((
    rows: TableRowData[],
    activeEndpoint: string
  ): TabTypeInfo[] => {
    return rows.map(row => detectTabType(row, activeEndpoint));
  }, [detectTabType]);

  return {
    detectTabType,
    detectMultipleTabTypes,
  };
};
