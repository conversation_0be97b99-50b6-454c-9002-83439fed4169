import { useCallback } from 'react';
import { useAppDispatch, useAppSelector } from 'shared/model';
import { loadHasNestedTabs } from '../actions';
import {
  selectHasNestedTabsForRow,
  selectActiveHasNestedKeys,
  selectHasNestedLoadingState
} from '../selectors';

export const useHasNestedTabs = (rowId: string, endpoint: string, cabinetId?: string): {
  activeKeys: string[];
  clearTabs: () => void;
  error?: string;
  isLoading: boolean;
  loadTabs: () => Promise<void>;
  setActiveKeys: (keys: string[]) => void;
  tabsData: any;
} => {
  const dispatch = useAppDispatch();

  const tabsData = useAppSelector(state => selectHasNestedTabsForRow(state, rowId));
  const activeKeys = useAppSelector(state => selectActiveHasNestedKeys(state, rowId));
  const loadingState = useAppSelector(state => selectHasNestedLoadingState(state, rowId));

  const loadTabs = useCallback(async () => {
    if (!tabsData && !loadingState.isLoading) {
      await dispatch(loadHasNestedTabs({ cabinetId, endpoint, rowId }));
    }
  }, [dispatch, endpoint, rowId, cabinetId, tabsData, loadingState.isLoading]);

  const setActiveKeys = useCallback((keys: string[]) => {
    dispatch({
      type: 'newWorkGroupTabs/setActiveHasNestedKeys',
      payload: { keys, rowId },
    });
  }, [dispatch, rowId]);

  const clearTabs = useCallback(() => {
    dispatch({
      type: 'newWorkGroupTabs/clearHasNestedTabsForRow',
      payload: { rowId },
    });
  }, [dispatch, rowId]);

  return {
    activeKeys,
    clearTabs,
    isLoading: loadingState.isLoading,
    loadTabs,
    setActiveKeys,
    tabsData,
    error: loadingState.error,
  };
};
