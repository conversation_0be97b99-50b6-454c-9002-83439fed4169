import { useCallback, useEffect } from 'react';
import { TabsWithLazyTable } from 'features/DataGrid';
import { useAppDispatch, useAppSelector } from 'shared/model';
import { NWGStore } from '../..';
import { TabData, UseLazyTabsDataReturn } from '../../types';

export const useLazyTabsRedux = (
  initialTabs: TabsWithLazyTable[],
  parentRowId: string | number | undefined,
): UseLazyTabsDataReturn => {
  const dispatch = useAppDispatch();
  const rowId = String(parentRowId || '');

  // Получаем данные из Redux
  const activeTabKeys = useAppSelector(state =>
    NWGStore.selectors.selectActiveLazyTabKeys(state, rowId)
  );
  const tabsData = useAppSelector(state =>
    NWGStore.selectors.selectLazyTabsForRow(state, rowId)
  );

  // Инициализируем табы при первом рендере
  useEffect(() => {
    if (parentRowId && initialTabs.length > 0) {
      dispatch(NWGStore.slice.actions.initializeTabs({
        parentRowId: rowId,
        tabs: initialTabs,
      }));
    }
  }, [dispatch, parentRowId, rowId, initialTabs]);

  const loadTabData = useCallback(
    async (tabKey: string, page = 1): Promise<void> => {
      if (!parentRowId) return;

      const tab = initialTabs.find(t => t.key === tabKey);
      if (!tab) return;

      // Если таб preloaded, не загружаем
      if (tab.tableData) return;

      await dispatch(NWGStore.actions.fetchTabData({
        parentRowId: rowId,
        tabKey,
        endpoint: tab.endpoint,
        page,
      })).unwrap();
    },
    [dispatch, parentRowId, rowId, initialTabs]
  );

  const handleTabToggle = useCallback(
    async (keys: string | string[]): Promise<void> => {
      const keysArray = Array.isArray(keys) ? keys : [keys];

      // Обновляем активные табы в Redux
      dispatch(NWGStore.slice.actions.setActiveTabKeys({
        rowId,
        keys: keysArray,
      }));

      // Загружаем данные для новых активных табов
      const loadPromises = keysArray
        .filter(tabKey => {
          const tab = tabsData.find(t => t.key === tabKey);
          return tab && !tab.tabStatus?.isLoaded && !tab.tabStatus?.isLoading;
        })
        .map(tabKey => loadTabData(tabKey));

      await Promise.all(loadPromises);
    },
    [dispatch, rowId, tabsData, loadTabData]
  );

  const handlePageChange = useCallback(
    async (tab: TabData, page: number): Promise<void> => {
      // Если таб preloaded, не загружаем
      const originalTab = initialTabs.find(t => t.key === tab.key);
      if (originalTab?.tableData) return;

      await loadTabData(String(tab.key), page);
    },
    [initialTabs, loadTabData]
  );

  // Преобразуем табы из Redux в формат, ожидаемый компонентами
  const formattedTabsData: TabData[] = initialTabs.map(initialTab => {
    const reduxTab = tabsData.find(t => t.key === initialTab.key);

    if (reduxTab) {
      return reduxTab;
    }

    // Если таба нет в Redux, возвращаем исходный
    return {
      ...initialTab,
      tabStatus: initialTab.tableData ? {
        isLoading: false,
        isLoaded: true,
        isError: false,
      } : {
        isLoading: false,
        isLoaded: false,
        isError: false,
      },
    };
  });

  return {
    tabsData: formattedTabsData,
    activeTabKeys,
    loadTabData,
    handleTabToggle,
    handlePageChange,
  };
};
