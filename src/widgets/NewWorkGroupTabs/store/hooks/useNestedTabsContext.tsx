import { createContext, useContext, ReactNode, FC } from 'react';

interface NestedTabsContextValue {
  activeTabKeys: Record<string, string[]>;
  setActiveTabKeys: (rowId: string, keys: string[]) => void;
}

const NestedTabsContext = createContext<NestedTabsContextValue | undefined>(
  undefined,
);

export const useNestedTabsContext = (): NestedTabsContextValue => {
  const context = useContext(NestedTabsContext);
  if (!context) {
    throw new Error(
      'useNestedTabsContext must be used within NestedTabsProvider',
    );
  }
  return context;
};

interface NestedTabsProviderProps {
  children: ReactNode;
  value: NestedTabsContextValue;
}

export const NestedTabsProvider: FC<NestedTabsProviderProps> = ({
  children,
  value,
}) => (
  <NestedTabsContext.Provider value={value}>
    {children}
  </NestedTabsContext.Provider>
);
