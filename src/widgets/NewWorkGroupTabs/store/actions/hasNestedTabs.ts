import { createAsyncThunk } from '@reduxjs/toolkit';
import { NestedTabsWithLazyTable } from 'features/DataGrid';
import { appInstance } from 'shared/api';
import { generateUrlWithQueryParams } from 'shared/lib';

// Action для загрузки hasNested табов
export const loadHasNestedTabs = createAsyncThunk<
  NestedTabsWithLazyTable,
  { cabinetId?: string; endpoint: string; rowId: string },
  { rejectValue: string }
>(
  'tabs/loadHasNestedTabs',
  async ({ endpoint, rowId, cabinetId }, { rejectWithValue }) => {
    try {
      // Формируем URL для загрузки табов
      const tabsUrl = `${endpoint}/tabs`;
      const params: Record<string, string> = {};

      // Определяем параметры в зависимости от эндпоинта
      if (endpoint === 'krg3_request_notice' && rowId) {
        params.cabinetId = cabinetId || '';
        params.requestNoticeId = rowId;
      } else {
        params.rowId = rowId;
      }

      const url = generateUrlWithQueryParams(tabsUrl, params);
      const response = await appInstance.post(url);

      if (!response.data) {
        throw new Error('Нет данных в ответе');
      }

      return response.data as NestedTabsWithLazyTable;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Ошибка при загрузке hasNested табов'
      );
    }
  }
);

// Action для перезагрузки hasNested табов
export const refetchHasNestedTabs = createAsyncThunk<
  NestedTabsWithLazyTable,
  { cabinetId?: string; endpoint: string; rowId: string },
  { rejectValue: string }
>(
  'tabs/refetchHasNestedTabs',
  async ({ endpoint, rowId, cabinetId }, { rejectWithValue }) => {
    try {
      // Формируем URL для загрузки табов
      const tabsUrl = `${endpoint}/tabs`;
      const params: Record<string, string> = {};

      // Определяем параметры в зависимости от эндпоинта
      if (endpoint === 'krg3_request_notice' && rowId) {
        params.cabinetId = cabinetId || '';
        params.requestNoticeId = rowId;
      } else {
        params.rowId = rowId;
      }

      const url = generateUrlWithQueryParams(tabsUrl, params);
      const response = await appInstance.post(url);

      if (!response.data) {
        throw new Error('Нет данных в ответе');
      }

      return response.data as NestedTabsWithLazyTable;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Ошибка при перезагрузке hasNested табов'
      );
    }
  }
);
