import { createAsyncThunk } from '@reduxjs/toolkit';
import { AxiosError } from 'axios';
import { NestedTabsWithLazyTable } from 'features/DataGrid';
import { appInstance } from 'shared/api';
import { generateUrlWithQueryParams } from 'shared/lib';

// Функция для обработки ошибок HTTP запросов
const handleHttpError = (error: unknown): string => {
  if (error instanceof AxiosError) {
    const status = error.response?.status;
    const statusText = error.response?.statusText;

    switch (status) {
      case 400:
        return 'Неверный запрос (400)';
      case 401:
        return 'Не авторизован (401)';
      case 403:
        return 'Доступ запрещен (403)';
      case 404:
        return 'Ресурс не найден (404)';
      case 405:
        return 'Метод не разрешен (405). Проверьте конфигурацию сервера';
      case 500:
        return 'Внутренняя ошибка сервера (500)';
      case 502:
        return 'Плохой шлюз (502)';
      case 503:
        return 'Сервис недоступен (503)';
      default:
        return `HTTP ошибка ${status}: ${statusText || 'Неизвестная ошибка'}`;
    }
  }

  if (error instanceof Error) {
    return error.message;
  }

  return 'Неизвестная ошибка при загрузке данных';
};

// Action для загрузки hasNested табов
export const loadHasNestedTabs = createAsyncThunk<
  NestedTabsWithLazyTable,
  { cabinetId?: string; endpoint: string; rowId: string },
  { rejectValue: string }
>(
  'tabs/loadHasNestedTabs',
  async ({ endpoint, rowId, cabinetId }, { rejectWithValue }) => {
    try {
      // Формируем URL для загрузки табов
      const tabsUrl = `${endpoint}/tabs`;
      const params: Record<string, string> = {};

      // Определяем параметры в зависимости от эндпоинта
      if (endpoint === 'krg3_request_notice' && rowId) {
        params.cabinetId = cabinetId || '';
        params.requestNoticeId = rowId;
      } else {
        params.rowId = rowId;
      }

      const url = generateUrlWithQueryParams(tabsUrl, params);
      const response = await appInstance.post(url);

      // Проверяем статус ответа
      if (response.status < 200 || response.status >= 300) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      if (!response.data) {
        throw new Error('Нет данных в ответе');
      }

      return response.data as NestedTabsWithLazyTable;
    } catch (error) {
      const errorMessage = handleHttpError(error);
      console.error('Ошибка загрузки hasNested табов:', errorMessage, error);
      return rejectWithValue(errorMessage);
    }
  }
);

// Action для перезагрузки hasNested табов
export const refetchHasNestedTabs = createAsyncThunk<
  NestedTabsWithLazyTable,
  { cabinetId?: string; endpoint: string; rowId: string },
  { rejectValue: string }
>(
  'tabs/refetchHasNestedTabs',
  async ({ endpoint, rowId, cabinetId }, { rejectWithValue }) => {
    try {
      // Формируем URL для загрузки табов
      const tabsUrl = `${endpoint}/tabs`;
      const params: Record<string, string> = {};

      // Определяем параметры в зависимости от эндпоинта
      if (endpoint === 'krg3_request_notice' && rowId) {
        params.cabinetId = cabinetId || '';
        params.requestNoticeId = rowId;
      } else {
        params.rowId = rowId;
      }

      const url = generateUrlWithQueryParams(tabsUrl, params);
      const response = await appInstance.post(url);

      // Проверяем статус ответа
      if (response.status < 200 || response.status >= 300) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      if (!response.data) {
        throw new Error('Нет данных в ответе');
      }

      return response.data as NestedTabsWithLazyTable;
    } catch (error) {
      const errorMessage = handleHttpError(error);
      console.error('Ошибка перезагрузки hasNested табов:', errorMessage, error);
      return rejectWithValue(errorMessage);
    }
  }
);
