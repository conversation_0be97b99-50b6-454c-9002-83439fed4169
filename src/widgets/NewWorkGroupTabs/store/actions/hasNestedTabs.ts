import { createAsyncThunk } from '@reduxjs/toolkit';
import { NestedTabsWithLazyTable } from 'features/DataGrid';
import { appInstance } from 'shared/api';

// Action для загрузки hasNested табов
export const loadHasNestedTabs = createAsyncThunk<
  NestedTabsWithLazyTable,
  { endpoint: string; rowId: string },
  { rejectValue: string }
>(
  'tabs/loadHasNestedTabs',
  async ({ endpoint, rowId }, { rejectWithValue }) => {
    try {
      const response = await appInstance.get(`${endpoint}/tabs`, {
        params: { rowId },
      });
      
      if (!response.data) {
        throw new Error('Нет данных в ответе');
      }
      
      return response.data as NestedTabsWithLazyTable;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Ошибка при загрузке hasNested табов'
      );
    }
  }
);

// Action для перезагрузки hasNested табов
export const refetchHasNestedTabs = createAsyncThunk<
  NestedTabsWithLazyTable,
  { endpoint: string; rowId: string },
  { rejectValue: string }
>(
  'tabs/refetchHasNestedTabs',
  async ({ endpoint, rowId }, { rejectWithValue }) => {
    try {
      const response = await appInstance.get(`${endpoint}/tabs`, {
        params: { rowId },
      });
      
      if (!response.data) {
        throw new Error('Нет данных в ответе');
      }
      
      return response.data as NestedTabsWithLazyTable;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Ошибка при перезагрузке hasNested табов'
      );
    }
  }
);
