import { createAsyncThunk } from '@reduxjs/toolkit';
import { TabType } from '../../types';
import { refetchHasNestedTabs } from './hasNestedTabs';
import { fetchTabData } from './lazyTabs';

// Интерфейс для параметров рефетча
interface RefetchParams {
  cabinetId?: string;
  endpoint?: string;
  rowId?: string;
  tabKey?: string;
  tabType: TabType;
}

// Универсальный action для рефетча табов
export const refetchTabs = createAsyncThunk<
  void,
  RefetchParams,
  { rejectValue: string }
>(
  'tabs/refetchTabs',
  async ({ tabType, rowId, tabKey, endpoint, cabinetId }, { dispatch, rejectWithValue }): Promise<void> => {
    try {
      switch (tabType) {
        case TabType.EAGER:
          // Для жадных табов нужно перезагрузить основную таблицу
          // Это обрабатывается на уровне компонента через getTableData
          break;

        case TabType.HAS_NESTED:
          // Для hasNested табов перезагружаем табы для конкретной строки
          if (rowId && endpoint) {
            await dispatch(refetchHasNestedTabs({ cabinetId, endpoint, rowId }));
          }
          break;

        case TabType.LAZY:
          // Для ленивых табов перезагружаем конкретный таб
          if (rowId && tabKey && endpoint) {
            await dispatch(fetchTabData({
              endpoint,
              page: 1,
              parentRowId: rowId,
              tabKey,
            }));
          }
          break;

        default:
          throw new Error('Неизвестный тип таба');
      }
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Ошибка при рефетче табов'
      );
    }
  }
);

// Action для рефетча при действиях пользователя (удаление, отметка основным)
export const refetchAfterUserAction = createAsyncThunk<
  void,
  {
    actionType: 'delete' | 'markMain' | 'updateCount';
    affectedRows: Array<{
      cabinetId?: string;
      endpoint?: string;
      rowId: string;
      tabKey?: string;
      tabType: TabType;
    }>;
  },
  { rejectValue: string }
>(
  'tabs/refetchAfterUserAction',
  async ({ actionType, affectedRows }, { dispatch }) => {
    // Группируем строки по типу таба для оптимизации
    const groupedByType = affectedRows.reduce((acc, row) => {
      if (!acc[row.tabType]) {
        acc[row.tabType] = [];
      }
      acc[row.tabType].push(row);
      return acc;
    }, {} as Record<TabType, typeof affectedRows>);

    // Обрабатываем каждый тип табов
    const refetchPromises = Object.entries(groupedByType).map(([tabType, rows]) =>
      Promise.all(
        rows.map(row =>
          dispatch(refetchTabs({
            cabinetId: row.cabinetId,
            endpoint: row.endpoint,
            rowId: row.rowId,
            tabKey: row.tabKey,
            tabType: tabType as TabType,
          }))
        )
      )
    );

    await Promise.all(refetchPromises);
  }
);
