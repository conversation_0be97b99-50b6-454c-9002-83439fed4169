import { createAsyncThunk } from '@reduxjs/toolkit';
import { TabType } from '../../types';

// Интерфейс для параметров рефетча
interface RefetchParams {
  tabType: TabType;
  rowId?: string;
  tabKey?: string;
  endpoint?: string;
}

// Универсальный action для рефетча табов
export const refetchTabs = createAsyncThunk<
  void,
  RefetchParams,
  { rejectValue: string }
>(
  'tabs/refetchTabs',
  async ({ tabType, rowId, tabKey, endpoint }, { dispatch, rejectWithValue }) => {
    try {
      switch (tabType) {
        case TabType.EAGER:
          // Для жадных табов нужно перезагрузить основную таблицу
          // Это будет обрабатываться в middleware или компоненте
          break;
          
        case TabType.HAS_NESTED:
          // Для hasNested табов нужно перезагрузить табы для конкретной строки
          if (rowId) {
            // Очищаем данные и перезагружаем
            // Это будет обрабатываться в middleware
          }
          break;
          
        case TabType.LAZY:
          // Для ленивых табов нужно перезагрузить конкретный таб
          if (rowId && tabKey && endpoint) {
            // Перезагружаем конкретный таб
            // Это будет обрабатываться через fetchTabData
          }
          break;
          
        default:
          return rejectWithValue('Неизвестный тип таба');
      }
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Ошибка при рефетче табов'
      );
    }
  }
);

// Action для рефетча при действиях пользователя (удаление, отметка основным)
export const refetchAfterUserAction = createAsyncThunk<
  void,
  {
    actionType: 'delete' | 'markMain' | 'updateCount';
    affectedRows: Array<{
      rowId: string;
      tabType: TabType;
      endpoint?: string;
      tabKey?: string;
    }>;
  },
  { rejectValue: string }
>(
  'tabs/refetchAfterUserAction',
  async ({ actionType, affectedRows }, { dispatch }) => {
    // Группируем строки по типу таба для оптимизации
    const groupedByType = affectedRows.reduce((acc, row) => {
      if (!acc[row.tabType]) {
        acc[row.tabType] = [];
      }
      acc[row.tabType].push(row);
      return acc;
    }, {} as Record<TabType, typeof affectedRows>);

    // Обрабатываем каждый тип табов
    const refetchPromises = Object.entries(groupedByType).map(([tabType, rows]) => {
      return Promise.all(
        rows.map(row =>
          dispatch(refetchTabs({
            tabType: tabType as TabType,
            rowId: row.rowId,
            tabKey: row.tabKey,
            endpoint: row.endpoint,
          }))
        )
      );
    });

    await Promise.all(refetchPromises);
  }
);
