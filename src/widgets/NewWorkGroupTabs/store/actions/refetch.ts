import { createAsyncThunk } from '@reduxjs/toolkit';
import { TabType } from '../../types';

// Интерфейс для параметров рефетча
interface RefetchParams {
  endpoint?: string;
  rowId?: string;
  tabKey?: string;
  tabType: TabType;
}

// Универсальный action для рефетча табов
export const refetchTabs = createAsyncThunk<
  void,
  RefetchParams,
  { rejectValue: string }
>(
  'tabs/refetchTabs',
  async ({ tabType, rowId, tabKey, endpoint }, { rejectWithValue }) => {
    try {
      switch (tabType) {
        case TabType.EAGER:
          // Для жадных табов нужно перезагрузить основную таблицу
          // Это будет обрабатываться в middleware или компоненте
          break;

        case TabType.HAS_NESTED:
          // Для hasNested табов нужно перезагрузить табы для конкретной строки
          if (rowId) {
            // Очищаем данные и перезагружаем
            // Это будет обрабатываться в middleware
          }
          break;

        case TabType.LAZY:
          // Для ленивых табов нужно перезагрузить конкретный таб
          if (rowId && tabKey && endpoint) {
            // Перезагружаем конкретный таб
            // Это будет обрабатываться через fetchTabData
          }
          break;

        default:
          throw new Error('Неизвестный тип таба');
      }
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Ошибка при рефетче табов'
      );
    }
  }
);

// Action для рефетча при действиях пользователя (удаление, отметка основным)
export const refetchAfterUserAction = createAsyncThunk<
  void,
  {
    actionType: 'delete' | 'markMain' | 'updateCount';
    affectedRows: Array<{
      rowId: string;
      tabType: TabType;
      endpoint?: string;
      tabKey?: string;
    }>;
  },
  { rejectValue: string }
>(
  'tabs/refetchAfterUserAction',
  async ({ actionType, affectedRows }, { dispatch }) => {
    // Группируем строки по типу таба для оптимизации
    const groupedByType = affectedRows.reduce((acc, row) => {
      if (!acc[row.tabType]) {
        acc[row.tabType] = [];
      }
      acc[row.tabType].push(row);
      return acc;
    }, {} as Record<TabType, typeof affectedRows>);

    // Обрабатываем каждый тип табов
    const refetchPromises = Object.entries(groupedByType).map(([tabType, rows]) =>
      Promise.all(
        rows.map(row =>
          dispatch(refetchTabs({
            endpoint: row.endpoint,
            rowId: row.rowId,
            tabKey: row.tabKey,
            tabType: tabType as TabType,
          }))
        )
      )
    );

    await Promise.all(refetchPromises);
  }
);
