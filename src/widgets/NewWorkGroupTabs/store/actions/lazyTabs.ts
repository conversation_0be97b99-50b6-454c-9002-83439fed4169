import { createAsyncThunk } from '@reduxjs/toolkit';
import { AxiosError } from 'axios';
import { appInstance } from 'shared/api';
import { generateUrlWithQueryParams } from 'shared/lib';
import { getTabRequestParams } from '../../lib';

// Функция для обработки ошибок HTTP запросов
const handleHttpError = (error: unknown): string => {
  if (error instanceof AxiosError) {
    const status = error.response?.status;
    const statusText = error.response?.statusText;

    switch (status) {
      case 400:
        return 'Неверный запрос (400)';
      case 401:
        return 'Не авторизован (401)';
      case 403:
        return 'Доступ запрещен (403)';
      case 404:
        return 'Ресурс не найден (404)';
      case 405:
        return 'Метод не разрешен (405). Проверьте конфигурацию сервера';
      case 500:
        return 'Внутренняя ошибка сервера (500)';
      case 502:
        return 'Плохой шлюз (502)';
      case 503:
        return 'Сервис недоступен (503)';
      default:
        return `HTTP ошибка ${status}: ${statusText || 'Неизвестная ошибка'}`;
    }
  }

  if (error instanceof Error) {
    return error.message;
  }

  return 'Неизвестная ошибка при загрузке данных';
};

interface FetchTabDataParams {
  endpoint: string;
  page: number;
  parentRowId: string;
  tabKey: string;
}

export const fetchTabData = createAsyncThunk<
  TableColumnsAndRowsWithPagination,
  FetchTabDataParams,
  { rejectValue: string }
>(
  'lazyTabs/fetchTabData',
  async ({ parentRowId, endpoint, page }, { rejectWithValue }) => {
    try {
      const params = getTabRequestParams(endpoint, parentRowId, page);
      const url = generateUrlWithQueryParams(endpoint, params);

      const response = await appInstance.get<TableColumnsAndRowsWithPagination>(url);

      // Проверяем статус ответа
      if (response.status < 200 || response.status >= 300) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      if (!response.data) {
        throw new Error('Нет данных в ответе');
      }

      return response.data;
    } catch (error) {
      const errorMessage = handleHttpError(error);
      console.error('Ошибка загрузки данных ленивого таба:', errorMessage, error);
      return rejectWithValue(errorMessage);
    }
  },
  {
    condition: ({ parentRowId, tabKey }, { getState }) => {
      const state = getState() as {
        lazyTabs: { loadingStates: Record<string, { isLoading: boolean }> };
      };
      const key = `${parentRowId}-${tabKey}`;
      const loadingState = state.lazyTabs?.loadingStates[key];

      if (loadingState?.isLoading) {
        return false;
      }

      return true;
    },
  },
);
