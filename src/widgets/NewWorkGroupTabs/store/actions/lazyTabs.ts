import { createAsyncThunk } from '@reduxjs/toolkit';
import { appInstance } from 'shared/api';
import { generateUrlWithQueryParams } from 'shared/lib';
import { getTabRequestParams } from '../../lib';

interface FetchTabDataParams {
  endpoint: string;
  page: number;
  parentRowId: string;
  tabKey: string;
}

export const fetchTabData = createAsyncThunk<
  TableColumnsAndRowsWithPagination,
  FetchTabDataParams,
  { rejectValue: string }
>(
  'lazyTabs/fetchTabData',
  async ({ parentRowId, endpoint, page }, { rejectWithValue }) => {
    try {
      const params = getTabRequestParams(endpoint, parentRowId, page);
      const url = generateUrlWithQueryParams(endpoint, params);

      const { data } = await appInstance.get<TableColumnsAndRowsWithPagination>(
        url,
      );

      return data;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error
          ? error.message
          : 'Произошла ошибка при загрузке данных',
      );
    }
  },
  {
    condition: ({ parentRowId, tabKey }, { getState }) => {
      const state = getState() as {
        lazyTabs: { loadingStates: Record<string, { isLoading: boolean }> };
      };
      const key = `${parentRowId}-${tabKey}`;
      const loadingState = state.lazyTabs?.loadingStates[key];

      if (loadingState?.isLoading) {
        return false;
      }

      return true;
    },
  },
);
