import { createAsyncThunk } from '@reduxjs/toolkit';
import axios, { AxiosError } from 'axios';
import { appInstance } from 'shared/api';
import { generateUrlWithQueryParams } from 'shared/lib';
import { getTabRequestParams } from '../../lib';

interface FetchTabDataParams {
  endpoint: string;
  page: number;
  parentRowId: string;
  tabKey: string;
}

export const fetchTabData = createAsyncThunk<
  TableColumnsAndRowsWithPagination,
  FetchTabDataParams,
  { rejectValue: AxiosError }
>(
  'lazyTabs/fetchTabData',
  async ({ parentRowId, endpoint, page }, { rejectWithValue }) => {
    try {
      const params = getTabRequestParams(endpoint, parentRowId, page);
      const url = generateUrlWithQueryParams(endpoint, params);

      const { data } = await appInstance.get<TableColumnsAndRowsWithPagination>(url);

      return data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(error);
      }
      throw error;
    }
  },
  {
    condition: ({ parentRowId, tabKey }, { getState }) => {
      const state = getState() as {
        lazyTabs: { loadingStates: Record<string, { isLoading: boolean }> };
      };
      const key = `${parentRowId}-${tabKey}`;
      const loadingState = state.lazyTabs?.loadingStates[key];

      if (loadingState?.isLoading) {
        return false;
      }

      return true;
    },
  },
);
