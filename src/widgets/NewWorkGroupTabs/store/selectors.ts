import { createSelector } from '@reduxjs/toolkit';
import { NestedTabsWithLazyTable } from 'features/DataGrid';
import { selectSelf } from 'shared/lib';
import { TabData, TabsState } from '../types';

// Базовый селектор
const tabsSelector = selectSelf('newWorkGroupTabs');

// ===== Main Tabs Selectors =====
export const selectMainTabsState = createSelector(
  tabsSelector,
  (state) => state?.mainTabs || { activeKey: '0', endpoint: '' },
);

export const selectMainActiveKey = createSelector(
  selectMainTabsState,
  (mainTabs) => mainTabs.activeKey,
);

export const selectMainEndpoint = createSelector(
  selectMainTabsState,
  (mainTabs) => mainTabs.endpoint,
);

// ===== Nested Tabs Selectors =====
export const selectNestedTabsState = createSelector(
  tabsSelector,
  (state) => state?.nestedTabs || { activeKey: '0', endpoint: '' },
);

export const selectNestedActiveKey = createSelector(
  selectNestedTabsState,
  (nestedTabs) => nestedTabs.activeKey,
);

export const selectNestedEndpoint = createSelector(
  selectNestedTabsState,
  (nestedTabs) => nestedTabs.endpoint,
);

// ===== Expanded Rows Selectors =====
export const selectExpandedRowsState = createSelector(
  tabsSelector,
  (state) => state?.expandedRows || { keys: [] },
);

export const selectExpandedRowKeys = createSelector(
  selectExpandedRowsState,
  (expandedRows) => expandedRows.keys,
);

// ===== Lazy Tabs Selectors =====
export const selectLazyTabsState = createSelector(
  tabsSelector,
  (state) =>
    state?.lazyTabs || { activeTabKeys: {}, loadingStates: {}, tabsData: {} },
);

export const selectLazyTabsForRow = createSelector(
  [
    selectLazyTabsState,
    (_: { newWorkGroupTabs: TabsState }, rowId: string) => rowId,
  ],
  (lazyTabs, rowId): TabData[] => {
    const tabsForRow: TabData[] = [];

    Object.entries(lazyTabs.tabsData).forEach(([key, tabData]) => {
      if (key.startsWith(`${rowId}-`)) {
        tabsForRow.push(tabData as TabData);
      }
    });

    return tabsForRow.sort((a, b) =>
      String(a.key).localeCompare(String(b.key)),
    );
  },
);

export const selectActiveLazyTabKeys = createSelector(
  [
    selectLazyTabsState,
    (_: { newWorkGroupTabs: TabsState }, rowId: string) => rowId,
  ],
  (lazyTabs, rowId): string[] => lazyTabs.activeTabKeys[rowId] || [],
);

export const selectLazyTabLoadingState = createSelector(
  [
    selectLazyTabsState,
    (
      _: { newWorkGroupTabs: TabsState },
      rowId: string,
      tabKey: string,
    ) => ({ rowId, tabKey }),
  ],
  (lazyTabs, { rowId, tabKey }) => {
    const key = `${rowId}-${tabKey}`;
    return (
      lazyTabs.loadingStates[key] || {
        isLoading: false,
        isLoaded: false,
        isError: false,
      }
    );
  },
);

// ===== HasNested Tabs Selectors =====
export const selectHasNestedTabsState = createSelector(
  tabsSelector,
  (state) =>
    state?.hasNestedTabs || { tabsData: {}, activeKeys: {}, loadingStates: {} },
);

export const selectHasNestedTabsForRow = createSelector(
  [
    selectHasNestedTabsState,
    (_: { newWorkGroupTabs: TabsState }, rowId: string) => rowId,
  ],
  (hasNestedTabs, rowId): NestedTabsWithLazyTable | null =>
    hasNestedTabs.tabsData[rowId] || null,
);

export const selectActiveHasNestedKeys = createSelector(
  [
    selectHasNestedTabsState,
    (_: { newWorkGroupTabs: TabsState }, rowId: string) => rowId,
  ],
  (hasNestedTabs, rowId): string[] => hasNestedTabs.activeKeys[rowId] || [],
);

export const selectHasNestedLoadingState = createSelector(
  [
    selectHasNestedTabsState,
    (_: { newWorkGroupTabs: TabsState }, rowId: string) => rowId,
  ],
  (hasNestedTabs, rowId) =>
    hasNestedTabs.loadingStates[rowId] || {
      isLoading: false,
      error: undefined,
    },
);

// ===== Combined Selectors =====
export const selectAllTabsState = createSelector(
  tabsSelector,
  (state) =>
    state || {
      mainTabs: { activeKey: '0', endpoint: '' },
      nestedTabs: { activeKey: '0', endpoint: '' },
      expandedRows: { keys: [] },
      lazyTabs: { activeTabKeys: {}, loadingStates: {}, tabsData: {} },
      hasNestedTabs: { tabsData: {}, activeKeys: {}, loadingStates: {} },
    },
);

// Селектор для проверки есть ли активные табы
export const selectHasActiveTabs = createSelector(
  selectAllTabsState,
  (state) => {
    const hasLazyTabs = Object.keys(state.lazyTabs.tabsData).length > 0;
    const hasNestedTabs = Object.keys(state.hasNestedTabs.tabsData).length > 0;
    const hasExpandedRows = state.expandedRows.keys.length > 0;

    return hasLazyTabs || hasNestedTabs || hasExpandedRows;
  },
);

// Селектор для получения общего состояния загрузки
export const selectIsAnyTabLoading = createSelector(
  selectAllTabsState,
  (state) => {
    const lazyTabsLoading = Object.values(state.lazyTabs.loadingStates).some(
      (loadingState) => loadingState.isLoading,
    );

    const hasNestedTabsLoading = Object.values(
      state.hasNestedTabs.loadingStates,
    ).some((loadingState) => loadingState.isLoading);

    return lazyTabsLoading || hasNestedTabsLoading;
  },
);
