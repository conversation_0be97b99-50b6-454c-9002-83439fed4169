import { createSelector } from '@reduxjs/toolkit';
import { NestedTabsWithLazyTable } from 'features/DataGrid';
import { selectSelf } from 'shared/lib';
import { TabData, TabsState } from '../types';

// Базовый селектор
const tabsSelector = selectSelf('newWorkGroupTabs');

// ===== Используемые селекторы =====

// ===== Expanded Rows Selectors =====
export const selectExpandedRowsState = createSelector(
  tabsSelector,
  (state) => state?.expandedRows || { keys: [] },
);

export const selectExpandedRowKeys = createSelector(
  selectExpandedRowsState,
  (expandedRows) => expandedRows.keys,
);

// ===== Lazy Tabs Selectors =====
export const selectLazyTabsState = createSelector(
  tabsSelector,
  (state) =>
    state?.lazyTabs || { activeTabKeys: {}, loadingStates: {}, tabsData: {} },
);

export const selectLazyTabsForRow = createSelector(
  [
    selectLazyTabsState,
    (_: { newWorkGroupTabs: TabsState }, rowId: string) => rowId,
  ],
  (lazyTabs, rowId): TabData[] => {
    const tabsForRow: TabData[] = [];

    Object.entries(lazyTabs.tabsData).forEach(([key, tabData]) => {
      if (key.startsWith(`${rowId}-`)) {
        tabsForRow.push(tabData as TabData);
      }
    });

    return tabsForRow.sort((a, b) =>
      String(a.key).localeCompare(String(b.key)),
    );
  },
);

export const selectActiveLazyTabKeys = createSelector(
  [
    selectLazyTabsState,
    (_: { newWorkGroupTabs: TabsState }, rowId: string) => rowId,
  ],
  (lazyTabs, rowId): string[] => lazyTabs.activeTabKeys[rowId] || [],
);

// ===== HasNested Tabs Selectors =====
export const selectHasNestedTabsState = createSelector(
  tabsSelector,
  (state) =>
    state?.hasNestedTabs || { tabsData: {}, activeKeys: {}, loadingStates: {} },
);

export const selectHasNestedTabsForRow = createSelector(
  [
    selectHasNestedTabsState,
    (_: { newWorkGroupTabs: TabsState }, rowId: string) => rowId,
  ],
  (hasNestedTabs, rowId): NestedTabsWithLazyTable | null =>
    hasNestedTabs.tabsData[rowId] || null,
);

export const selectActiveHasNestedKeys = createSelector(
  [
    selectHasNestedTabsState,
    (_: { newWorkGroupTabs: TabsState }, rowId: string) => rowId,
  ],
  (hasNestedTabs, rowId): string[] => hasNestedTabs.activeKeys[rowId] || [],
);

export const selectHasNestedLoadingState = createSelector(
  [
    selectHasNestedTabsState,
    (_: { newWorkGroupTabs: TabsState }, rowId: string) => rowId,
  ],
  (hasNestedTabs, rowId) =>
    hasNestedTabs.loadingStates[rowId] || {
      isLoading: false,
      error: undefined,
    },
);


