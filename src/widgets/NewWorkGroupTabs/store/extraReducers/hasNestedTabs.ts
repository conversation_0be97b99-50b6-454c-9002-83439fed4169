import { ActionReducerMapBuilder } from '@reduxjs/toolkit';
import { TabsState } from '../../types';
import { loadHasNestedTabs, refetchHasNestedTabs } from '../actions';

export const hasNestedTabsExtraReducers = (
  builder: ActionReducerMapBuilder<TabsState>
): void => {
  builder
    // Загрузка hasNested табов
    .addCase(loadHasNestedTabs.pending, (state, action) => {
      const { rowId } = action.meta.arg;
      state.hasNestedTabs.loadingStates[rowId] = {
        isLoading: true,
        error: undefined,
      };
    })
    .addCase(loadHasNestedTabs.fulfilled, (state, action) => {
      const { rowId } = action.meta.arg;
      const tabsData = action.payload;

      state.hasNestedTabs.tabsData[rowId] = tabsData;
      state.hasNestedTabs.loadingStates[rowId] = {
        isLoading: false,
        error: undefined,
      };

      // Инициализируем активные ключи пустым массивом если их нет
      if (!state.hasNestedTabs.activeKeys[rowId]) {
        state.hasNestedTabs.activeKeys[rowId] = [];
      }
    })
    .addCase(loadHasNestedTabs.rejected, (state, action) => {
      const { rowId } = action.meta.arg;
      state.hasNestedTabs.loadingStates[rowId] = {
        isLoading: false,
        error: action.payload || 'Ошибка загрузки',
      };
    })

    // Перезагрузка hasNested табов
    .addCase(refetchHasNestedTabs.pending, (state, action) => {
      const { rowId } = action.meta.arg;
      state.hasNestedTabs.loadingStates[rowId] = {
        isLoading: true,
        error: undefined,
      };
    })
    .addCase(refetchHasNestedTabs.fulfilled, (state, action) => {
      const { rowId } = action.meta.arg;
      const tabsData = action.payload;

      state.hasNestedTabs.tabsData[rowId] = tabsData;
      state.hasNestedTabs.loadingStates[rowId] = {
        isLoading: false,
        error: undefined,
      };
    })
    .addCase(refetchHasNestedTabs.rejected, (state, action) => {
      const { rowId } = action.meta.arg;
      state.hasNestedTabs.loadingStates[rowId] = {
        isLoading: false,
        error: action.payload || 'Ошибка перезагрузки',
      };
    });
};
