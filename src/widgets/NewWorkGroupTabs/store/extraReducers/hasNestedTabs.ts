import { ActionReducerMapBuilder } from '@reduxjs/toolkit';
import { TabsState } from '../../types';
// import { loadHasNestedTabs } from '../actions'; // Будет создан позже

export const hasNestedTabsExtraReducers = (
  builder: ActionReducerMapBuilder<TabsState>
): void => {
  // Пока оставляем пустым, добавим когда создадим actions
  // builder
  //   .addCase(loadHasNestedTabs.pending, (state, action) => {
  //     // Обработка pending состояния
  //   })
  //   .addCase(loadHasNestedTabs.fulfilled, (state, action) => {
  //     // Обработка успешной загрузки
  //   })
  //   .addCase(loadHasNestedTabs.rejected, (state, action) => {
  //     // Обработка ошибки
  //   });
};
