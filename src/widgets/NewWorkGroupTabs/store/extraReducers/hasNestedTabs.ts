import { ActionReducerMapBuilder } from '@reduxjs/toolkit';
import { TabsState } from '../../types';
// import { loadHasNestedTabs } from '../actions'; // Будет создан позже

export const hasNestedTabsExtraReducers = (
  builder: ActionReducerMapBuilder<TabsState>
): void => {
  // Пока оставляем пустым, добавим когда создадим actions
  // Используем builder чтобы избежать ошибки линтера
  // eslint-disable-next-line @typescript-eslint/no-unused-expressions
  builder;
};
