import { ActionReducerMapBuilder } from '@reduxjs/toolkit';
import axios from 'axios';
import { TabsState } from '../../types';
import { fetchTabData } from '../actions';

export const lazyTabsExtraReducers = (
  builder: ActionReducerMapBuilder<TabsState>
): void => {
  builder
    .addCase(fetchTabData.pending, (state, action) => {
      const { parentRowId, tabKey } = action.meta.arg;
      const key = `${parentRowId}-${tabKey}`;

      state.lazyTabs.loadingStates[key] = {
        isLoading: true,
        isLoaded: false,
        isError: false,
      };

      if (state.lazyTabs.tabsData[key]) {
        state.lazyTabs.tabsData[key].tabStatus = {
          isLoading: true,
          isLoaded: false,
          isError: false,
        };
      }
    })
    .addCase(fetchTabData.fulfilled, (state, action) => {
      const { parentRowId, tabKey, page } = action.meta.arg;
      const key = `${parentRowId}-${tabKey}`;
      const result = action.payload;

      state.lazyTabs.loadingStates[key] = {
        isLoading: false,
        isLoaded: true,
        isError: false,
      };

      if (state.lazyTabs.tabsData[key]) {
        state.lazyTabs.tabsData[key] = {
          ...state.lazyTabs.tabsData[key],
          tableData: result,
          pagination: {
            currentPage: page,
            pageSize: result?.pagination?.pageSize || 10,
            total: result?.pagination?.total || result?.rows?.length || 0,
          },
          tabStatus: {
            isLoading: false,
            isLoaded: true,
            isError: false,
          },
        };
      } else {
        // Если таб еще не инициализирован, создаем его
        state.lazyTabs.tabsData[key] = {
          key: tabKey,
          label: '',
          endpoint: action.meta.arg.endpoint,
          tableData: result,
          pagination: {
            currentPage: page,
            pageSize: result?.pagination?.pageSize || 10,
            total: result?.pagination?.total || result?.rows?.length || 0,
          },
          tabStatus: {
            isLoading: false,
            isLoaded: true,
            isError: false,
          },
        };
      }
    })
    .addCase(fetchTabData.rejected, (state, action) => {
      const { parentRowId, tabKey } = action.meta.arg;
      const key = `${parentRowId}-${tabKey}`;

      const errorMessage = axios.isAxiosError(action.payload)
        ? `HTTP ${action.payload.response?.status || 'ошибка'}: ${action.payload.message}`
        : action.error?.message || 'Ошибка загрузки данных таба';

      state.lazyTabs.loadingStates[key] = {
        isLoading: false,
        isLoaded: false,
        isError: true,
        error: errorMessage,
      };

      if (state.lazyTabs.tabsData[key]) {
        state.lazyTabs.tabsData[key].tabStatus = {
          isLoading: false,
          isLoaded: false,
          isError: true,
        };
      }
    });
};
