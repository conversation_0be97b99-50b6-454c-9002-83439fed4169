import { TableProps } from 'antd';
import { ReactNode } from 'react';
import { ColumnFilterActions } from 'features/CustomColumnFilters';
import type {
  NestedTabsWithLazyTable,
  TabsWithLazyTable,
  TableColumnData,
  TableRowData,
} from 'features/DataGrid';
import { PermissionsInitial } from 'entities/Permissions';
import { CabinetStatuses } from 'shared/config/enums';
import { RequestStatuses } from 'shared/model';

export interface NewWorkGroupTabsProps {
  additionalParams: Record<string, string>;
}

export interface HeaderData {
  cabinetId: string;
  defaultTab: string;
  status: CabinetStatuses;
  tabHeaders: TabHeaders[];
  title: string;
}

export type TabsItemsResponse = import('rc-tabs/lib/interface').Tab & {
  endpoint: Endpoint;
  isMarked: boolean;
};

export interface TabHeaders {
  data: TabHeadersData[];
  key: string;
  title: string;
}

export interface TabHeadersData {
  content: string;
  isEditable: boolean;
  isLink: boolean;
  key: string;
  title: string;
}

export interface MainTabsProps {
  activeKey: string;
  counter: number;
  handleChangeTab: (key: string, endpoint: Endpoint) => void;
  tabs: RequestStatuses<TabsItemsResponse[]>;
}

export interface TabsOrTableProps {
  activeEndpoint: Endpoint;
  additionalButtons: AdditionalButton[];
  cabinetId: string;
  clearSelection: Callback;
  columnFilters: ColumnFilterActions;
  columnSorter: ColumnSorter;
  currentPage: number;
  data: TableColumnsAndRowsWithPagination | TabsItemsResponse[];
  getData: (page?: number) => void;
  handleRowData: (value: Partial<TableRowData>) => void;
  handleSelect: (value: TableRowSelection) => void;
  handleTab: (key: string, tableEndpoint: Endpoint) => void;
  isPending: boolean;
  isShowSelects: boolean;
  nestedActiveKey: string;
  nestedData: TableColumnsAndRowsWithPagination | null;
  permissions: Permissions;
  selectedTableKeys: Key[];
  togglePopup: TogglePopup;
  total: number;
}

interface ColumnSorter {
  handleSort: (sortParams: SortObject, isNested: boolean) => void;
  sortOrder: SortObject;
}

export type SortObject = { direction: SortOrder; sort: string } | null;

export type SortOrder = 'DESC' | 'ASC';

export interface PopupsProps {
  cabinetId: string;
  isFullFilesAccess: boolean;
  permissions: Permissions;
  popup: Record<
    keyof typeof import('widgets/NewWorkGroupTabs/config').popupsState,
    boolean
  >;
  refetch: Callback;
  refetchHeader: Callback;
  rowAdditional: {
    data: Partial<TableRowData>;
    reset: Callback;
  };
  togglePopup: TogglePopup;
}

export type CustomRowRender = (
  text: string,
  row: TableRowData,
  column: TableColumnData,
  refetch: Callback,
  togglePopup: TogglePopup,
  handleRow: HandleRow,
  tableEndpoint: Endpoint,
  permissions: Permissions,
  isRequest?: boolean,
) => JSX.Element;

export type TogglePopup = (
  name: keyof typeof import('widgets/NewWorkGroupTabs/config').popupsState,
) => void;

export type AdditionalParams = Partial<TableRowData>;

export type HandleRow = (value: AdditionalParams) => void;

export type CollapseWithLazyTableProps = (
  nestedContent: NestedTabsWithLazyTable,
  customRowRender: CustomRowRender,
  parentRow: TableRowData,
  refetch: Callback,
  togglePopup: TogglePopup,
  handleRow: HandleRow,
  activeEndpoint: Endpoint,
  permissions: Permissions,
  isRequest?: boolean,
) => ReactNode;

export type TableConfigWithLazyTabsProps = (
  collapseWithLazyTable: CollapseWithLazyTableProps,
  customRowRender: CustomRowRender,
  refetch: Callback,
  togglePopup: TogglePopup,
  handleRow: HandleRow,
  activeEndpoint: Endpoint,
  permissions: Permissions,
  isRequest?: boolean,
  rows?: TableRowData[],
  expandedRowKeys?: React.Key[],
  onExpandedRowsChange?: (expandedKeys: readonly React.Key[]) => void,
  cabinetId?: string,
) => TableProps<TableRowData>;

export type Permissions = PermissionsInitial;

export type TabData = TabsWithLazyTable;

export interface UseLazyTabsDataReturn {
  activeTabKeys: string[];
  handlePageChange: (tab: TabData, page: number) => Promise<void>;
  handleTabToggle: (keys: string | string[]) => Promise<void>;
  loadTabData: (tabKey: string, page?: number) => Promise<void>;
  tabsData: TabData[];
}

// Redux store types
export interface TabLoadingState {
  isError: boolean;
  isLoaded: boolean;
  isLoading: boolean;
  error?: string;
}

export interface HasNestedTabsState {
  tabsData: Record<string, NestedTabsWithLazyTable>;
  activeKeys: Record<string, string[]>;
  loadingStates: Record<string, { isLoading: boolean; error?: string }>;
}

export interface MainTabsState {
  activeKey: string;
  endpoint: string;
}

export interface NestedTabsState {
  activeKey: string;
  endpoint: string;
}

export interface ExpandedRowsState {
  keys: string[];
}

export interface LazyTabsState {
  // Активные табы для каждой строки
  activeTabKeys: Record<string, string[]>;
  // Состояния загрузки для каждого таба
  loadingStates: Record<string, TabLoadingState>;
  // Ключ: `${parentRowId}-${tabKey}`, значение: данные таба
  tabsData: Record<string, TabData>;
}

export interface TabsState {
  mainTabs: MainTabsState;
  nestedTabs: NestedTabsState;
  expandedRows: ExpandedRowsState;
  lazyTabs: LazyTabsState;
  hasNestedTabs: HasNestedTabsState;
}

export enum TabType {
  EAGER = 'eager',
  HAS_NESTED = 'hasNested',
  LAZY = 'lazy',
}
