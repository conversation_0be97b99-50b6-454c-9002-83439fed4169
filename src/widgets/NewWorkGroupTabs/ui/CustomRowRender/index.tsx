import {
  AlertTwoTone,
  CheckCircleTwoTone,
  CheckOutlined,
} from '@ant-design/icons';
import { Tooltip, Typography } from 'antd';
import classNames from 'classnames';
import { CustomRowRender, NWGConfig, NWGStore } from 'widgets/NewWorkGroupTabs';
import { ButtonsContainer } from 'shared/ui';

import styles from './styles.module.scss';

export const customRowRender: CustomRowRender = (
  text,
  row,
  column,
  refetch,
  togglePopup,
  handleRow,
  tableEndpoint,
  permissions,
  isRequest,
  smartRefetch,
): JSX.Element => {
  const [
    downloadButtons,
    deleteButtons,
    mainFileButtons,
    statusButtons,
    requestInternalEdit,
    editEpcButtons,
    defCommentButtons,
    fileBindButtons,
    unlinkDefButtons,
  ] = NWGStore.hooks.useTableSingleButtons(
    row,
    refetch,
    handleRow,
    togglePopup,
    tableEndpoint,
    permissions,
    isRequest,
    smartRefetch,
  );

  if (NWGConfig.constants.UKEP_UNEP.includes(column.dataIndex)) {
    return row[column.dataIndex] !== null && row[column.dataIndex] !== '' ? (
      <CheckCircleTwoTone twoToneColor="#52c41a" />
    ) : (
      <div />
    );
  }

  if (
    NWGConfig.constants.IS_MAIN.includes(column.dataIndex) &&
    column.columnType !== 'mark'
  ) {
    return row[column.dataIndex] !== null &&
      String(row[column.dataIndex]) === NWGConfig.constants.IS_MAIN_VALUE ? (
      <div className={styles.feed}>
        <CheckCircleTwoTone twoToneColor="#52c41a" />
        <Typography.Text>Основной</Typography.Text>
      </div>
    ) : (
      <div />
    );
  }

  switch (column.columnType) {
    case 'Feed':
      return (
        <div className={styles.feed}>
          {row.rowId?.isRead ? (
            <CheckCircleTwoTone twoToneColor="#52c41a" />
          ) : (
            <AlertTwoTone twoToneColor="#ff7d7d" />
          )}
          <Typography.Text>
            {row.rowId?.isRead ? 'Прочитано' : 'Не прочитано'}
          </Typography.Text>
        </div>
      );
    case 'downloadButton':
      return (
        <ButtonsContainer
          buttons={downloadButtons}
          className={styles.buttons}
        />
      );
    case 'mark':
      return row.rowId?.format === NWGConfig.constants.VALID_MAIN_FORMAT ? (
        row.rowId?.isMain ? (
          <Typography.Text>
            <CheckOutlined className={styles.check} />
            Основной файл
          </Typography.Text>
        ) : row.rowId?.isMainEditable ? (
          <ButtonsContainer
            buttons={mainFileButtons}
            className={styles.buttons}
          />
        ) : (
          <div />
        )
      ) : (
        <div />
      );
    case 'deleteButton':
      return (
        <ButtonsContainer
          buttons={deleteButtons}
          className={styles.buttons}
          disableAnimation
        />
      );
    case 'statusButton':
      return (
        <ButtonsContainer
          buttons={statusButtons}
          className={styles.buttons}
          disableAnimation
        />
      );
    case 'requestInternalEdit':
      return (
        <ButtonsContainer
          className={styles.buttons}
          disableAnimation
          buttons={requestInternalEdit}
        />
      );
    case 'editEpc':
      return (
        <ButtonsContainer
          buttons={editEpcButtons}
          className={styles.buttons}
          disableAnimation
        />
      );
    case 'defComment':
      return (
        <ButtonsContainer
          buttons={defCommentButtons}
          className={styles.buttons}
          disableAnimation
        />
      );
    case 'fileBind':
      return (
        <ButtonsContainer
          buttons={fileBindButtons}
          className={styles.buttons}
          disableAnimation
        />
      );
    case 'unlinkDef':
      return (
        <ButtonsContainer
          buttons={unlinkDefButtons}
          className={styles.buttons}
          disableAnimation
        />
      );
    default:
      return (
        <Tooltip
          title={row?.hint?.[column.dataIndex] || text}
          placement="topLeft"
        >
          <span
            className={classNames(
              Object.hasOwn(row?.rowId || {}, 'isActual') &&
                !row.rowId?.isActual &&
                styles.rowUnActual,
            )}
          >
            {text}
          </span>
        </Tooltip>
      );
  }
};
