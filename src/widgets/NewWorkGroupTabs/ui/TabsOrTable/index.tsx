import { Tabs, TableProps } from 'antd';
import classNames from 'classnames';
import type { FC } from 'react';
import { useState, useCallback, useEffect } from 'react';
import {
  NWGConfig,
  TabsOrTableProps,
  NWGLib,
  NWGStore,
} from 'widgets/NewWorkGroupTabs';
import {
  customColumnFilters,
  ColumnFilters,
} from 'features/CustomColumnFilters';
import { DataGrid, TableRowData, TableColumnData } from 'features/DataGrid';
import { apiUrls } from 'shared/api';
import { TableSelectionInfo } from 'shared/ui';
import { collapseWithLazyTable } from '../CollapseWithLazyTable';
import { customRowRender } from '../CustomRowRender';

import styles from './styles.module.scss';

export const TabsOrTable: FC<TabsOrTableProps> = ({
  data,
  additionalButtons,
  cabinetId,
  handleSelect,
  nestedActiveKey,
  handleTab,
  nestedData,
  isPending,
  total,
  getData,
  currentPage,
  selectedTableKeys,
  isShowSelects,
  activeEndpoint,
  columnFilters,
  togglePopup,
  handleRowData,
  columnSorter,
  permissions,
  clearSelection,
  smartRefetch,
}) => {
  const { tableConfigWithLazyTabs, customColumnsWidth } = NWGLib;
  const [filterDropdown, setFilterDropdown] = useState('');
  const [nestedTabsActiveKeys, setNestedTabsActiveKeys] = useState<
    Record<string, string[]>
  >({});

  const { expandedRowKeys, onExpandedRowsChange } =
    NWGStore.hooks.useExpandedRows();
  const { NestedTabsProvider } = NWGStore.hooks;

  const handleSetActiveTabKeys = useCallback(
    (rowId: string, keys: string[]) => {
      setNestedTabsActiveKeys((prev) => ({
        ...prev,
        [rowId]: keys,
      }));
    },
    [],
  );

  useEffect(() => {
    // Сбрасываем раскрытые строки при смене таба или страницы
    onExpandedRowsChange([]);
    setNestedTabsActiveKeys({});
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeEndpoint, currentPage]);

  const getTableConfig = (): TableProps<TableRowData> =>
    tableConfigWithLazyTabs(
      collapseWithLazyTable,
      customRowRender,
      () => getData(currentPage),
      togglePopup,
      handleRowData,
      activeEndpoint,
      permissions,
      activeEndpoint === apiUrls.workGroup.nestedTabsEndpoints.request,
      Array.isArray(data) && nestedData
        ? nestedData?.rows || []
        : (data as TableColumnsAndRowsWithPagination)?.rows || [],
      expandedRowKeys,
      onExpandedRowsChange,
      cabinetId,
      smartRefetch,
    );

  const paginationProps = {
    show: true,
    pageSize: 10,
    onPaginatorClick: (page: number) => {
      // Сбрасываем состояние табов при пагинации
      smartRefetch?.refetchOnPagination();
      getData(page);
    },
    currentPage,
    hideOnSinglePage: true,
    total,
    disabled: isPending,
    showSizeChanger: false,
  };

  const additionalClassNames = {
    container: styles.container,
    table: styles.containerTable,
  };

  const columnsProps = (column: TableColumnData): Partial<TableColumnData> =>
    ({
      ...column,
      ...(column.sortable && {
        onLazyLoadSort: (newSortOrder: 'ascend' | 'descend' | undefined) => {
          columnSorter.handleSort(
            newSortOrder
              ? {
                  sort: column?.dataIndex ?? '',
                  direction: newSortOrder === 'ascend' ? 'ASC' : 'DESC',
                }
              : null,
            Boolean(Array.isArray(data) && nestedData !== null),
          );
        },
        lazySortOrder:
          columnSorter.sortOrder?.sort === column.dataIndex
            ? columnSorter.sortOrder?.direction
            : undefined,
      }),

      ...(Object.hasOwn(column, 'filterType') &&
        column?.filterType !== null && {
          filters: [],
          filteredValue: [],
          onFilterDropdownOpenChange: (value: boolean) =>
            value ? setFilterDropdown(column.dataIndex) : setFilterDropdown(''),
          filterDropdownOpen: column.dataIndex === filterDropdown,
          filterDropdown: () =>
            customColumnFilters(
              column,
              (value: ColumnFilters) => {
                setFilterDropdown('');
                columnFilters.onSubmit(
                  value,
                  Array.isArray(data) ? 'nested' : 'main',
                );
                clearSelection();
              },
              columnFilters.filters,
              (value: ColumnFilters) => {
                setFilterDropdown('');
                columnFilters.onReset(
                  value,
                  Array.isArray(data) ? 'nested' : 'main',
                );
                clearSelection();
              },
            ),
        }),

      ...(column.columnType !== NWGConfig.constants.DEFAULT_TYPE && {
        width: customColumnsWidth(column?.columnType || ''),
        align: 'center',
        ...(column.columnType !== NWGConfig.constants.FEED_TYPE && {
          fixed: 'right',
        }),
      }),
      ...((NWGConfig.constants.UKEP_UNEP.includes(column.dataIndex) ||
        NWGConfig.constants.IS_MAIN.includes(column.dataIndex)) && {
        align: 'center',
      }),
      filtered: columnFilters.filters.some(
        (item) => item.column === column.dataIndex,
      ),
      render: (text: string, row: TableRowData) =>
        customRowRender(
          text,
          row,
          column,
          () => getData(currentPage),
          togglePopup,
          handleRowData,
          activeEndpoint,
          permissions,
          activeEndpoint === apiUrls.workGroup.nestedTabsEndpoints.request,
          smartRefetch,
        ),
    } as Partial<TableColumnData>);

  const tableAdditionProps = {
    rowClassName: (record: TableRowData) =>
      classNames({
        [styles.errorRow]: record.statusId === 'request_error',
      }),
    loading: isPending,
    ...getTableConfig(),
    ...(isShowSelects && {
      rowSelection: {
        preserveSelectedRowKeys: true,
        selectedRowKeys: selectedTableKeys,
        getCheckboxProps: (row: TableRowData) => ({
          disabled: Boolean(row.rowId?.isRead),
        }),
        onChange: (keys: Key[], rows: TableRowData[]) =>
          handleSelect({ keys, rows }),
      },
    }),
  };

  const columns =
    nestedData !== null && Array.isArray(data)
      ? nestedData?.columns || []
      : (data as TableColumnsAndRowsWithPagination)?.columns || [];

  const rows =
    nestedData !== null && Array.isArray(data)
      ? nestedData.rows || []
      : (data as TableColumnsAndRowsWithPagination).rows || [];

  const nestedTableProps = {
    tableAdditionProps: getTableConfig(),
  };

  return (
    <NestedTabsProvider
      value={{
        activeTabKeys: nestedTabsActiveKeys,
        setActiveTabKeys: handleSetActiveTabKeys,
      }}
    >
      {Array.isArray(data) && data.length !== 0 && (
        <Tabs
          items={data}
          activeKey={nestedActiveKey}
          onChange={(key) =>
            handleTab(key, data?.[Number(key) - 1]?.endpoint || '')
          }
          type="card"
        />
      )}

      <DataGrid
        key={activeEndpoint}
        hideColumnSearch
        hideSorter
        footerAdditionalComponent={
          <TableSelectionInfo selectedRowCount={selectedTableKeys.length} />
        }
        additionalClassNames={additionalClassNames}
        paginationProps={paginationProps}
        columnsProps={columnsProps}
        tableAdditionProps={tableAdditionProps}
        additionalButtons={additionalButtons.filter(
          (button) => button.title !== '',
        )}
        resizableProps={{ isActive: true }}
        columns={columns}
        rows={rows}
        nestedTableProps={nestedTableProps}
      />
    </NestedTabsProvider>
  );
};
