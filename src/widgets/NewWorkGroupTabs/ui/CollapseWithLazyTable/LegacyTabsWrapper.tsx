import { useEffect } from 'react';
import type {
  CustomRowRender,
  HandleRow,
  TogglePopup,
  Permissions,
} from 'widgets/NewWorkGroupTabs';
import { NWGStore } from 'widgets/NewWorkGroupTabs';
import { TableRowData, NestedTabsWithLazyTable } from 'features/DataGrid';
import { ApiContainer } from 'shared/ui';

interface LegacyTabsWrapperProps {
  cabinetId?: string;
  collapseWithLazyTable: (
    nestedContent: NestedTabsWithLazyTable,
    customRowRender: CustomRowRender,
    row: TableRowData,
    refetch: Callback,
    togglePopup: TogglePopup,
    handleRow: HandleRow,
    activeEndpoint: Endpoint,
    permissions: Permissions,
    isRequest?: boolean,
    smartRefetch?: import('../../types').SmartRefetch,
  ) => React.ReactNode;
  customRowRender: CustomRowRender;
  endpoint: Endpoint;
  handleRow: HandleRow;
  isRequest?: boolean;
  permissions: Permissions;
  refetch: Callback;
  row: TableRowData;
  smartRefetch?: import('../../types').SmartRefetch;
  togglePopup: TogglePopup;
}

export const LegacyTabsWrapper: React.FC<LegacyTabsWrapperProps> = ({
  cabinetId,
  collapseWithLazyTable,
  customRowRender,
  endpoint,
  handleRow,
  isRequest,
  permissions,
  refetch,
  row,
  smartRefetch,
  togglePopup,
}) => {
  const rowId = String(row.rowId?.id || '');
  const { tabsData, isLoading, error, loadTabs } = NWGStore.hooks.useHasNestedTabs(rowId, endpoint, cabinetId);

  useEffect(() => {
    // Загружаем табы только если их еще нет
    if (!row.nestedTable && !tabsData && !isLoading) {
      loadTabs();
    } else if (row.nestedTable && !tabsData) {
      // Если данные уже есть в строке, сохраняем их в Redux
      // Это нужно для обратной совместимости
      row.nestedTable = row.nestedTable as NestedTabsWithLazyTable;
    }
  }, [row.key, endpoint, cabinetId, tabsData, isLoading, loadTabs, row.nestedTable]);

  // Используем данные из Redux или из строки для обратной совместимости
  const currentTabsData = tabsData || (row.nestedTable as NestedTabsWithLazyTable);

  return (
    <ApiContainer
      error={Boolean(error)}
      isPending={isLoading}
      errorTitle={error || 'Ошибка загрузки вложенных данных'}
      refresh={() => {
        loadTabs();
      }}
    >
      {currentTabsData &&
        collapseWithLazyTable(
          currentTabsData,
          customRowRender,
          row,
          refetch,
          togglePopup,
          handleRow,
          endpoint,
          permissions,
          isRequest,
          smartRefetch,
        )}
    </ApiContainer>
  );
};
