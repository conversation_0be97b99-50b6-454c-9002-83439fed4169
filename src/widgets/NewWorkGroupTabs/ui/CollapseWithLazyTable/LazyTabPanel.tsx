import { memo, ReactNode } from 'react';
import {
  NWGLib,
  CustomRowRender,
  HandleRow,
  TogglePopup,
  Permissions,
  TabData,
} from 'widgets/NewWorkGroupTabs';
import { DataGrid, TableRowData } from 'features/DataGrid';
import { ApiContainer } from 'shared/ui';

interface LazyTabPanelProps {
  activeEndpoint: Endpoint;
  customRowRender: CustomRowRender;
  handleRow: HandleRow;
  onPageChange: (page: number) => Promise<void>;
  permissions: Permissions;
  refetch: Callback;
  tab: TabData;
  togglePopup: TogglePopup;
  isRequest?: boolean;
  renderExpandedRow?: (row: TableRowData) => ReactNode;
  smartRefetch?: import('../../types').SmartRefetch;
}

export const LazyTabPanel = memo<LazyTabPanelProps>(
  ({
    tab,
    customRowRender,
    handleRow,
    togglePopup,
    permissions,
    activeEndpoint,
    onPageChange,
    refetch,
    renderExpandedRow,
    isRequest,
  }) => {
    const { customColumnsWidth } = NWGLib;

    const handlePageChangeInternal = async (page: number): Promise<void> => {
      await onPageChange(page);
    };

    const hasNestedTables = tab.tableData?.rows?.some((row: TableRowData) =>
      Object.hasOwn(row, 'nestedTable'),
    );

    return (
      <ApiContainer
        error={Boolean(tab.tabStatus?.isError)}
        isPending={tab.tableData ? false : Boolean(tab.tabStatus?.isLoading)}
        errorTitle="Произошла ошибка при загрузке вложенной таблицы"
        refresh={() => onPageChange(1)}
      >
        {tab.tableData && (
          <DataGrid
            resizableProps={{ isActive: true }}
            hideColumnSearch
            hideSorter
            columns={
              Array.isArray(tab.tableData.columns)
                ? tab.tableData.columns.map((column) => ({
                    ...column,
                    ...(column.columnType !== 'String' && {
                      fixed: 'right',
                      width: customColumnsWidth(column?.columnType || ''),
                      align: 'center',
                      hideSorter: true,
                      hideColumnSearch: true,
                    }),
                    render: (text: string, row: TableRowData) =>
                      customRowRender(
                        text,
                        row,
                        column,
                        refetch,
                        togglePopup,
                        handleRow,
                        activeEndpoint,
                        permissions,
                        isRequest,
                      ),
                  }))
                : []
            }
            rows={Array.isArray(tab.tableData.rows) ? tab.tableData.rows : []}
            tableAdditionProps={{
              loading: tab.tabStatus?.isLoading,
              size: 'small',
              scroll: { x: '100%', y: '100%' },
              ...(hasNestedTables &&
                renderExpandedRow && {
                  expandable: {
                    rowExpandable: (row: TableRowData) =>
                      Object.hasOwn(row, 'nestedTable'),
                    expandedRowRender: renderExpandedRow,
                  },
                }),
            }}
            paginationProps={{
              show: true,
              disabled: tab.tabStatus?.isLoading,
              currentPage: tab.pagination?.currentPage || 1,
              pageSize: tab.pagination?.pageSize || 10,
              total: tab.pagination?.total || 0,
              showSizeChanger: false,
              hideOnSinglePage: true,
              onPaginatorClick: handlePageChangeInternal,
            }}
          />
        )}
      </ApiContainer>
    );
  },
);

LazyTabPanel.displayName = 'LazyTabPanel';
