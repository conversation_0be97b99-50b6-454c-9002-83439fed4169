import { memo, ReactNode } from 'react';
import {
  NWGLib,
  CustomRowRender,
  HandleRow,
  TogglePopup,
  Permissions,
  TabData,
} from 'widgets/NewWorkGroupTabs';
import { DataGrid, TableRowData } from 'features/DataGrid';

interface PreloadedTabPanelProps {
  activeEndpoint: Endpoint;
  customRowRender: CustomRowRender;
  handleRow: HandleRow;
  isRequest?: boolean;
  permissions: Permissions;
  refetch: Callback;
  renderExpandedRow?: (row: TableRowData) => ReactNode;
  smartRefetch?: import('../../types').SmartRefetch;
  tab: TabData;
  togglePopup: TogglePopup;
}

export const PreloadedTabPanel = memo<PreloadedTabPanelProps>(
  ({
    tab,
    customRowRender,
    handleRow,
    togglePopup,
    permissions,
    activeEndpoint,
    renderExpandedRow,
    refetch,
    isRequest,
  }) => {
    const { customColumnsWidth } = NWGLib;

    if (!tab.tableData) {
      return null;
    }

    const hasNestedTables = tab.tableData.rows?.some((row: TableRowData) =>
      Object.hasOwn(row, 'nestedTable'),
    );

    return (
      <DataGrid
        resizableProps={{ isActive: true }}
        hideColumnSearch
        hideSorter
        columns={
          Array.isArray(tab.tableData.columns)
            ? tab.tableData.columns.map((column) => ({
                ...column,
                ...(column.columnType !== 'String' && {
                  fixed: 'right',
                  width: customColumnsWidth(column?.columnType || ''),
                  align: 'center',
                  hideSorter: true,
                  hideColumnSearch: true,
                }),
                render: (text: string, row: TableRowData) =>
                  customRowRender(
                    text,
                    row,
                    column,
                    refetch,
                    togglePopup,
                    handleRow,
                    activeEndpoint,
                    permissions,
                    isRequest,
                  ),
              }))
            : []
        }
        rows={Array.isArray(tab.tableData.rows) ? tab.tableData.rows : []}
        tableAdditionProps={{
          size: 'small',
          scroll: { x: '100%', y: '100%' },
          ...(hasNestedTables &&
            renderExpandedRow && {
              expandable: {
                rowExpandable: (row: TableRowData) =>
                  Object.hasOwn(row, 'nestedTable'),
                expandedRowRender: renderExpandedRow,
              },
            }),
        }}
        paginationProps={{
          show: false,
          currentPage: 1,
          pageSize: tab.tableData.rows?.length || 10,
          total: tab.tableData.rows?.length || 0,
          hideOnSinglePage: true,
        }}
      />
    );
  },
);

PreloadedTabPanel.displayName = 'PreloadedTabPanel';
