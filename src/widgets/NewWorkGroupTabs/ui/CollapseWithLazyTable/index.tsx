import { Collapse } from 'antd';
import { ReactNode, memo } from 'react';
import {
  CollapseWithLazyTableProps,
  NWGLib,
  CustomRowRender,
  HandleRow,
  TogglePopup,
  Permissions,
  NWGStore,
} from 'widgets/NewWorkGroupTabs';
import { TableRowData, NestedTabsWithLazyTable } from 'features/DataGrid';
import { LazyTabPanel } from './LazyTabPanel';
import { PreloadedTabPanel } from './PreloadedTabPanel';

export { LegacyTabsWrapper } from './LegacyTabsWrapper';

interface CollapseWithLazyTableComponentProps {
  activeEndpoint: Endpoint;
  customRowRender: CustomRowRender;
  handleRow: HandleRow;
  nestedContent: NestedTabsWithLazyTable;
  parentRow: TableRowData;
  permissions: Permissions;
  refetch: Callback;
  togglePopup: TogglePopup;
  isRequest?: boolean;
  smartRefetch?: import('../../types').SmartRefetch;
}

const CollapseWithLazyTableComponent =
  memo<CollapseWithLazyTableComponentProps>(
    ({
      activeEndpoint,
      customRowRender,
      handleRow,
      isRequest,
      nestedContent,
      parentRow,
      permissions,
      refetch,
      smartRefetch,
      togglePopup,
    }) => {
      const { renderCollapseTitle } = NWGLib;
      const rowId = String(parentRow.rowId?.id || '');

      const { tabsData, activeTabKeys, handleTabToggle, handlePageChange } =
        NWGStore.hooks.useLazyTabsData(nestedContent.tabs, rowId);

      const renderExpandedRow = (row: TableRowData): ReactNode => (
        <CollapseWithLazyTableComponent
          nestedContent={row.nestedTable as NestedTabsWithLazyTable}
          customRowRender={customRowRender}
          parentRow={row}
          refetch={refetch}
          togglePopup={togglePopup}
          handleRow={handleRow}
          activeEndpoint={activeEndpoint}
          permissions={permissions}
          isRequest={isRequest}
        />
      );

      const isPreloadedTab = (tabKey: string): boolean => {
        const originalTab = nestedContent.tabs.find((t) => t.key === tabKey);
        return !!originalTab?.tableData;
      };

      return (
        <Collapse activeKey={activeTabKeys} onChange={handleTabToggle}>
          {tabsData.map((tab) => (
            <Collapse.Panel
              key={tab.key}
              header={renderCollapseTitle(
                tab.label,
                parentRow.rowId?.additional || '',
              )}
            >
              {isPreloadedTab(String(tab.key)) ? (
                <PreloadedTabPanel
                  activeEndpoint={activeEndpoint}
                  customRowRender={customRowRender}
                  handleRow={handleRow}
                  isRequest={isRequest}
                  permissions={permissions}
                  refetch={refetch}
                  renderExpandedRow={renderExpandedRow}
                  smartRefetch={smartRefetch}
                  tab={tab}
                  togglePopup={togglePopup}
                />
              ) : (
                <LazyTabPanel
                  activeEndpoint={activeEndpoint}
                  customRowRender={customRowRender}
                  handleRow={handleRow}
                  isRequest={isRequest}
                  onPageChange={(page) => handlePageChange(tab, page)}
                  permissions={permissions}
                  refetch={refetch}
                  renderExpandedRow={renderExpandedRow}
                  smartRefetch={smartRefetch}
                  tab={tab}
                  togglePopup={togglePopup}
                />
              )}
            </Collapse.Panel>
          ))}
        </Collapse>
      );
    },
  );

CollapseWithLazyTableComponent.displayName = 'CollapseWithLazyTableComponent';

const MemoizedCollapseWithLazyTableComponent = memo(
  CollapseWithLazyTableComponent,
);

export const collapseWithLazyTable: CollapseWithLazyTableProps = (
  nestedContent,
  customRowRender,
  parentRow,
  refetch,
  togglePopup,
  handleRow,
  activeEndpoint,
  permissions,
  isRequest,
  smartRefetch,
): ReactNode => (
  <MemoizedCollapseWithLazyTableComponent
    activeEndpoint={activeEndpoint}
    customRowRender={customRowRender}
    handleRow={handleRow}
    isRequest={isRequest}
    nestedContent={nestedContent}
    parentRow={parentRow}
    permissions={permissions}
    refetch={refetch}
    smartRefetch={smartRefetch}
    togglePopup={togglePopup}
  />
);
