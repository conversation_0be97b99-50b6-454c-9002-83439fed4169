export interface TabRequestParams {
  [key: string]: string | number;
  pageNumber: number;
  pageSize: number;
}

export const getTabRequestParams = (
  endpoint: Endpoint,
  parentId: string | number,
  page = 1,
  pageSize = 10,
): TabRequestParams => {
  const baseParams: TabRequestParams = {
    pageNumber: page,
    pageSize,
  };

  // Убираем начальный слеш, если есть
  const normalizedEndpoint = endpoint.startsWith('/')
    ? endpoint.slice(1)
    : endpoint;

  switch (normalizedEndpoint) {
    /* Входящие файлы заявки */
    case 'krg3_request_input_files':
      return { ...baseParams, requestId: parentId };
    /* Входящие файлы к пункту */
    case 'krg3_request_item_input_files':
      return { ...baseParams, requestId: parentId };
    /* 'Файлы в составе пакета' */
    case 'krg3_package_files_def':
      return { ...baseParams, packageId: parentId };
    default:
      throw new Error(
        `Неизвестный эндпоинт в getTabRequestParams: ${endpoint}`,
      );
  }
};
