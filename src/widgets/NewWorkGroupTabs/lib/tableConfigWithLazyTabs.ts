import { TableProps } from 'antd';
import React from 'react';
import type { TableConfigWithLazyTabsProps } from 'widgets/NewWorkGroupTabs';
import { NestedTabsWithLazyTable, TableRowData } from 'features/DataGrid';
import { LegacyTabsWrapper } from '../ui';

export const tableConfigWithLazyTabs: TableConfigWithLazyTabsProps = (
  collapseWithLazyTable,
  customRowRender,
  refetch,
  togglePopup,
  handleRow,
  activeEndpoint,
  permissions,
  isRequest,
  rows?,
  expandedRowKeys?,
  onExpandedRowsChange?,
  cabinetId?,
  smartRefetch?,
): TableProps<TableRowData> => {
  // Проверяем наличие строк с nestedTable или hasNested флагом
  const hasExpandableRows =
    rows &&
    rows.some(
      (row) => Object.hasOwn(row, 'nestedTable') || row.rowId?.hasNested,
    );

  return {
    size: 'middle',
    pagination: false,
    scroll: { x: '100%', y: '100%' },
    ...(hasExpandableRows && {
      expandable: {
        rowExpandable: (row) =>
          Object.hasOwn(row, 'nestedTable') || !!row.rowId?.hasNested,
        expandedRowKeys,
        onExpandedRowsChange,
        expandedRowRender: (row) => {
          // Если есть nestedTable - используем напрямую
          if (row.nestedTable) {
            return collapseWithLazyTable(
              row.nestedTable as NestedTabsWithLazyTable,
              customRowRender,
              row,
              refetch,
              togglePopup,
              handleRow,
              activeEndpoint,
              permissions,
              isRequest,
              smartRefetch,
            );
          }

          // Если hasNested флаг - используем обертку для загрузки табов
          if (row.rowId?.hasNested) {
            return React.createElement(LegacyTabsWrapper, {
              cabinetId,
              collapseWithLazyTable,
              customRowRender,
              endpoint: activeEndpoint,
              handleRow,
              isRequest,
              permissions,
              refetch,
              row,
              smartRefetch,
              togglePopup,
            });
          }

          return null;
        },
      },
    }),
  };
};
