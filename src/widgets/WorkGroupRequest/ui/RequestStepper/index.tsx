import { StepProps, Steps } from 'antd';
import type { FC } from 'react';
import { useMemo } from 'react';
import {
  WorkGroupRequestStore,
  RequestStepper as RequestStepperType,
  Request,
} from 'widgets/WorkGroupRequest';
import { TableRowData } from 'features/DataGrid';
import { useAppSelector, useCreateSliceActions } from 'shared/model';
import { DynamicForm } from '../DynamicForm';
import { MainData } from '../MainData';
import { SaddForm } from '../SaddForm';
import { UploadWithTree } from '../UploadWithTree';
import styles from './styles.module.scss';

interface RequestStepperProps {
  handlePopup: (
    name: 'saddTable' | 'requestBind' | 'countDownMessage',
    status?: boolean,
  ) => void;
  isCreation: boolean;
  isDisabled: boolean;
  request: Request;
  type: 'request' | 'internal';
  additionalParams?: Partial<TableRowData>;
  cabinetId?: string;
}

export const RequestStepper: FC<RequestStepperProps> = ({
  type,
  isCreation,
  isDisabled,
  handlePopup,
  cabinetId,
  additionalParams,
  request,
}) => {
  const {
    employeeCounter,
    mainDataValidation,
    formDataValidation,
    filesCount,
  } = WorkGroupRequestStore.hooks.useRequestStepper({
    request,
    type,
  });

  const requestStepper: RequestStepperType[] = useMemo(
    () => [
      {
        title: 'Общие данные',
        key: '1',
        status: mainDataValidation.hasError ? 'error' : undefined,
        element: (
          <MainData type={type} isDisabled={isCreation ? false : isDisabled} />
        ),
        subTitle: mainDataValidation.errorMessage,
      },
      {
        title: 'Исполнители САДД БР',
        key: '2',
        subTitle: `Количество: ${employeeCounter}`,
        element: (
          <SaddForm
            handlePopup={handlePopup}
            isDisabled={isCreation ? false : isDisabled}
          />
        ),
      },
      {
        title: type === 'request' && !request.isNotice ? 'Пункты заявки' : '',
        key: '3',
        status: formDataValidation.hasError ? 'error' : undefined,
        element: <DynamicForm isDisabled={isCreation ? false : isDisabled} />,
        subTitle:
          formDataValidation.errorMessage ||
          `Количество: ${request.formData.length}`,
      },
      {
        title: 'Файлы',
        key: '4',
        element: (
          <UploadWithTree
            cabinetId={cabinetId!}
            isDisabled={isCreation ? false : isDisabled}
            additionalParams={additionalParams!}
          />
        ),
        subTitle: `Количество: ${filesCount}`,
      },
    ],
    [
      mainDataValidation,
      employeeCounter,
      type,
      request.isNotice,
      formDataValidation,
      request.formData.length,
      filesCount,
      isCreation,
      isDisabled,
      handlePopup,
      cabinetId,
      additionalParams,
    ],
  );

  const filteredStepper = useMemo(
    () => requestStepper.filter((step) => step.title !== ''),
    [requestStepper],
  );

  const { setStep } = useCreateSliceActions(
    WorkGroupRequestStore.reducers.slice.actions,
  );

  const currentRequest = useAppSelector(
    WorkGroupRequestStore.selectors.requestDataSelector,
  );

  const items = filteredStepper.map<StepProps>((step) => ({
    className: styles.step,
    status: step.status,
    title: step.title,
    key: step.key,
    description: step.subTitle,
  }));

  return (
    <>
      <Steps items={items} current={currentRequest.step} onChange={setStep} />
      <div className={styles.content}>
        {filteredStepper[currentRequest.step].element}
      </div>
    </>
  );
};
