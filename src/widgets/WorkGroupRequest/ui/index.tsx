import { Divider } from 'antd';
import type { FC } from 'react';
import {
  WorkGroupRequestProps,
  WorkGroupRequestStore,
  SubmitParams,
  WorkGroupRequestConfig,
  RequestProps,
} from 'widgets/WorkGroupRequest';
import { CustomMessageModalWithOutClose } from 'features/CustomMessageModalWithOutClose';
import { useAppSelector, usePopupsToggle } from 'shared/model';
import { AppPopup } from 'shared/ui';
import { ButtonsContainer } from 'shared/ui/ButtonsContainer';
import { BlockedUserName } from './BlockedUserName';
import { CreationHeader } from './CreationHeader';
import { EditCountDown } from './EditCountDown';
import { PreparedStatusSwitcher } from './PreparedStatusSwitcher';
import { RequestBindTable } from './RequestBindTable';
import { RequestStepper } from './RequestStepper';
import { SaddMembersTable } from './SaddMembersTable';
import styles from './styles.module.scss';

const Request: FC<RequestProps> = ({
  isClosingRequestPending,
  closeWithRequest,
  refetch,
  cabinetId,
  additionalParams,
  type,
  isCreation,
  permissions,
  viewOnly,
}) => {
  const { isDisabled } = WorkGroupRequestStore.hooks.useRequestPermissions(
    permissions,
    type,
    viewOnly,
  );
  const request = useAppSelector(
    WorkGroupRequestStore.selectors.requestDataSelector,
  );

  const [popup, handlePopup] = usePopupsToggle(
    WorkGroupRequestConfig.popupsInitial,
  );
  WorkGroupRequestStore.hooks.useGetData(
    type,
    isCreation,
    cabinetId,
    additionalParams,
  );

  const { ms, handleRenewTimer } = WorkGroupRequestStore.hooks.useTimerLogic({
    isCreation,
    type,
    viewOnly,
    closeWithRequest,
    additionalParams,
    handlePopup,
  });

  const submitParams: SubmitParams = {
    request,
    isCreation,
    type,
    refetch,
    handleClose: () =>
      closeWithRequest(WorkGroupRequestConfig.constants.CANCEL_CLOSE_REASON),
    cabinetId,
    additionalParams,
  };

  const handleSubmit = WorkGroupRequestStore.hooks.useSubmitForm(submitParams);

  const buttons = WorkGroupRequestStore.hooks.useButtons({
    submitParams,
    handleSubmit,
    handlePopup,
    isPending: request.isPending || isClosingRequestPending,
    isDisabled,
    isPrepared: request.isPrepared,
  });

  return (
    <>
      <CreationHeader isCreation={isCreation} type={type} />
      <EditCountDown ms={ms} />
      <BlockedUserName additionalParams={additionalParams} />
      <PreparedStatusSwitcher isDisabled={isDisabled} />

      <RequestStepper
        type={type}
        isCreation={isCreation}
        isDisabled={isDisabled}
        handlePopup={handlePopup}
        cabinetId={cabinetId}
        additionalParams={additionalParams}
        request={request}
      />

      <Divider />

      <ButtonsContainer buttons={buttons} disableAnimation />

      <RequestBindTable
        isOpened={popup.requestBind}
        handleClose={() => handlePopup('requestBind')}
        cabinetId={cabinetId}
      />

      <SaddMembersTable
        isOpened={popup.saddTable}
        handleClose={() => handlePopup('saddTable')}
      />

      <CustomMessageModalWithOutClose
        isOpened={popup.countDownMessage}
        handleClose={() => handlePopup('countDownMessage')}
        message="Достигнут лимит времени редактирования заявки/уведомления"
        buttons={[
          {
            title: 'Продолжить',
            key: 'renew',
            onClick: handleRenewTimer,
          },
        ]}
      />
    </>
  );
};

export const WorkGroupRequest: FC<WorkGroupRequestProps> = ({
  handleClose,
  title,
  type,
  isOpened,
  additionalParams,
  isCreation,
  viewOnly,
  ...props
}) => {
  const request = useAppSelector(
    WorkGroupRequestStore.selectors.requestDataSelector,
  );

  const closeWithRequest = WorkGroupRequestStore.hooks.useCloseWithRequest(
    additionalParams?.id || '',
    handleClose,
    type,
    !isCreation,
    viewOnly,
  );

  const isRowTypeNotice =
    // only request.isNotice works, but with delay
    type === 'request' && (request.isNotice || additionalParams?.type === '2');

  return (
    <AppPopup
      duration={
        WorkGroupRequestConfig.constants.CLOSING_ANIMATION_DELAY_MS / 1000 // Convert to sec for framer-motion
      }
      isOpened={isOpened}
      isCloseButtonDisabled={closeWithRequest.isClosingRequestPending}
      onClose={() =>
        closeWithRequest.onClose(
          WorkGroupRequestConfig.constants.CANCEL_CLOSE_REASON,
        )
      }
      shouldCloseOnBackdropClick={false}
      className={styles.popup}
      title={`${title} ${
        isRowTypeNotice
          ? 'уведомление'
          : { internal: 'переписку', request: 'заявку' }[type]
      }`}
    >
      <Request
        isClosingRequestPending={closeWithRequest.isClosingRequestPending}
        viewOnly={viewOnly}
        isCreation={isCreation}
        closeWithRequest={closeWithRequest.onClose}
        type={type}
        additionalParams={additionalParams}
        {...props}
      />
    </AppPopup>
  );
};
