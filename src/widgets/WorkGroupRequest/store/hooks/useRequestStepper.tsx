import { useMemo } from 'react';
import { WorkGroupRequestLib, Request } from 'widgets/WorkGroupRequest';

interface UseRequestStepperParams {
  request: Request;
  type: 'request' | 'internal';
}

interface UseRequestStepperReturn {
  employeeCounter: number;
  filesCount: number;
  formDataValidation: {
    hasError: boolean;
    errorMessage?: string;
  };
  formValidationCounter: number;
  mainDataValidation: {
    hasError: boolean;
    errorMessage?: string;
  };
}

export const useRequestStepper = ({
  request,
  type,
}: UseRequestStepperParams): UseRequestStepperReturn => {
  const employeeCounter = useMemo(
    () =>
      request.employeeData.employeeVisa.length +
      request.employeeData.employeeSign.length,
    [request.employeeData],
  );

  const formValidationCounter = useMemo(
    () => request.formData.filter((item) => item.directoryId === '').length,
    [request.formData],
  );

  const mainDataValidation = useMemo(() => {
    const hasError = WorkGroupRequestLib.stepsValidation.isMainWarn(
      request.isNotice,
      type,
      request.otherDirectoryId,
    );
    return {
      hasError,
      errorMessage: hasError ? 'Не выбрана директория' : undefined,
    };
  }, [request.isNotice, type, request.otherDirectoryId]);

  const formDataValidation = useMemo(() => {
    const hasError = WorkGroupRequestLib.stepsValidation.isFormWarn(
      request.isNotice,
      type,
      request.formData,
    );
    return {
      hasError,
      errorMessage: hasError
        ? `Не ${
            formValidationCounter === 1
              ? 'выбрана директория'
              : 'выбраны директории'
          }`
        : undefined,
    };
  }, [request.isNotice, type, request.formData, formValidationCounter]);

  const filesCount = useMemo(
    () => request.fileList.length + request.filesCount,
    [request.fileList.length, request.filesCount],
  );

  return {
    employeeCounter,
    formValidationCounter,
    mainDataValidation,
    formDataValidation,
    filesCount,
  };
};
