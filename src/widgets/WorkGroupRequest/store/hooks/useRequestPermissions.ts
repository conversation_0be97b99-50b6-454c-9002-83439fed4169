import { useMemo } from 'react';
import type { WorkGroupRequestProps } from 'widgets/WorkGroupRequest';
import { permissionsStore } from 'entities/Permissions';

interface UsePermissionsReturn {
  canEdit: {
    internal: boolean;
    request: boolean;
  };
  isDisabled: boolean;
}

export const useRequestPermissions = (
  permissions: WorkGroupRequestProps['permissions'],
  type: 'request' | 'internal',
  viewOnly?: boolean,
): UsePermissionsReturn => {
  const canEdit = useMemo(
    () => ({
      request:
        permissions.ap_RequestNoticeWG.includes(
          permissionsStore.enums.Actions.EDIT_PROFILE_AT,
        ) && type === 'request',
      internal:
        permissions.ap_InternalWGO.includes(
          permissionsStore.enums.Actions.EDIT_PROFILE_AT,
        ) && type === 'internal',
    }),
    [permissions, type],
  );

  const isDisabled = useMemo(
    () => !canEdit[type] || Boolean(viewOnly),
    [canEdit, type, viewOnly],
  );

  return { canEdit, isDisabled };
};
