import { notification, TreeProps } from 'antd';
import { RcFile } from 'antd/es/upload';
import { useCallback } from 'react';
import type { Files } from 'widgets/WorkGroupRequest';
import { createBasicClosableNotice } from 'shared/model';

interface UseFileUploadProps {
  bindedFileNames: string[];
  fileList: Files[];
  handleFileTreeSelect: (file: Files) => void;
  handleUpdateFileList: (files: Files[]) => void;
  isPrepared: boolean;
}

interface UseFileUploadReturn {
  checkDuplicateFiles: (fileName: string) => boolean;
  filterDuplicateFiles: (files: Files[]) => {
    duplicates: string[];
    validFiles: Files[];
  };
  handleBeforeUpload: (file: RcFile, files: RcFile[]) => boolean;
  handleRemove: (file: Files) => void;
  handleTreeFileSelect: TreeProps<TreeElement>['onSelect'];
}

export const useFileUpload = ({
  bindedFileNames,
  fileList,
  handleFileTreeSelect,
  handleUpdateFileList,
  isPrepared,
}: UseFileUploadProps): UseFileUploadReturn => {
  const checkDuplicateFiles = useCallback(
    (fileName: string): boolean =>
      fileList.some((file) => file.name === fileName) ||
      bindedFileNames.includes(fileName),
    [fileList, bindedFileNames],
  );

  const filterDuplicateFiles = useCallback(
    (files: Files[]) => {
      const duplicates: string[] = [];
      const validFiles: Files[] = [];

      files.forEach((file) => {
        if (checkDuplicateFiles(file.name)) {
          duplicates.push(file.name);
        } else {
          validFiles.push(file);
        }
      });

      return { duplicates, validFiles };
    },
    [checkDuplicateFiles],
  );

  const handleBeforeUpload = useCallback(
    (file: RcFile, files: RcFile[]): boolean => {
      const isFirstFile = files[0].name === file.name;
      if (!isFirstFile) return false;

      const { duplicates, validFiles } = filterDuplicateFiles(files as Files[]);

      if (duplicates.length > 0) {
        createBasicClosableNotice({
          message: 'Невозможно прикрепить файлы с одинаковыми именами',
          description: `Следующие файлы уже есть в списке или привязаны к заявке: ${duplicates.join(
            ', ',
          )}`,
        });
      }

      if (validFiles.length > 0) {
        handleUpdateFileList([...fileList, ...validFiles]);
      }

      return false;
    },
    [fileList, filterDuplicateFiles, handleUpdateFileList],
  );

  const handleRemove = useCallback(
    (file: Files): void => {
      if (isPrepared) return;

      const newFileList = fileList.filter((f) => f !== file);
      handleUpdateFileList(newFileList);
    },
    [fileList, isPrepared, handleUpdateFileList],
  );

  const handleTreeFileSelect: TreeProps<TreeElement>['onSelect'] = useCallback(
    (_keys, info) => {
      const isDoubleClick = info.nativeEvent.detail === 2;
      const isFile = !info.node.isDirectory;

      if (!isDoubleClick || !isFile || isPrepared) return;

      const isDuplicate =
        fileList.some(
          (file) =>
            file.uid === info.node.itemId || file.name === info.node.title,
        ) || checkDuplicateFiles(info.node.title);

      if (isDuplicate) {
        notification.warn({
          message:
            'Файл с таким именем уже есть в списке или привязан к заявке',
        });
      } else {
        handleFileTreeSelect({
          name: info.node.title,
          uid: info.node.itemId,
          editable: true,
        } as Files);
      }
    },
    [fileList, isPrepared, checkDuplicateFiles, handleFileTreeSelect],
  );

  return {
    checkDuplicateFiles,
    filterDuplicateFiles,
    handleBeforeUpload,
    handleRemove,
    handleTreeFileSelect,
  };
};
