import { useCallback } from 'react';
import {
  WorkGroupRequestStore,
  WorkGroupRequestConfig,
} from 'widgets/WorkGroupRequest';
import { TableRowData } from 'features/DataGrid';
import { apiUrls, appInstance } from 'shared/api';
import { appErrorNotification, generateUrlWithQueryParams } from 'shared/lib';

interface UseTimerLogicParams {
  closeWithRequest: (reason: string) => void;
  handlePopup: (
    name: 'saddTable' | 'requestBind' | 'countDownMessage',
    status?: boolean,
  ) => void;
  isCreation: boolean;
  type: 'request' | 'internal';
  additionalParams?: Partial<TableRowData>;
  viewOnly?: boolean;
}

interface UseTimerLogicReturn {
  handleRenewTimer: () => Promise<void>;
  initialValue: number;
  ms: number;
  renewTimer: (duration: number) => void;
}

export const useTimerLogic = ({
  isCreation,
  type,
  viewOnly,
  closeWithRequest,
  additionalParams,
  handlePopup,
}: UseTimerLogicParams): UseTimerLogicReturn => {
  const [ms, initialValue, renewTimer] =
    WorkGroupRequestStore.hooks.useCountDown(
      !isCreation && type === 'request' && Boolean(!viewOnly),
      () =>
        closeWithRequest(WorkGroupRequestConfig.constants.TIMEOUT_CLOSE_REASON),
      additionalParams,
    );

  WorkGroupRequestStore.hooks.useTimerRenewal(
    initialValue,
    ms,
    handlePopup,
    !isCreation && type === 'request',
  );

  const handleRenewTimer = useCallback(async (): Promise<void> => {
    try {
      await appInstance.post(
        generateUrlWithQueryParams(apiUrls.workGroup.request.renewTimer, {
          requestNoticeId: additionalParams?.id,
        }),
      );
      renewTimer(Number(additionalParams?.duration));
      handlePopup('countDownMessage');
    } catch (e) {
      appErrorNotification(
        'Ошибка продления времени редактирования',
        e as AppError,
      );
    }
  }, [additionalParams, renewTimer, handlePopup]);

  return {
    ms,
    initialValue,
    renewTimer,
    handleRenewTimer,
  };
};
