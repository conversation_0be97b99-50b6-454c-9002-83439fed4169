import axios from 'axios';
import { useEffect, useMemo, useState } from 'react';
import {
  WorkGroupRequestConfig,
  WorkGroupRequestStore,
} from 'widgets/WorkGroupRequest';
import { NestedTabsWithTable, TableRowData } from 'features/DataGrid';
import { apiUrls } from 'shared/api';
import { appErrorNotification, generateUrlWithQueryParams } from 'shared/lib';
import { useAxiosRequest, useCreateSliceActions } from 'shared/model';

interface UseFilesTableProps {
  additionalParams: Partial<TableRowData>;
}

interface UseFilesTableReturn {
  bindedFileNames: string[];
  filesState: ReturnType<typeof useAxiosRequest>[1];
  filesTable: TableColumnsAndRows;
}

export const useFilesTable = ({
  additionalParams,
}: UseFilesTableProps): UseFilesTableReturn => {
  const [filesTrigger, filesState] = useAxiosRequest<TableColumnsAndRows>();
  const [filesTable, setFilesTable] = useState<TableColumnsAndRows>();
  const { id: itemId, isRequest, nestedTable } = additionalParams || {};
  const nestedTabs = (nestedTable as NestedTabsWithTable)?.tabs;

  const { setFilesCount } = useCreateSliceActions(
    WorkGroupRequestStore.reducers.slice.actions,
  );

  const filesTableFromNested = useMemo(
    () =>
      nestedTabs?.find((tab) =>
        WorkGroupRequestConfig.constants.OUTPUT_FILES_ENDPOINTS.includes(
          tab.endpoint,
        ),
      )?.tableData,
    [nestedTabs],
  );

  const filesTableFromNestedRows = filesTableFromNested?.rows;
  const filesTableFromNestedColumns = filesTableFromNested?.columns?.filter(
    (data) => data?.dataIndex === 'name',
  );

  useEffect(() => {
    if (!itemId || !isRequest || filesTableFromNested?.rows) {
      if (filesTableFromNested?.rows) {
        setFilesCount(filesTableFromNested.rows.length);
      }
      return undefined;
    }

    const controller = new AbortController();

    const getFiles = async (): Promise<void> => {
      try {
        const res = await filesTrigger(
          generateUrlWithQueryParams(apiUrls.workGroup.request.outputFiles, {
            requestId: itemId,
            pageSize: 10,
            pageNumber: 1,
            light: true,
          }),
          { signal: controller.signal },
        );

        setFilesTable(res);
        setFilesCount(res.rows.length || 0);
      } catch (err) {
        if (axios.isAxiosError(err) && !controller.signal.aborted) {
          appErrorNotification(
            'Произошла ошибка загрузки таблицы исходящих файлов',
            err as AppError,
          );
        }
      }
    };

    getFiles();

    return () => {
      controller.abort();
    };
  }, [
    itemId,
    isRequest,
    filesTableFromNested?.rows,
    filesTrigger,
    setFilesCount,
  ]);

  const bindedFileNames = useMemo(
    () =>
      (filesTable?.rows || filesTableFromNestedRows || []).map(
        (row) => row.name as string,
      ) || [],
    [filesTable?.rows, filesTableFromNestedRows],
  );

  return {
    filesState,
    filesTable: filesTable || {
      columns: filesTableFromNestedColumns || [],
      rows: filesTableFromNestedRows || [],
    },
    bindedFileNames,
  };
};
