import { Button } from 'antd';
import type { FC, ReactNode } from 'react';
import styles from './styles.module.scss';

interface LoadMoreButtonProps {
  onClick: () => void;
  title: ReactNode;
  loading?: boolean;
}

export const LoadMoreButton: FC<LoadMoreButtonProps> = ({
  title,
  onClick,
  loading = false,
}) => (
  <Button
    loading={loading}
    className={styles.button}
    onClick={onClick}
    type="link"
    block
  >
    {title}
  </Button>
);
