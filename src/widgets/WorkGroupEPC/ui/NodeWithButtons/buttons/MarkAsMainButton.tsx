import { PushpinOutlined } from '@ant-design/icons';
import { Button, Tooltip, notification } from 'antd';
import { memo, useCallback } from 'react';
import { appInstance, apiUrls } from 'shared/api';
import { appErrorNotification } from 'shared/lib';
import { createConfirmModal } from 'shared/model';

interface MarkAsMainButtonProps {
  node: TreeElement;
  refetchNode: (
    itemId: string,
    forcePaginationEnabled?: boolean,
  ) => Promise<void>;
}

export const MarkAsMainButton = memo(
  ({ node, refetchNode }: MarkAsMainButtonProps) => {
    const handleMarkAsMain = useCallback(
      (e: React.MouseEvent<HTMLButtonElement>) => {
        e.stopPropagation();
        createConfirmModal({
          title: 'Отметить основным',
          shouldCloseOnBackdropClick: false,
          closeOnEscape: false,
          message: `Отметить основным файл "${node.title}"?`,
          onConfirm: async () => {
            if (node.fileNetId) {
              try {
                await appInstance.get(
                  apiUrls.workGroup.EPC.markMainFile(node.fileNetId),
                );
                notification.success({
                  message: `Файл ${node.title} успешно отмечен основным`,
                });
                refetchNode(node.parent!, true);
              } catch (error) {
                appErrorNotification(
                  `Произошла ошибка при попытке отметить "${node.title}" основным`,
                  error as AppError,
                );
              }
            }
          },
        });
      },
      [node, refetchNode],
    );

    return (
      <Tooltip title="Отметить основным">
        <Button
          type="dashed"
          size="small"
          icon={<PushpinOutlined />}
          onClick={handleMarkAsMain}
        />
      </Tooltip>
    );
  },
);
