import { useMemo } from 'react';
import { EpcPermissions } from 'widgets/WorkGroupEPC/types';
import { PermissionsInitial, permissionsStore } from 'entities/Permissions';

export const useEPCPermissions = ({
  isFullFilesAccess,
  permissions,
}: {
  isFullFilesAccess: boolean;
  permissions: PermissionsInitial;
}): EpcPermissions => {
  const epcPermissions: EpcPermissions = useMemo(
    () => ({
      canViewFiles: permissions.ap_FilesEPC.includes(
        permissionsStore.enums.Actions.VIEW_FILES_AT,
      ),
      canDownloadFiles: permissions.ap_FilesEPC.includes(
        permissionsStore.enums.Actions.DOWNLOAD_FILES_AT,
      ),
      canEdit: permissions.ap_FilesEPC.includes(
        permissionsStore.enums.Actions.EDIT_LIST_AT,
      ),
      canDeleteFiles: permissions.ap_FilesWGO.includes(
        // APPID-2131
        permissionsStore.enums.Actions.DELETE_FILES_AT,
      ),
      canEditEPCStructure: permissions.ap_StructureEPC.includes(
        permissionsStore.enums.Actions.EDIT_LIST_AT,
      ),
      canCopyFiles:
        isFullFilesAccess &&
        permissions.ap_FilesEPC.includes(
          permissionsStore.enums.Actions.EDIT_LIST_AT,
        ),
    }),
    [
      isFullFilesAccess,
      permissions.ap_FilesEPC,
      permissions.ap_FilesWGO,
      permissions.ap_StructureEPC,
    ],
  );

  return epcPermissions;
};
