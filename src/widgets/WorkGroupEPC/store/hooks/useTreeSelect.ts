import { TreeProps } from 'antd';
import { useCallback } from 'react';

type UseTreeSelect = TreeProps<TreeElement>['onSelect'];

export const useTreeSelect = (
  onSelect: TreeProps<TreeElement>['onSelect'],
): UseTreeSelect => {
  const handleSelect: TreeProps<TreeElement>['onSelect'] = useCallback(
    (keys, info) => {
      if (!info.node.isDirectory) {
        onSelect?.([], info);
      } else {
        onSelect?.(keys, info);
      }
    },
    [onSelect], // eslint-disable-line
  );

  return handleSelect;
};
