import { AxiosResponse } from 'axios';
import { treeParserByConfig } from 'shared/lib';
import { treeParserConfig } from './treeParserConfig';

export const transformResponse = (
  response: AxiosResponse<{
    treeData: TreeElement[];
    pagination?: { pageNumber: number; pageSize: number; total: number };
    parentInfo?: { totalCountOfLeafs: number };
  }>,
  parentId?: string,
): TreeElement[] => {
  const nodes =
    treeParserByConfig(response.data.treeData, treeParserConfig) || [];

  // Если есть информация о пагинации, сохраняем её для использования в processTreeData
  if (response.data.pagination && parentId) {
    // Добавляем информацию о пагинации к первому узлу для передачи в processTreeData
    // Это временное решение - информация будет перенесена к родительскому узлу
    if (nodes.length > 0) {
      const { total, pageNumber, pageSize } = response.data.pagination;
      nodes[0].total = total;
      nodes[0].page = pageNumber;
      nodes[0].size = pageSize;

      // Если бэкенд передает totalCountOfLeafs для родителя
      if (response.data.parentInfo?.totalCountOfLeafs !== undefined) {
        nodes[0].parentTotalCountOfLeafs =
          response.data.parentInfo.totalCountOfLeafs;
      }
    }
  }

  return nodes;
};
