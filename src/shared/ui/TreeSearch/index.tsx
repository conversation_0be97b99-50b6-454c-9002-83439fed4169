import { ArrowDownOutlined, ArrowUpOutlined } from '@ant-design/icons';
import { Button, Tooltip, Typography } from 'antd';
import styles from './styles.module.scss';

interface TreeSearchProps {
  togglePopup: Callback;
  treeSearchConfig: {
    arrowDown: Callback;
    arrowUp: Callback;
    currentIndex: number;
    taggedNodes: TreeElement[];
  };
  isLoading?: boolean;
}

export const TreeSearch = ({
  togglePopup,
  treeSearchConfig,
  isLoading = false,
}: TreeSearchProps): JSX.Element => (
  <div className={styles.search}>
    <Tooltip title={isLoading ? 'Дождитесь окончания загрузки каталогов' : ''}>
      <Button
        className={styles.button}
        onClick={togglePopup}
        disabled={isLoading}
      >
        Поиск
      </Button>
    </Tooltip>
    {treeSearchConfig.taggedNodes.length !== 0 && (
      <div className={styles.searchSwitch}>
        <Typography.Text>{`${treeSearchConfig.currentIndex}/${treeSearchConfig.taggedNodes.length}`}</Typography.Text>
        <Button
          size="small"
          onClick={treeSearchConfig.arrowDown}
          icon={<ArrowDownOutlined />}
        />
        <Button
          size="small"
          onClick={treeSearchConfig.arrowUp}
          icon={<ArrowUpOutlined />}
        />
      </div>
    )}
  </div>
);
