export const DEFAULT_DATE_VARIANT = 'DD.MM.YYYY';
export const DEFAULT_TIME_VARIANT = 'HH:mm';
export const DEFAULT_DATE_TIME_VARIANT = 'DD.MM.YYYY HH:mm';
export const REPLICATOR_DATE_VARIANT = 'YYYYMM';
export const DATE_VARIANT_FOR_FILTERS = 'YYYY-MM-DD';

/**
 * Требуется для авто конвертации в DEFAULT_DATE_TIME_VARIANT При вводе с
 * клавиатуры в RangePicker, если формат есть в массиве, он будет преобразован в
 * первый формат из массива
 */
export const DATE_VARIANTS_LIST = [
  DEFAULT_DATE_VARIANT,
  'DD/MM/YY',
  'DD-MM-YYYY',
  'DD-MM-YY',
  'DDMMYYYY',
  'DDMMYY',
];
