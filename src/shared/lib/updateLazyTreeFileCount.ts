export function updateLazyTreeFileCount(
  nodeArray: TreeElement[],
  loadedKeys: Key[],
): TreeElement[] {
  function calculateTotalCount(node: TreeElement): number {
    if (node.isDirectory) {
      // Если дети не загружены, используем totalCountOfLeafs
      if (node?.children?.length === 0 && !loadedKeys.includes(node.key)) {
        return node?.totalCountOfLeafs || 0;
      }

      // Подсчет количества файлов в директории, если дети загружены
      const totalCount = node?.children?.reduce(
        (sum, child) => sum + calculateTotalCount(child),
        0,
      );
      node.totalCountOfLeafs = totalCount || 0;
      node.isLeaf = !node.children?.length;
      return totalCount || 0;
    }
    return 1;
  }

  nodeArray.forEach((node) => calculateTotalCount(node));

  return nodeArray;
}
